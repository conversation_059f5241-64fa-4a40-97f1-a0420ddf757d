<?php
// Debug why the term display loses the asterisk
require_once 'bootstrap.php';

use App\Services\DosarService;

// Simulate the exact POST request
$_POST['bulkSearchTerms'] = "14096/3/2024*";

echo "<h1>🔍 Debug Term Display Issue</h1>";

// Include the exact functions from index.php
function parseBulkSearchTerms($input) {
    $input = str_replace(',', "\n", $input);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        return 'numarDosar';
    }
    
    return 'numeParte';
}

function performBulkSearchWithFilters($searchTermsData, $filters) {
    $dosarService = new DosarService();
    $results = [];

    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];

        try {
            $searchParams = [
                'numarDosar' => ($searchType === 'numarDosar') ? $term : '',
                'institutie' => $filters['institutie'] ?? null,
                'numeParte' => ($searchType === 'numeParte') ? $term : '',
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => '',
                'categorieCaz' => ''
            ];

            $termResults = $dosarService->cautareAvansata($searchParams);

            foreach ($termResults as $dosar) {
                $dosar->searchTerm = $term;
                $dosar->searchType = $searchType;
            }

            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => $termResults,
                'count' => count($termResults),
                'error' => null
            ];

        } catch (Exception $e) {
            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => [],
                'count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    return $results;
}

function generateResultMessage($count, $term) {
    if ($count === 0) {
        return "Nu au fost găsite rezultate pentru termenul '{$term}'";
    } elseif ($count === 1) {
        return "1 rezultat găsit pentru termenul '{$term}'";
    } else {
        return "{$count} rezultate găsite pentru termenul '{$term}'";
    }
}

try {
    $bulkSearchTerms = $_POST['bulkSearchTerms'] ?? '';
    
    if (!empty($bulkSearchTerms)) {
        echo "<h2>Step 1: Parse Search Terms</h2>";
        $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
        
        echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
        echo "<strong>Parsed search terms:</strong><br>";
        foreach ($searchTermsData as $index => $termData) {
            echo "Term #{$index}: '" . htmlspecialchars($termData['term']) . "' (type: {$termData['type']})<br>";
        }
        echo "</div>";
        
        echo "<h2>Step 2: Perform Search</h2>";
        $searchResults = performBulkSearchWithFilters($searchTermsData, []);
        
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
        echo "<strong>Search results:</strong><br>";
        foreach ($searchResults as $index => $result) {
            echo "Result #{$index}: term='" . htmlspecialchars($result['term']) . "', count={$result['count']}<br>";
        }
        echo "</div>";
        
        echo "<h2>Step 3: Simulate HTML Generation</h2>";
        
        foreach ($searchResults as $index => $result) {
            echo "<h3>Processing result #{$index}</h3>";
            
            echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
            echo "<strong>Original result data:</strong><br>";
            echo "result['term'] = '" . htmlspecialchars($result['term']) . "'<br>";
            echo "result['type'] = '" . htmlspecialchars($result['type']) . "'<br>";
            echo "result['count'] = " . $result['count'] . "<br>";
            echo "</div>";
            
            // Simulate the exact HTML generation from index.php
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
            echo "<strong>HTML that would be generated:</strong><br>";
            
            // Title with term
            echo "<h6>Search term in title: " . htmlspecialchars($result['term']) . "</h6>";
            
            // Result message
            $resultMessage = generateResultMessage($result['count'], $result['term']);
            echo "<span id='resultMessage{$index}' data-term='" . htmlspecialchars($result['term']) . "' data-original-count='{$result['count']}'>";
            echo $resultMessage;
            echo "</span><br>";
            
            echo "<strong>Message generated:</strong> " . htmlspecialchars($resultMessage) . "<br>";
            echo "</div>";
            
            // Check if there's any issue with the term
            if (strpos($result['term'], '*') !== false) {
                echo "<div style='background: #d4edda; padding: 8px; margin: 3px 0; border: 1px solid #c3e6cb;'>";
                echo "<strong>✅ Asterisk preserved in result term</strong>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 8px; margin: 3px 0; border: 1px solid #f5c6cb;'>";
                echo "<strong>❌ Asterisk lost in result term</strong>";
                echo "</div>";
            }
        }
        
        echo "<h2>Step 4: Check for Term Modification</h2>";
        
        // Check if there's any code that might modify the term
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
        echo "<strong>Potential issues to check:</strong><br>";
        echo "1. Is the term being modified somewhere in the search process?<br>";
        echo "2. Is there any URL encoding/decoding that removes the asterisk?<br>";
        echo "3. Is there any JavaScript that modifies the displayed term?<br>";
        echo "4. Is there any HTML escaping that affects the asterisk?<br>";
        echo "</div>";
        
        echo "<h2>Step 5: Check Actual Web Interface</h2>";
        
        echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffc107;'>";
        echo "<h3>🔍 Investigation Steps:</h3>";
        echo "<ol>";
        echo "<li><strong>Check the actual web interface:</strong> Search for '14096/3/2024*'</li>";
        echo "<li><strong>View page source:</strong> Look for the result message HTML</li>";
        echo "<li><strong>Check data-term attribute:</strong> Should contain '14096/3/2024*'</li>";
        echo "<li><strong>Check JavaScript console:</strong> Look for any term modifications</li>";
        echo "<li><strong>Check network tab:</strong> See what term is actually sent in the POST request</li>";
        echo "</ol>";
        echo "</div>";
        
        // Test the exact scenario that's happening
        echo "<h2>Step 6: Test Exact Scenario</h2>";
        
        // What if the term is being modified by some other process?
        $testTerm = "14096/3/2024*";
        echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
        echo "<strong>Testing term modifications:</strong><br>";
        
        // Test various operations that might affect the term
        echo "Original: '" . htmlspecialchars($testTerm) . "'<br>";
        echo "htmlspecialchars: '" . htmlspecialchars($testTerm) . "'<br>";
        echo "urlencode: '" . urlencode($testTerm) . "'<br>";
        echo "urldecode: '" . urldecode($testTerm) . "'<br>";
        echo "trim: '" . trim($testTerm) . "'<br>";
        echo "trim with chars: '" . trim($testTerm, '"\'') . "'<br>";
        
        // Test regex that might capture without asterisk
        if (preg_match('/^(\d+\/\d+(?:\/\d+)?)/', $testTerm, $matches)) {
            echo "Regex capture (without asterisk): '" . $matches[1] . "'<br>";
            echo "<strong style='color: red;'>⚠️ This could be the issue!</strong><br>";
        }
        echo "</div>";
        
    } else {
        echo "<p>No search terms provided.</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🎯 Next Steps</h2>";
echo "<p>If the backend processing preserves the asterisk correctly, then check:</p>";
echo "<ol>";
echo "<li>The actual web interface to see what's displayed</li>";
echo "<li>Browser developer tools to see the actual HTML generated</li>";
echo "<li>JavaScript console for any term modifications</li>";
echo "<li>Network tab to see the actual POST data sent</li>";
echo "</ol>";
?>
