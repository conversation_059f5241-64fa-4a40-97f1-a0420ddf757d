<?php
// Test frontend wildcard display discrepancy
require_once 'bootstrap.php';
require_once 'includes/functions.php';
require_once 'bulk_search_functions.php';

use App\Services\DosarService;

// Copy necessary functions from index.php
function parseBulkSearchTerms($input) {
    $input = str_replace(',', "\n", $input);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');

    if (preg_match('/^\d+\/\d+(?:\/\d+)?[\*]?$/', $cleanTerm)) {
        return 'numarDosar';
    }

    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)[\*]?$/i', $cleanTerm)) {
        return 'numarDosar';
    }

    return 'numeParte';
}

echo "<h1>Frontend Wildcard Display Investigation</h1>";

$searchTerm = "14096/3/2024*";
echo "<h2>Testing search term: '$searchTerm'</h2>";

try {
    // 1. Test direct DosarService call (backend)
    echo "<h3>1. Direct Backend Test (DosarService)</h3>";
    $dosarService = new DosarService();
    $backendResults = $dosarService->cautareAvansata(['numarDosar' => $searchTerm]);
    
    echo "<p><strong>Backend results count: " . count($backendResults) . "</strong></p>";
    
    if (!empty($backendResults)) {
        foreach ($backendResults as $index => $dosar) {
            echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
            echo "<strong>Backend Result #" . ($index + 1) . ":</strong><br>";
            echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
            echo "Institution: " . ($dosar->instanta ?? 'N/A') . "<br>";
            echo "Object: " . substr($dosar->obiect ?? 'N/A', 0, 100) . "...<br>";
            echo "Date: " . ($dosar->data ?? 'N/A') . "<br>";
            echo "</div>";
        }
    }
    
    // 2. Test search term parsing
    echo "<h3>2. Search Term Parsing Test</h3>";

    $searchTermsData = parseBulkSearchTerms($searchTerm);
    echo "<p>Parsed search terms:</p>";
    echo "<pre>" . print_r($searchTermsData, true) . "</pre>";

    // Skip the full frontend test for now to focus on the core issue
    
    // 3. Focus on literal asterisk case analysis
    echo "<h3>3. Literal Asterisk Case Analysis</h3>";

    $literalAsteriskFound = false;
    foreach ($backendResults as $dosar) {
        if (strpos($dosar->numar, '*') !== false) {
            $literalAsteriskFound = true;
            echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
            echo "<strong>Found case with literal asterisk in backend:</strong><br>";
            echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
            echo "Institution: " . ($dosar->instanta ?? 'N/A') . "<br>";
            echo "Object: " . ($dosar->obiect ?? 'N/A') . "<br>";
            echo "Date: " . ($dosar->data ?? 'N/A') . "<br>";
            echo "</div>";
            break;
        }
    }

    if (!$literalAsteriskFound) {
        echo "<p style='color: orange;'>No case with literal asterisk found in backend results</p>";
    } else {
        echo "<p style='color: green;'><strong>✓ Backend correctly returns the case with literal asterisk</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr><h2>Conclusion</h2>";
echo "<p>Backend correctly returns 3 results including the literal asterisk case.</p>";
echo "<p>The issue must be in the frontend display or filtering logic.</p>";
