<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tomorrow Button</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Tomorrow Button Functionality</h2>
        
        <div class="form-group">
            <label for="dataSedinta">Data Ședinței *</label>
            <div class="input-group">
                <input type="text"
                       class="form-control"
                       id="dataSedinta"
                       name="dataSedinta"
                       placeholder="ZZ.LL.AAAA (ex: 15.03.2024)"
                       required
                       pattern="^(\d{1,2})\.(\d{1,2})\.(\d{4})$"
                       title="Formatul datei trebuie să fie ZZ.LL.AAAA">
                <div class="input-group-append">
                    <button type="button" class="btn btn-outline-secondary" id="todayBtn" title="Setează data de azi">
                        <i class="fas fa-calendar-day"></i>
                        Azi
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="tomorrowBtn" title="Setează data de mâine">
                        <i class="fas fa-calendar-plus"></i>
                        Mâine
                    </button>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info">
            <strong>Test Instructions:</strong>
            <ul>
                <li>Click "Azi" button to set today's date</li>
                <li>Click "Mâine" button to set tomorrow's date</li>
                <li>Verify the date format is DD.MM.YYYY</li>
            </ul>
        </div>
        
        <div id="testResults" class="mt-3"></div>
    </div>

    <script>
        /**
         * Formatează o dată în format românesc DD.MM.YYYY
         */
        function formatDateToRomanian(date) {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}.${month}.${year}`;
        }

        /**
         * Validare simplă pentru test
         */
        function validateDateInput(input) {
            const pattern = /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/;
            const isValid = pattern.test(input.value);
            
            const resultsDiv = document.getElementById('testResults');
            if (isValid) {
                resultsDiv.innerHTML = `<div class="alert alert-success">✅ Date format valid: ${input.value}</div>`;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">❌ Date format invalid: ${input.value}</div>`;
            }
        }

        /**
         * Inițializează date picker pentru test
         */
        function initDatePicker() {
            const todayBtn = document.getElementById('todayBtn');
            const tomorrowBtn = document.getElementById('tomorrowBtn');
            const dateInput = document.getElementById('dataSedinta');

            if (todayBtn && dateInput) {
                todayBtn.addEventListener('click', function() {
                    dateInput.value = formatDateToRomanian(new Date());
                    validateDateInput(dateInput);
                });
            }

            if (tomorrowBtn && dateInput) {
                tomorrowBtn.addEventListener('click', function() {
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    dateInput.value = formatDateToRomanian(tomorrow);
                    validateDateInput(dateInput);
                });
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initDatePicker);
    </script>
</body>
</html>
