<?php
/**
 * Frontend Loading Mechanism Test
 * Tests the JavaScript loading overlay behavior
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test the exact same logic as detalii_dosar.php
$numarDosar = isset($_GET['numar']) ? trim($_GET['numar']) : (isset($_GET['numar_dosar']) ? trim($_GET['numar_dosar']) : '');
$institutie = isset($_GET['institutie']) ? trim($_GET['institutie']) : '';

// Default test case if no parameters
if (empty($numarDosar) || empty($institutie)) {
    $numarDosar = '130/98/2022';
    $institutie = 'TribunalulIALOMITA';
}

$dosar = null;
$error = null;

if (empty($numarDosar) || empty($institutie)) {
    $error = "Parametrii necesari pentru afișarea detaliilor dosarului lipsesc.";
} else {
    try {
        $dosarService = new DosarService();
        $dosare = $dosarService->cautareDupaNumarDosar($numarDosar, $institutie, '', '', '');
        
        if (!empty($dosare)) {
            $dosar = $dosare[0];
        } else {
            $error = "Nu s-au găsit dosare pentru criteriile specificate.";
        }
    } catch (Exception $e) {
        $error = "Eroare la obținerea detaliilor dosarului: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Loading Test - Romanian Judicial Portal</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #6f42c1, #5a32a3); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
        .section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
        .alert-danger { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .dosar-header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #007bff; }
        .test-controls { background: #e9ecef; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .debug-info { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; border-radius: 4px; }
        
        /* Loading overlay styles (copied from detalii_dosar.php) */
        .page-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(2px);
        }
        
        .page-loading-content {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .page-loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .page-loading-message {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 0.5rem;
        }
        
        .page-loading-submessage {
            font-size: 0.9rem;
            color: #6c757d;
            margin: 0;
        }
        
        .fade-out {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
        
        .main-content {
            opacity: 0;
            transition: opacity 0.5s ease-in;
        }
        
        .main-content.loaded {
            opacity: 1;
        }
    </style>
</head>
<body>

<!-- Loading overlay (same as detalii_dosar.php) -->
<div id="pageLoadingOverlay" class="page-loading-overlay" role="status" aria-live="polite" aria-label="Se încarcă detaliile dosarului">
    <div class="page-loading-content">
        <div class="page-loading-spinner" aria-hidden="true"></div>
        <p class="page-loading-message">Se încarcă detaliile dosarului...</p>
        <p class="page-loading-submessage">Vă rugăm să așteptați</p>
    </div>
</div>

<div class="container main-content" id="mainContent">
    <div class="header">
        <h1>🔧 Frontend Loading Mechanism Test</h1>
        <p>Testing JavaScript loading overlay behavior</p>
        <p><strong>Case:</strong> <?php echo htmlspecialchars($numarDosar); ?> from <?php echo htmlspecialchars($institutie); ?></p>
    </div>

    <div class="section">
        <h2>📊 Loading Test Results</h2>
        
        <div class="test-controls">
            <h4>🎮 Test Controls</h4>
            <button class="test-button" onclick="showLoadingOverlay()">Show Loading Overlay</button>
            <button class="test-button" onclick="hideLoadingOverlay()">Hide Loading Overlay</button>
            <button class="test-button" onclick="testLoadingMechanism()">Test Loading Mechanism</button>
            <button class="test-button" onclick="location.reload()">Reload Page</button>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            </div>
            <div class="alert alert-info">
                <strong>JavaScript Detection:</strong> This error will be detected by <code>document.querySelector('.alert-danger')</code><br>
                <strong>Loading Overlay:</strong> Will hide because hasError = true
            </div>
        <?php elseif ($dosar): ?>
            <div class="dosar-header">
                <h3><?php echo htmlspecialchars($dosar->numar ?? $numarDosar); ?></h3>
                <p><strong>Instanță:</strong> <?php echo htmlspecialchars($institutie); ?></p>
                <p><strong>Părți:</strong> <?php echo count($dosar->parti ?? []); ?></p>
                <p><strong>Status:</strong> Dosar găsit și încărcat cu succes</p>
            </div>
            <div class="alert alert-success">
                <strong>JavaScript Detection:</strong> This content will be detected by <code>document.querySelector('.dosar-header')</code><br>
                <strong>Loading Overlay:</strong> Will hide because hasContent = true
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <strong>No Content State:</strong> Neither error nor dosar content<br>
                <strong>JavaScript Detection:</strong> hasError = false, hasContent = false<br>
                <strong>Loading Overlay:</strong> Will hide after 2-second timeout
            </div>
        <?php endif; ?>

        <div class="debug-info">
            <strong>Debug Information:</strong><br>
            URL Parameters: numar=<?php echo htmlspecialchars($numarDosar); ?>, institutie=<?php echo htmlspecialchars($institutie); ?><br>
            Dosar Object: <?php echo $dosar ? 'EXISTS' : 'NULL'; ?><br>
            Error Message: <?php echo $error ? htmlspecialchars($error) : 'NULL'; ?><br>
            .dosar-header Element: <?php echo $dosar ? 'WILL BE RENDERED' : 'WILL NOT BE RENDERED'; ?><br>
            .alert-danger Element: <?php echo $error ? 'WILL BE RENDERED' : 'WILL NOT BE RENDERED'; ?><br>
            Expected Loading Behavior: <?php 
                if ($error) {
                    echo 'Hide overlay (hasError = true)';
                } elseif ($dosar) {
                    echo 'Hide overlay (hasContent = true)';
                } else {
                    echo 'Hide overlay after 2s timeout';
                }
            ?>
        </div>
    </div>

    <div class="section">
        <h2>🔗 Test Different Cases</h2>
        <p>Test the loading mechanism with different cases:</p>
        <a href="?numar=130%2F98%2F2022&institutie=TribunalulIALOMITA" class="test-button">TribunalulIALOMITA (Fixed)</a>
        <a href="?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI" class="test-button">CurteadeApelBUCURESTI (Working)</a>
        <a href="?numar=999%2F99%2F9999&institutie=TribunalulIALOMITA" class="test-button">Non-existent Case</a>
        <a href="?numar=&institutie=" class="test-button">Invalid Parameters</a>
    </div>
</div>

<script>
// Copy the exact loading mechanism from detalii_dosar.php
function logDebug(message) {
    console.log('[DEBUG] ' + message);
}

function initPageLoadingOverlay() {
    const loadingOverlay = document.getElementById('pageLoadingOverlay');
    const mainContent = document.getElementById('mainContent');

    if (!loadingOverlay || !mainContent) {
        console.error('Elementele pentru loading overlay nu au fost găsite!');
        return;
    }

    // Funcție pentru ascunderea loading overlay-ului
    function hideLoadingOverlay() {
        mainContent.classList.add('loaded');
        loadingOverlay.classList.add('fade-out');
        
        setTimeout(() => {
            if (loadingOverlay.parentNode) {
                loadingOverlay.parentNode.removeChild(loadingOverlay);
            }
        }, 500);
        
        logDebug('Loading overlay ascuns cu succes.');
    }

    // Verificăm dacă pagina are erori sau conținut gol
    const hasError = document.querySelector('.alert-danger');
    const hasContent = document.querySelector('.dosar-header');

    logDebug('hasError: ' + (hasError ? 'true' : 'false'));
    logDebug('hasContent: ' + (hasContent ? 'true' : 'false'));

    const minDisplayTime = 1000;
    const startTime = Date.now();

    function hideWithMinTime() {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);
        setTimeout(hideLoadingOverlay, remainingTime);
    }

    if (hasError || hasContent) {
        logDebug('Ascundem loading-ul: ' + (hasError ? 'hasError' : 'hasContent'));
        hideWithMinTime();
    } else {
        logDebug('Fallback: ascundem după 2 secunde');
        setTimeout(hideLoadingOverlay, 2000);
    }

    logDebug('Loading overlay inițializat pentru detalii dosar.');
}

// Test functions
function showLoadingOverlay() {
    location.reload();
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('pageLoadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
    document.getElementById('mainContent').classList.add('loaded');
}

function testLoadingMechanism() {
    console.log('=== Testing Loading Mechanism ===');
    console.log('hasError:', !!document.querySelector('.alert-danger'));
    console.log('hasContent:', !!document.querySelector('.dosar-header'));
    console.log('Loading overlay exists:', !!document.getElementById('pageLoadingOverlay'));
    console.log('Main content exists:', !!document.getElementById('mainContent'));
    
    // Re-run the loading mechanism
    initPageLoadingOverlay();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    logDebug('DOM loaded, initializing loading overlay...');
    initPageLoadingOverlay();
});

// Immediate initialization check
(function() {
    function checkAndInitLoading() {
        const loadingOverlay = document.getElementById('pageLoadingOverlay');
        if (loadingOverlay) {
            logDebug('Loading overlay detectat și activ.');
        } else {
            setTimeout(checkAndInitLoading, 50);
        }
    }
    
    if (document.readyState === 'loading') {
        checkAndInitLoading();
    }
})();
</script>

</body>
</html>
