{% extends "base.twig" %}

{% block title %}Set<PERSON>ri <PERSON>fidenț<PERSON> - Portal Judiciar România{% endblock %}

{% block meta_description %}Gestionați setările de confidențialitate, consimțămintele GDPR și exercitați-vă drepturile asupra datelor personale în Portal Judiciar România.{% endblock %}

{% block additional_css %}
<style>
.privacy-dashboard {
    padding: 20px 0;
}

.privacy-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
    border-radius: 8px;
}

.privacy-section {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.privacy-section h3 {
    color: #28a745;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.consent-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.consent-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.consent-description {
    flex: 1;
    margin-right: 15px;
}

.consent-status {
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85em;
}

.consent-status.granted {
    background: #d4edda;
    color: #155724;
}

.consent-status.denied {
    background: #f8d7da;
    color: #721c24;
}

.data-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.data-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
}

.data-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: #28a745;
    margin-bottom: 5px;
}

.data-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.danger-zone {
    border: 2px solid #dc3545;
    border-radius: 8px;
    padding: 20px;
    background: #fff5f5;
}

.danger-zone h4 {
    color: #dc3545;
    margin-bottom: 15px;
}

.btn-danger-outline {
    border: 2px solid #dc3545;
    color: #dc3545;
    background: transparent;
}

.btn-danger-outline:hover {
    background: #dc3545;
    color: white;
}

@media (max-width: 768px) {
    .consent-toggle {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .consent-description {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .data-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .data-summary {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="privacy-dashboard">
    <!-- Privacy Header -->
    <div class="privacy-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-shield-alt"></i> Setări Confidențialitate</h1>
                    <p class="mb-0">Gestionați consimțămintele GDPR și exercitați-vă drepturile asupra datelor personale</p>
                </div>
                <div class="col-md-4 text-right">
                    <div class="user-info">
                        <i class="fas fa-user-shield"></i>
                        <span>{{ user_name }}</span>
                        <div class="mt-1">
                            <small class="text-light">{{ user_email }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- GDPR Consents Section -->
        <div class="privacy-section">
            <h3><i class="fas fa-check-circle"></i> Consimțăminte GDPR</h3>
            <p class="text-muted mb-4">Gestionați consimțămintele pentru prelucrarea datelor personale. Puteți retrage consimțământul oricând.</p>
            
            <div class="consent-item">
                <div class="consent-toggle">
                    <div class="consent-description">
                        <h5>Prelucrarea Datelor pentru Monitorizare</h5>
                        <p class="mb-0 text-muted">Permite prelucrarea datelor pentru funcționarea sistemului de monitorizare a dosarelor.</p>
                    </div>
                    <div>
                        <span class="consent-status {{ consents.data_processing ? 'granted' : 'denied' }}">
                            {{ consents.data_processing ? 'Acordat' : 'Refuzat' }}
                        </span>
                        <button class="btn btn-sm btn-outline-primary ml-2" 
                                onclick="toggleConsent('data_processing', {{ consents.data_processing ? 'false' : 'true' }})">
                            {{ consents.data_processing ? 'Retrage' : 'Acordă' }}
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="consent-item">
                <div class="consent-toggle">
                    <div class="consent-description">
                        <h5>Monitorizarea Dosarelor</h5>
                        <p class="mb-0 text-muted">Permite monitorizarea automată a dosarelor și detectarea modificărilor.</p>
                    </div>
                    <div>
                        <span class="consent-status {{ consents.monitoring ? 'granted' : 'denied' }}">
                            {{ consents.monitoring ? 'Acordat' : 'Refuzat' }}
                        </span>
                        <button class="btn btn-sm btn-outline-primary ml-2" 
                                onclick="toggleConsent('monitoring', {{ consents.monitoring ? 'false' : 'true' }})">
                            {{ consents.monitoring ? 'Retrage' : 'Acordă' }}
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="consent-item">
                <div class="consent-toggle">
                    <div class="consent-description">
                        <h5>Notificări prin Email</h5>
                        <p class="mb-0 text-muted">Permite trimiterea de notificări prin email pentru modificările dosarelor.</p>
                    </div>
                    <div>
                        <span class="consent-status {{ consents.email_notifications ? 'granted' : 'denied' }}">
                            {{ consents.email_notifications ? 'Acordat' : 'Refuzat' }}
                        </span>
                        <button class="btn btn-sm btn-outline-primary ml-2" 
                                onclick="toggleConsent('email_notifications', {{ consents.email_notifications ? 'false' : 'true' }})">
                            {{ consents.email_notifications ? 'Retrage' : 'Acordă' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Summary Section -->
        <div class="privacy-section">
            <h3><i class="fas fa-database"></i> Rezumatul Datelor</h3>
            <p class="text-muted mb-4">Vizualizați un rezumat al datelor personale stocate în sistem.</p>
            
            <div class="data-summary">
                <div class="data-item">
                    <div class="data-number">{{ privacy_report.monitored_cases_count }}</div>
                    <div class="data-label">Dosare Monitorizate</div>
                </div>
                <div class="data-item">
                    <div class="data-number">{{ privacy_report.notifications_sent }}</div>
                    <div class="data-label">Notificări Trimise</div>
                </div>
                <div class="data-item">
                    <div class="data-number">{{ privacy_report.data_processing_logs }}</div>
                    <div class="data-label">Înregistrări de Prelucrare</div>
                </div>
                <div class="data-item">
                    <div class="data-number">{{ privacy_report.account_age_days }}</div>
                    <div class="data-label">Zile de la Înregistrare</div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-success btn-block" onclick="exportData()">
                        <i class="fas fa-download"></i> Exportă Datele Mele
                    </button>
                    <small class="text-muted">Descărcați toate datele personale în format JSON</small>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-info btn-block" onclick="viewDataProcessingLog()">
                        <i class="fas fa-list"></i> Istoric Prelucrări
                    </button>
                    <small class="text-muted">Vizualizați istoricul prelucrărilor de date</small>
                </div>
            </div>
        </div>

        <!-- Danger Zone -->
        <div class="privacy-section">
            <div class="danger-zone">
                <h4><i class="fas fa-exclamation-triangle"></i> Zona Periculoasă</h4>
                <p class="mb-3">Acțiunile de mai jos sunt ireversibile. Vă rugăm să procedați cu atenție.</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-danger-outline btn-block" onclick="requestDataDeletion()">
                            <i class="fas fa-trash"></i> Șterge Toate Datele
                        </button>
                        <small class="text-muted">Șterge permanent toate datele personale</small>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-warning btn-block" onclick="deactivateAccount()">
                            <i class="fas fa-user-times"></i> Dezactivează Contul
                        </button>
                        <small class="text-muted">Dezactivează contul păstrând datele legale</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white;">
        <i class="fas fa-spinner fa-spin fa-3x"></i>
        <div style="margin-top: 20px; font-size: 18px;">Se procesează...</div>
    </div>
</div>
{% endblock %}

{% block additional_js %}
<script>
// CSRF tokens for different actions
const csrfTokens = {
    update_consent: '{{ csrf_tokens.update_consent }}',
    export_data: '{{ csrf_tokens.export_data }}',
    delete_data: '{{ csrf_tokens.delete_data }}',
    update_privacy_settings: '{{ csrf_tokens.update_privacy_settings }}'
};

// Rate limit information
const rateLimits = {
    export_data: {{ rate_limits.export_data|json_encode|raw }},
    delete_data: {{ rate_limits.delete_data|json_encode|raw }}
};

function toggleConsent(consentType, granted) {
    showLoading(true);
    
    const formData = new FormData();
    formData.append('action', 'update_consent');
    formData.append('consent_type', consentType);
    formData.append('granted', granted ? '1' : '0');
    formData.append('csrf_token', csrfTokens.update_consent);
    
    fetch('privacy.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        showNotification('Eroare de conexiune', 'danger');
    })
    .finally(() => {
        showLoading(false);
    });
}

function exportData() {
    if (rateLimits.export_data && rateLimits.export_data.remaining <= 0) {
        showNotification('Ați atins limita de exporturi. Încercați din nou mai târziu.', 'warning');
        return;
    }
    
    showLoading(true);
    
    const formData = new FormData();
    formData.append('action', 'export_data');
    formData.append('csrf_token', csrfTokens.export_data);
    
    fetch('privacy.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Create download link
            const link = document.createElement('a');
            link.href = data.download_url;
            link.download = data.download_url.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        showNotification('Eroare de conexiune', 'danger');
    })
    .finally(() => {
        showLoading(false);
    });
}

function requestDataDeletion() {
    if (!confirm('Sunteți sigur că doriți să ștergeți toate datele? Această acțiune este ireversibilă!')) {
        return;
    }
    
    const keepLegal = confirm('Doriți să păstrați datele necesare pentru obligațiile legale?');
    
    showLoading(true);
    
    const formData = new FormData();
    formData.append('action', 'delete_data');
    formData.append('confirm_deletion', '1');
    formData.append('keep_legal_data', keepLegal ? '1' : '0');
    formData.append('csrf_token', csrfTokens.delete_data);
    
    fetch('privacy.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => {
                window.location.href = '/';
            }, 3000);
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        showNotification('Eroare de conexiune', 'danger');
    })
    .finally(() => {
        showLoading(false);
    });
}

function deactivateAccount() {
    showNotification('Funcționalitatea de dezactivare a contului va fi implementată în curând.', 'info');
}

function viewDataProcessingLog() {
    showNotification('Istoricul prelucrărilor va fi implementat în curând.', 'info');
}

function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = show ? 'block' : 'none';
}
</script>
{% endblock %}
