<?php
/**
 * Portal Judiciar - Test Contact Functionality
 * 
 * Pagină pentru testarea funcționalității de contact
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

// Importăm clasele necesare
use App\Helpers\SecurityHelper;

// Inițializăm sesiunea dacă nu este deja inițializată
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$testResults = [];

// Test 1: Verificăm dacă fișierele există
$testResults['files'] = [
    'contact.php' => file_exists('contact.php'),
    'process_contact.php' => file_exists('process_contact.php'),
    'SecurityHelper.php' => file_exists('src/Helpers/SecurityHelper.php'),
    'bootstrap.php' => file_exists('bootstrap.php')
];

// Test 2: Verificăm constantele de configurare
$testResults['constants'] = [
    'SMTP_HOST' => defined('SMTP_HOST'),
    'SMTP_PORT' => defined('SMTP_PORT'),
    'SMTP_USERNAME' => defined('SMTP_USERNAME'),
    'SMTP_PASSWORD' => defined('SMTP_PASSWORD'),
    'CONTACT_EMAIL' => defined('CONTACT_EMAIL'),
    'CONTACT_NAME' => defined('CONTACT_NAME'),
    'LOG_DIR' => defined('LOG_DIR')
];

// Test 3: Verificăm directoarele necesare
$testResults['directories'] = [
    'logs' => is_dir(LOG_DIR) && is_writable(LOG_DIR),
    'cache' => is_dir(CACHE_DIR) && is_writable(CACHE_DIR),
    'temp' => is_dir(EXPORT_DIR) && is_writable(EXPORT_DIR)
];

// Test 4: Verificăm extensiile PHP necesare
$testResults['extensions'] = [
    'mbstring' => extension_loaded('mbstring'),
    'openssl' => extension_loaded('openssl'),
    'json' => extension_loaded('json'),
    'session' => extension_loaded('session')
];

// Test 5: Testăm funcțiile SecurityHelper
try {
    $csrfToken = SecurityHelper::generateCSRFToken();
    $testResults['security'] = [
        'csrf_generation' => !empty($csrfToken),
        'csrf_validation' => SecurityHelper::validateCSRFToken($csrfToken),
        'email_validation' => SecurityHelper::validateEmail('<EMAIL>'),
        'phone_validation' => SecurityHelper::validateRomanianPhone('0721234567'),
        'sanitization' => SecurityHelper::sanitizeInput('<script>alert("test")</script>') === '&lt;script&gt;alert(&quot;test&quot;)&lt;/script&gt;'
    ];
} catch (Exception $e) {
    $testResults['security'] = ['error' => $e->getMessage()];
}

// Test 6: Verificăm rate limiting
try {
    $rateLimitCheck = SecurityHelper::checkRateLimit('test', 5, 3600);
    $testResults['rate_limiting'] = [
        'check_works' => is_array($rateLimitCheck) && isset($rateLimitCheck['allowed']),
        'allowed' => $rateLimitCheck['allowed'] ?? false,
        'remaining' => $rateLimitCheck['remaining'] ?? 0
    ];
} catch (Exception $e) {
    $testResults['rate_limiting'] = ['error' => $e->getMessage()];
}

// Test 7: Verificăm PHPMailer
$testResults['phpmailer'] = class_exists('PHPMailer\PHPMailer\PHPMailer');

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Functionality - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-pass {
            color: #28a745;
        }
        .test-fail {
            color: #dc3545;
        }
        .test-warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1 class="display-5">
                <i class="fas fa-vial me-2"></i>
                Test Contact Functionality
            </h1>
            <p class="lead">Verificarea funcționalității de contact pentru Portal Judiciar</p>
        </div>

        <!-- Test Files -->
        <div class="test-section">
            <h3><i class="fas fa-file-code me-2"></i>Fișiere necesare</h3>
            <?php foreach ($testResults['files'] as $file => $exists): ?>
                <div class="test-item">
                    <span><?php echo htmlspecialchars($file); ?></span>
                    <span class="<?php echo $exists ? 'test-pass' : 'test-fail'; ?>">
                        <i class="fas fa-<?php echo $exists ? 'check' : 'times'; ?>"></i>
                        <?php echo $exists ? 'Există' : 'Lipsește'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Test Constants -->
        <div class="test-section">
            <h3><i class="fas fa-cog me-2"></i>Constante de configurare</h3>
            <?php foreach ($testResults['constants'] as $constant => $defined): ?>
                <div class="test-item">
                    <span><?php echo htmlspecialchars($constant); ?></span>
                    <span class="<?php echo $defined ? 'test-pass' : 'test-fail'; ?>">
                        <i class="fas fa-<?php echo $defined ? 'check' : 'times'; ?>"></i>
                        <?php echo $defined ? 'Definită' : 'Nedefinită'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Test Directories -->
        <div class="test-section">
            <h3><i class="fas fa-folder me-2"></i>Directoare necesare</h3>
            <?php foreach ($testResults['directories'] as $dir => $writable): ?>
                <div class="test-item">
                    <span><?php echo htmlspecialchars($dir); ?></span>
                    <span class="<?php echo $writable ? 'test-pass' : 'test-fail'; ?>">
                        <i class="fas fa-<?php echo $writable ? 'check' : 'times'; ?>"></i>
                        <?php echo $writable ? 'OK (scris)' : 'Eroare'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Test Extensions -->
        <div class="test-section">
            <h3><i class="fas fa-puzzle-piece me-2"></i>Extensii PHP</h3>
            <?php foreach ($testResults['extensions'] as $ext => $loaded): ?>
                <div class="test-item">
                    <span><?php echo htmlspecialchars($ext); ?></span>
                    <span class="<?php echo $loaded ? 'test-pass' : 'test-fail'; ?>">
                        <i class="fas fa-<?php echo $loaded ? 'check' : 'times'; ?>"></i>
                        <?php echo $loaded ? 'Încărcată' : 'Lipsește'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Test Security -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt me-2"></i>Funcții de securitate</h3>
            <?php if (isset($testResults['security']['error'])): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Eroare: <?php echo htmlspecialchars($testResults['security']['error']); ?>
                </div>
            <?php else: ?>
                <?php foreach ($testResults['security'] as $test => $result): ?>
                    <div class="test-item">
                        <span><?php echo htmlspecialchars(str_replace('_', ' ', $test)); ?></span>
                        <span class="<?php echo $result ? 'test-pass' : 'test-fail'; ?>">
                            <i class="fas fa-<?php echo $result ? 'check' : 'times'; ?>"></i>
                            <?php echo $result ? 'OK' : 'Eroare'; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Test Rate Limiting -->
        <div class="test-section">
            <h3><i class="fas fa-tachometer-alt me-2"></i>Rate Limiting</h3>
            <?php if (isset($testResults['rate_limiting']['error'])): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Eroare: <?php echo htmlspecialchars($testResults['rate_limiting']['error']); ?>
                </div>
            <?php else: ?>
                <?php foreach ($testResults['rate_limiting'] as $test => $result): ?>
                    <div class="test-item">
                        <span><?php echo htmlspecialchars(str_replace('_', ' ', $test)); ?></span>
                        <span class="test-pass">
                            <i class="fas fa-info-circle"></i>
                            <?php echo is_bool($result) ? ($result ? 'Da' : 'Nu') : $result; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Test PHPMailer -->
        <div class="test-section">
            <h3><i class="fas fa-envelope me-2"></i>PHPMailer</h3>
            <div class="test-item">
                <span>PHPMailer disponibil</span>
                <span class="<?php echo $testResults['phpmailer'] ? 'test-pass' : 'test-fail'; ?>">
                    <i class="fas fa-<?php echo $testResults['phpmailer'] ? 'check' : 'times'; ?>"></i>
                    <?php echo $testResults['phpmailer'] ? 'Disponibil' : 'Indisponibil'; ?>
                </span>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="test-section text-center">
            <h3><i class="fas fa-play me-2"></i>Acțiuni de test</h3>
            <div class="d-grid gap-2 d-md-block">
                <a href="contact.php" class="btn btn-primary">
                    <i class="fas fa-envelope me-2"></i>
                    Testează pagina de contact
                </a>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i>
                    Înapoi la pagina principală
                </a>
            </div>
        </div>

        <!-- Summary -->
        <div class="test-section">
            <h3><i class="fas fa-chart-pie me-2"></i>Rezumat</h3>
            <?php
            $totalTests = 0;
            $passedTests = 0;
            
            foreach ($testResults as $category => $tests) {
                if ($category === 'phpmailer') {
                    $totalTests++;
                    if ($tests) $passedTests++;
                } elseif (is_array($tests) && !isset($tests['error'])) {
                    foreach ($tests as $test => $result) {
                        $totalTests++;
                        if ($result) $passedTests++;
                    }
                }
            }
            
            $percentage = $totalTests > 0 ? round(($passedTests / $totalTests) * 100) : 0;
            $statusClass = $percentage >= 90 ? 'success' : ($percentage >= 70 ? 'warning' : 'danger');
            ?>
            <div class="alert alert-<?php echo $statusClass; ?>">
                <h5 class="mb-2">
                    <i class="fas fa-chart-line me-2"></i>
                    Rezultat general: <?php echo $percentage; ?>%
                </h5>
                <p class="mb-0">
                    <?php echo $passedTests; ?> din <?php echo $totalTests; ?> teste au trecut cu succes.
                    <?php if ($percentage < 100): ?>
                        Vă rugăm să verificați erorile de mai sus.
                    <?php else: ?>
                        Toate testele au trecut! Funcționalitatea de contact este gata de utilizare.
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
