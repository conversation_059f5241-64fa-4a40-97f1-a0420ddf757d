<?php

/**
 * Portal Judiciar - Security Helper
 * 
 * Funcții pentru securitatea aplicației, inclusiv protecție CSRF,
 * validare input și rate limiting.
 */

namespace App\Helpers;

class SecurityHelper
{
    /**
     * Generează un token CSRF și îl stochează în sesiune
     *
     * @return string Token-ul CSRF generat
     */
    public static function generateCSRFToken()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_token_time'] = time();
        
        return $token;
    }
    
    /**
     * Validează un token CSRF
     *
     * @param string $token Token-ul de validat
     * @param int $maxAge Vârsta maximă a token-ului în secunde (implicit 3600 = 1 oră)
     * @return bool True dacă token-ul este valid, false altfel
     */
    public static function validateCSRFToken($token, $maxAge = 3600)
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Verificăm dacă token-ul există în sesiune
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        // Verificăm vârsta token-ului
        if (time() - $_SESSION['csrf_token_time'] > $maxAge) {
            unset($_SESSION['csrf_token']);
            unset($_SESSION['csrf_token_time']);
            return false;
        }
        
        // Verificăm dacă token-ul se potrivește (folosim hash_equals pentru a preveni timing attacks)
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Curăță și validează datele de intrare
     *
     * @param string $data Datele de intrare
     * @param bool $allowHtml Permite HTML (implicit false)
     * @return string Datele curățate
     */
    public static function sanitizeInput($data, $allowHtml = false)
    {
        if (is_array($data)) {
            return array_map(function($item) use ($allowHtml) {
                return self::sanitizeInput($item, $allowHtml);
            }, $data);
        }
        
        $data = trim($data);
        $data = stripslashes($data);
        
        if (!$allowHtml) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        
        return $data;
    }
    
    /**
     * Validează o adresă de email
     *
     * @param string $email Adresa de email de validat
     * @return bool True dacă email-ul este valid, false altfel
     */
    public static function validateEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validează un număr de telefon românesc
     *
     * @param string $phone Numărul de telefon de validat
     * @return bool True dacă numărul este valid, false altfel
     */
    public static function validateRomanianPhone($phone)
    {
        // Eliminăm spațiile și caracterele speciale
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // Verificăm formatele românești comune
        $patterns = [
            '/^(\+40|0040|40)?[0-9]{9}$/',  // Format internațional
            '/^0[0-9]{9}$/',                // Format național
            '/^[0-9]{10}$/'                 // 10 cifre
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $phone)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Verifică rate limiting pentru o adresă IP
     *
     * @param string $action Acțiunea pentru care se verifică rate limiting
     * @param int $limit Numărul maxim de acțiuni permise
     * @param int $period Perioada în secunde
     * @param string|null $ip Adresa IP (implicit IP-ul curent)
     * @return array ['allowed' => bool, 'remaining' => int, 'reset_time' => int]
     */
    public static function checkRateLimit($action, $limit = 5, $period = 3600, $ip = null)
    {
        if ($ip === null) {
            $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        }
        
        $rateLimitFile = LOG_DIR . "/rate_limit_{$action}.json";
        
        // Creăm fișierul dacă nu există
        if (!file_exists($rateLimitFile)) {
            file_put_contents($rateLimitFile, json_encode([]));
        }
        
        // Citim datele de rate limiting
        $rateLimitData = json_decode(file_get_contents($rateLimitFile), true) ?: [];
        
        // Curățăm datele vechi
        $currentTime = time();
        foreach ($rateLimitData as $ipAddr => $data) {
            $rateLimitData[$ipAddr] = array_filter($data, function($timestamp) use ($currentTime, $period) {
                return ($currentTime - $timestamp) <= $period;
            });
            
            if (empty($rateLimitData[$ipAddr])) {
                unset($rateLimitData[$ipAddr]);
            }
        }
        
        // Verificăm numărul de acțiuni pentru IP-ul curent
        $ipActions = $rateLimitData[$ip] ?? [];
        $actionCount = count($ipActions);
        
        $allowed = $actionCount < $limit;
        $remaining = max(0, $limit - $actionCount);
        $resetTime = $currentTime + $period;
        
        if (!empty($ipActions)) {
            $oldestAction = min($ipActions);
            $resetTime = $oldestAction + $period;
        }
        
        return [
            'allowed' => $allowed,
            'remaining' => $remaining,
            'reset_time' => $resetTime,
            'current_count' => $actionCount
        ];
    }
    
    /**
     * Înregistrează o acțiune pentru rate limiting
     *
     * @param string $action Acțiunea înregistrată
     * @param string|null $ip Adresa IP (implicit IP-ul curent)
     * @return bool True dacă acțiunea a fost înregistrată cu succes
     */
    public static function recordAction($action, $ip = null)
    {
        if ($ip === null) {
            $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        }
        
        $rateLimitFile = LOG_DIR . "/rate_limit_{$action}.json";
        
        // Citim datele existente
        $rateLimitData = [];
        if (file_exists($rateLimitFile)) {
            $rateLimitData = json_decode(file_get_contents($rateLimitFile), true) ?: [];
        }
        
        // Adăugăm acțiunea curentă
        if (!isset($rateLimitData[$ip])) {
            $rateLimitData[$ip] = [];
        }
        
        $rateLimitData[$ip][] = time();
        
        // Salvăm datele
        return file_put_contents($rateLimitFile, json_encode($rateLimitData)) !== false;
    }
    
    /**
     * Generează un mesaj de eroare pentru rate limiting
     *
     * @param array $rateLimitInfo Informațiile de rate limiting
     * @return string Mesajul de eroare
     */
    public static function getRateLimitMessage($rateLimitInfo)
    {
        $resetTime = $rateLimitInfo['reset_time'];
        $remainingTime = $resetTime - time();
        
        if ($remainingTime > 3600) {
            $hours = ceil($remainingTime / 3600);
            return "Ați depășit limita de încercări. Vă rugăm să încercați din nou în {$hours} " . 
                   ($hours == 1 ? 'oră' : 'ore') . ".";
        } elseif ($remainingTime > 60) {
            $minutes = ceil($remainingTime / 60);
            return "Ați depășit limita de încercări. Vă rugăm să încercați din nou în {$minutes} " . 
                   ($minutes == 1 ? 'minut' : 'minute') . ".";
        } else {
            return "Ați depășit limita de încercări. Vă rugăm să încercați din nou în câteva secunde.";
        }
    }
}
