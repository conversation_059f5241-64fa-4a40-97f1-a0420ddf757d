<?php

/**
 * Portal Judiciar România - User Management
 * 
 * Administrative interface for user management, including
 * user details, status updates, and GDPR compliance.
 */

require_once dirname(__DIR__, 2) . '/bootstrap.php';
require_once dirname(__DIR__, 2) . '/includes/config.php';

use App\Helpers\TemplateEngine;
use App\Services\AdminAuthService;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use App\Config\Database;

// Session is already started in bootstrap.php
// No need to call session_start() again

// Initialize services
$templateEngine = new TemplateEngine();

// Require admin access with user management permission
AdminAuthService::requireAdmin('user_management');

$userId = $_SESSION['user_id'];

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        
        // Validate CSRF token
        if (!CSRFProtection::validateRequest($_POST, $action)) {
            throw new Exception('Invalid CSRF token');
        }
        
        // Check rate limiting
        $rateLimitKey = "admin_{$userId}";
        if (!RateLimiter::checkLimit($action, $rateLimitKey)) {
            throw new Exception('Rate limit exceeded. Please try again later.');
        }
        
        switch ($action) {
            case 'get_users_list':
                $page = (int)($_POST['page'] ?? 1);
                $limit = 20;
                $offset = ($page - 1) * $limit;
                $search = $_POST['search'] ?? '';
                
                $whereClause = "WHERE 1=1";
                $params = [];
                
                if (!empty($search)) {
                    $whereClause .= " AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)";
                    $searchParam = "%{$search}%";
                    $params = [$searchParam, $searchParam, $searchParam];
                }
                
                $users = Database::fetchAll("
                    SELECT 
                        id, email, first_name, last_name, admin_role,
                        email_verified, last_login_at, created_at,
                        deleted_at, locked_until,
                        (SELECT COUNT(*) FROM monitored_cases WHERE user_id = users.id) as cases_count
                    FROM users 
                    {$whereClause}
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?
                ", array_merge($params, [$limit, $offset]));
                
                $totalUsers = Database::fetchOne("SELECT COUNT(*) as count FROM users {$whereClause}", $params)['count'];
                
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'users' => $users,
                        'total' => $totalUsers,
                        'page' => $page,
                        'pages' => ceil($totalUsers / $limit)
                    ]
                ]);
                break;
                
            case 'toggle_user_status':
                $targetUserId = (int)$_POST['user_id'];
                $newStatus = $_POST['status']; // 'active', 'suspended', 'deleted'
                
                $result = AdminAuthService::updateUserStatus($targetUserId, $newStatus);
                
                if ($result) {
                    AdminAuthService::logAdminAction($userId, 'user_status_changed', [
                        'target_user_id' => $targetUserId,
                        'new_status' => $newStatus
                    ]);
                    
                    echo json_encode(['success' => true]);
                } else {
                    throw new Exception('Failed to update user status');
                }
                break;
                
            case 'get_user_details':
                $targetUserId = (int)$_POST['user_id'];
                $userDetails = AdminAuthService::getUserDetails($targetUserId);
                
                echo json_encode([
                    'success' => true,
                    'data' => $userDetails
                ]);
                break;
                
            case 'export_user_data':
                $targetUserId = (int)$_POST['user_id'];
                $exportData = \App\Security\GDPRCompliance::exportUserData($targetUserId);

                AdminAuthService::logAdminAction($userId, 'user_data_exported', [
                    'target_user_id' => $targetUserId
                ]);

                echo json_encode([
                    'success' => true,
                    'data' => $exportData
                ]);
                break;

            case 'create_user':
                $email = trim($_POST['email'] ?? '');
                $password = $_POST['password'] ?? '';
                $firstName = trim($_POST['first_name'] ?? '');
                $lastName = trim($_POST['last_name'] ?? '');
                $phone = trim($_POST['phone'] ?? '') ?: null;
                $adminRole = $_POST['admin_role'] ?? null;
                $emailVerified = isset($_POST['email_verified']) ? 1 : 0;

                // Validation
                if (empty($email) || empty($password) || empty($firstName) || empty($lastName)) {
                    throw new Exception('Toate câmpurile obligatorii trebuie completate');
                }

                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('Adresa de email nu este validă');
                }

                if (strlen($password) < 8) {
                    throw new Exception('Parola trebuie să aibă cel puțin 8 caractere');
                }

                // Check if email already exists
                $existingUser = Database::fetchOne("SELECT id FROM users WHERE email = ?", [$email]);
                if ($existingUser) {
                    throw new Exception('Un utilizator cu această adresă de email există deja');
                }

                // Validate admin role if provided
                if ($adminRole && !array_key_exists($adminRole, AdminAuthService::PERMISSIONS)) {
                    throw new Exception('Rolul de administrator specificat nu este valid');
                }

                // Create user
                $newUserId = Database::insert('users', [
                    'email' => $email,
                    'password_hash' => password_hash($password, PASSWORD_DEFAULT),
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'phone' => $phone,
                    'email_verified' => $emailVerified,
                    'admin_role' => $adminRole ?: null,
                    'data_processing_consent' => 1,
                    'gdpr_consent' => 1,
                    'gdpr_consent_date' => date('Y-m-d H:i:s'),
                    'gdpr_consent_ip' => $_SERVER['REMOTE_ADDR'] ?? null,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                AdminAuthService::logAdminAction($userId, 'user_created', [
                    'target_user_id' => $newUserId,
                    'email' => $email,
                    'admin_role' => $adminRole
                ]);

                echo json_encode([
                    'success' => true,
                    'message' => 'Utilizatorul a fost creat cu succes',
                    'user_id' => $newUserId
                ]);
                break;

            case 'update_user':
                $targetUserId = (int)$_POST['user_id'];
                $email = trim($_POST['email'] ?? '');
                $firstName = trim($_POST['first_name'] ?? '');
                $lastName = trim($_POST['last_name'] ?? '');
                $phone = trim($_POST['phone'] ?? '') ?: null;
                $adminRole = $_POST['admin_role'] ?? null;
                $emailVerified = isset($_POST['email_verified']) ? 1 : 0;
                $newPassword = $_POST['new_password'] ?? '';

                // Prevent self-modification of admin privileges
                if ($targetUserId == $userId && $adminRole !== AdminAuthService::getAdminRole($userId)) {
                    throw new Exception('Nu vă puteți modifica propriile privilegii de administrator');
                }

                // Validation
                if (empty($email) || empty($firstName) || empty($lastName)) {
                    throw new Exception('Toate câmpurile obligatorii trebuie completate');
                }

                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('Adresa de email nu este validă');
                }

                // Check if email already exists for another user
                $existingUser = Database::fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $targetUserId]);
                if ($existingUser) {
                    throw new Exception('Un alt utilizator cu această adresă de email există deja');
                }

                // Validate admin role if provided
                if ($adminRole && !array_key_exists($adminRole, AdminAuthService::PERMISSIONS)) {
                    throw new Exception('Rolul de administrator specificat nu este valid');
                }

                // Prepare update data
                $updateData = [
                    'email' => $email,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'phone' => $phone,
                    'email_verified' => $emailVerified,
                    'admin_role' => $adminRole ?: null
                ];

                // Update password if provided
                if (!empty($newPassword)) {
                    if (strlen($newPassword) < 8) {
                        throw new Exception('Parola trebuie să aibă cel puțin 8 caractere');
                    }
                    $updateData['password_hash'] = password_hash($newPassword, PASSWORD_DEFAULT);
                }

                $result = Database::update('users', $updateData, ['id' => $targetUserId]);

                if ($result) {
                    AdminAuthService::logAdminAction($userId, 'user_updated', [
                        'target_user_id' => $targetUserId,
                        'email' => $email,
                        'admin_role' => $adminRole,
                        'password_changed' => !empty($newPassword)
                    ]);

                    echo json_encode([
                        'success' => true,
                        'message' => 'Utilizatorul a fost actualizat cu succes'
                    ]);
                } else {
                    throw new Exception('Nu s-au putut actualiza datele utilizatorului');
                }
                break;

            case 'delete_user':
                $targetUserId = (int)$_POST['user_id'];

                // Prevent self-deletion
                if ($targetUserId == $userId) {
                    throw new Exception('Nu vă puteți șterge propriul cont');
                }

                // Soft delete - mark as deleted instead of removing
                $result = Database::update('users', [
                    'deleted_at' => date('Y-m-d H:i:s')
                ], ['id' => $targetUserId]);

                if ($result) {
                    AdminAuthService::logAdminAction($userId, 'user_deleted', [
                        'target_user_id' => $targetUserId
                    ]);

                    echo json_encode([
                        'success' => true,
                        'message' => 'Utilizatorul a fost șters cu succes'
                    ]);
                } else {
                    throw new Exception('Nu s-a putut șterge utilizatorul');
                }
                break;

            case 'toggle_email_verification':
                $targetUserId = (int)$_POST['user_id'];
                $verified = isset($_POST['verified']) ? 1 : 0;

                $result = Database::update('users', [
                    'email_verified' => $verified
                ], ['id' => $targetUserId]);

                if ($result) {
                    AdminAuthService::logAdminAction($userId, 'email_verification_toggled', [
                        'target_user_id' => $targetUserId,
                        'verified' => $verified
                    ]);

                    echo json_encode([
                        'success' => true,
                        'message' => $verified ? 'Email-ul a fost marcat ca verificat' : 'Email-ul a fost marcat ca neverificat'
                    ]);
                } else {
                    throw new Exception('Nu s-a putut actualiza statusul de verificare');
                }
                break;

            default:
                throw new Exception('Unknown action');
        }
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    
    exit;
}

// Get initial users list with error handling
try {
    $users = Database::fetchAll("
        SELECT
            id, email, first_name, last_name, admin_role,
            email_verified, last_login_at, created_at,
            deleted_at, locked_until,
            (SELECT COUNT(*) FROM monitored_cases WHERE user_id = users.id) as cases_count
        FROM users
        ORDER BY created_at DESC
        LIMIT 20
    ");
} catch (Exception $e) {
    // Fallback query without monitored_cases if table doesn't exist
    error_log("Failed to query with monitored_cases: " . $e->getMessage());
    $users = Database::fetchAll("
        SELECT
            id, email, first_name, last_name, admin_role,
            email_verified, last_login_at, created_at,
            deleted_at, locked_until,
            0 as cases_count
        FROM users
        ORDER BY created_at DESC
        LIMIT 20
    ");
}

$totalUsers = Database::fetchOne("SELECT COUNT(*) as count FROM users")['count'];

// Get CSRF tokens
$csrfTokens = [
    'get_users_list' => CSRFProtection::generateToken('get_users_list'),
    'toggle_user_status' => CSRFProtection::generateToken('toggle_user_status'),
    'get_user_details' => CSRFProtection::generateToken('get_user_details'),
    'export_user_data' => CSRFProtection::generateToken('export_user_data'),
    'create_user' => CSRFProtection::generateToken('create_user'),
    'update_user' => CSRFProtection::generateToken('update_user'),
    'delete_user' => CSRFProtection::generateToken('delete_user'),
    'toggle_email_verification' => CSRFProtection::generateToken('toggle_email_verification')
];

// Prepare template data
$templateData = [
    'page_title' => 'Gestionare Utilizatori - Portal Judiciar România',
    'users' => $users,
    'total_users' => $totalUsers,
    'csrf_tokens' => $csrfTokens,
    'user_name' => $_SESSION['user_name'] ?? 'Administrator',
    'user_role' => AdminAuthService::getAdminRole($userId),
    'user_permissions' => AdminAuthService::PERMISSIONS[AdminAuthService::getAdminRole($userId)] ?? [],
    'admin_roles' => [
        'super_admin' => 'Super Administrator',
        'admin' => 'Administrator',
        'moderator' => 'Moderator',
        'viewer' => 'Viewer'
    ],
    'current_user_id' => $userId
];

// Render template
echo $templateEngine->render('admin/users.twig', $templateData);
