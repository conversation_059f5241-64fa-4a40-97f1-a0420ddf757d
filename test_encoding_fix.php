<?php
/**
 * Test script for Romanian character encoding fix
 * Tests the normalizeForSearch function with various Romanian text inputs
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Include the normalizeForSearch function from index.php
require_once 'index.php';

echo "<h1>🔤 Romanian Character Encoding Test</h1>\n";
echo "<p><strong>Testing:</strong> normalizeForSearch() function with Romanian diacritics</p>\n";

// Test cases with Romanian names and diacritics
$testCases = [
    // Standard Romanian names with diacritics
    '<PERSON><PERSON>a Tudorița',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON> Viteazul',
    
    // Mixed case and special characters
    'SARAGEA TUDORIȚA',
    'saragea tudorița',
    'Sărăcuță Gheorghe',
    'Țăranu Ion',
    
    // Legacy encoding variants (s-cedilla, t-cedilla)
    '<PERSON><PERSON><PERSON>',
    'ŞTEFAN ŢUŢU',
    
    // Names with numbers and special characters
    'SC ROMÂNIA SRL',
    '<PERSON><PERSON><PERSON> - Administrator',
    'Gheorghe & Asociații',
    
    // Empty and edge cases
    '',
    '   ',
    'A',
    'Ă',
    
    // Potential problematic encodings
    'Mănuțiu Ștefan',
    'Crăciun Gheorghiță',
    'Bălănescu Mărțișor'
];

echo "<h2>📊 Test Results</h2>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>\n";
echo "<thead style='background: #e9ecef;'>\n";
echo "<tr><th style='padding: 12px;'>Original Text</th><th style='padding: 12px;'>Normalized Result</th><th style='padding: 12px;'>Status</th><th style='padding: 12px;'>Notes</th></tr>\n";
echo "</thead>\n";
echo "<tbody>\n";

$successCount = 0;
$totalTests = count($testCases);

foreach ($testCases as $index => $testText) {
    echo "<tr>\n";
    echo "<td style='padding: 10px; font-family: monospace;'>" . htmlspecialchars($testText) . "</td>\n";
    
    try {
        // Capture any warnings or errors
        $originalErrorReporting = error_reporting();
        $errors = [];
        
        set_error_handler(function($severity, $message, $file, $line) use (&$errors) {
            $errors[] = "[$severity] $message at line $line";
        });
        
        // Test the function
        $startTime = microtime(true);
        $result = normalizeForSearch($testText);
        $processingTime = round((microtime(true) - $startTime) * 1000, 3);
        
        restore_error_handler();
        error_reporting($originalErrorReporting);
        
        echo "<td style='padding: 10px; font-family: monospace;'>" . htmlspecialchars($result) . "</td>\n";
        
        // Determine status
        if (empty($errors)) {
            echo "<td style='padding: 10px; color: #28a745;'>✅ Success</td>\n";
            $successCount++;
            $notes = "Processed in {$processingTime}ms";
        } else {
            echo "<td style='padding: 10px; color: #dc3545;'>❌ Warning</td>\n";
            $notes = "Warnings: " . implode('; ', $errors);
        }
        
        echo "<td style='padding: 10px; font-size: 0.9em;'>{$notes}</td>\n";
        
    } catch (Exception $e) {
        echo "<td style='padding: 10px; color: #dc3545;'>ERROR</td>\n";
        echo "<td style='padding: 10px; color: #dc3545;'>❌ Exception</td>\n";
        echo "<td style='padding: 10px; color: #dc3545;'>Exception: " . htmlspecialchars($e->getMessage()) . "</td>\n";
    }
    
    echo "</tr>\n";
}

echo "</tbody>\n";
echo "</table>\n";

// Summary
$successPercentage = round(($successCount / $totalTests) * 100, 1);

echo "<h2>📈 Test Summary</h2>\n";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; margin: 15px 0; border-radius: 8px;'>\n";
echo "<p><strong>Total Tests:</strong> {$totalTests}</p>\n";
echo "<p><strong>Successful:</strong> {$successCount}</p>\n";
echo "<p><strong>Success Rate:</strong> <span style='font-size: 1.5em; color: " . ($successPercentage >= 95 ? '#28a745' : ($successPercentage >= 80 ? '#ffc107' : '#dc3545')) . ";'>{$successPercentage}%</span></p>\n";
echo "</div>\n";

// Test specific Romanian diacritics mapping
echo "<h2>🔤 Diacritics Mapping Test</h2>\n";

$diacriticsTests = [
    'ă' => 'a', 'Ă' => 'A',
    'â' => 'a', 'Â' => 'A', 
    'î' => 'i', 'Î' => 'I',
    'ș' => 's', 'Ș' => 'S',
    'ț' => 't', 'Ț' => 'T',
    'ş' => 's', 'Ş' => 'S',  // Legacy s-cedilla
    'ţ' => 't', 'Ţ' => 'T'   // Legacy t-cedilla
];

echo "<table border='1' style='border-collapse: collapse; width: 50%; margin: 15px 0;'>\n";
echo "<thead style='background: #e9ecef;'>\n";
echo "<tr><th style='padding: 12px;'>Diacritic</th><th style='padding: 12px;'>Expected</th><th style='padding: 12px;'>Result</th><th style='padding: 12px;'>Status</th></tr>\n";
echo "</thead>\n";
echo "<tbody>\n";

$diacriticsSuccess = 0;
$diacriticsTotal = count($diacriticsTests);

foreach ($diacriticsTests as $diacritic => $expected) {
    $result = normalizeForSearch($diacritic);
    $isCorrect = ($result === strtolower($expected));
    
    if ($isCorrect) $diacriticsSuccess++;
    
    echo "<tr>\n";
    echo "<td style='padding: 10px; text-align: center; font-size: 1.2em;'>{$diacritic}</td>\n";
    echo "<td style='padding: 10px; text-align: center;'>" . strtolower($expected) . "</td>\n";
    echo "<td style='padding: 10px; text-align: center;'>{$result}</td>\n";
    echo "<td style='padding: 10px; text-align: center; color: " . ($isCorrect ? '#28a745' : '#dc3545') . ";'>" . ($isCorrect ? '✅' : '❌') . "</td>\n";
    echo "</tr>\n";
}

echo "</tbody>\n";
echo "</table>\n";

$diacriticsPercentage = round(($diacriticsSuccess / $diacriticsTotal) * 100, 1);

echo "<p><strong>Diacritics Mapping Success Rate:</strong> <span style='color: " . ($diacriticsPercentage >= 95 ? '#28a745' : '#dc3545') . ";'>{$diacriticsPercentage}%</span></p>\n";

// Test encoding detection with simulated problematic inputs
echo "<h2>🔍 Encoding Detection Test</h2>\n";

// Simulate different encoding scenarios
$encodingTests = [
    'UTF-8 valid' => 'Saragea Tudorița',
    'ASCII only' => 'Popescu Ion',
    'Mixed content' => 'Test ăâîșț 123',
    'Empty string' => '',
    'Whitespace only' => '   ',
    'Numbers only' => '12345',
    'Special chars' => 'Test & Co. SRL'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>\n";
echo "<thead style='background: #e9ecef;'>\n";
echo "<tr><th style='padding: 12px;'>Test Case</th><th style='padding: 12px;'>Input</th><th style='padding: 12px;'>Output</th><th style='padding: 12px;'>Encoding Check</th><th style='padding: 12px;'>Status</th></tr>\n";
echo "</thead>\n";
echo "<tbody>\n";

foreach ($encodingTests as $testName => $input) {
    $output = normalizeForSearch($input);
    $isValidUTF8 = mb_check_encoding($output, 'UTF-8');
    
    echo "<tr>\n";
    echo "<td style='padding: 10px;'>{$testName}</td>\n";
    echo "<td style='padding: 10px; font-family: monospace;'>" . htmlspecialchars($input) . "</td>\n";
    echo "<td style='padding: 10px; font-family: monospace;'>" . htmlspecialchars($output) . "</td>\n";
    echo "<td style='padding: 10px; text-align: center; color: " . ($isValidUTF8 ? '#28a745' : '#dc3545') . ";'>" . ($isValidUTF8 ? 'Valid UTF-8' : 'Invalid UTF-8') . "</td>\n";
    echo "<td style='padding: 10px; text-align: center; color: " . ($isValidUTF8 ? '#28a745' : '#dc3545') . ";'>" . ($isValidUTF8 ? '✅' : '❌') . "</td>\n";
    echo "</tr>\n";
}

echo "</tbody>\n";
echo "</table>\n";

// Overall assessment
echo "<h2>🎯 Overall Assessment</h2>\n";

$overallSuccess = ($successPercentage >= 95 && $diacriticsPercentage >= 95);

if ($overallSuccess) {
    echo "<div style='background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ SUCCESS: Encoding Fix Working Correctly</h4>\n";
    echo "<p style='color: #155724; margin: 0;'>The Romanian character encoding fix is working properly. No more encoding warnings should occur during search operations.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ ISSUES DETECTED: Further Investigation Needed</h4>\n";
    echo "<p style='color: #721c24; margin: 0;'>Some tests failed. The encoding fix may need additional refinement.</p>\n";
    echo "</div>\n";
}

echo "<h2>🧪 Next Steps</h2>\n";
echo "<ul>\n";
echo "<li>🔍 Test the fix with actual search operations in the portal</li>\n";
echo "<li>📝 Search for 'Saragea Tudorița' to verify Romanian diacritics handling</li>\n";
echo "<li>🌐 Test both bulk search and advanced search features</li>\n";
echo "<li>📊 Monitor error logs for any remaining encoding warnings</li>\n";
echo "</ul>\n";

echo "<h2>🌐 Test Search URLs</h2>\n";
echo "<ul>\n";
echo "<li><a href='index.php' target='_blank'>Main Search Page</a></li>\n";
echo "<li><a href='search.php' target='_blank'>Advanced Search Page</a></li>\n";
echo "</ul>\n";
?>
