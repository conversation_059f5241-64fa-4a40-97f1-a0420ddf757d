<?php
require_once 'services/SedinteService.php';

echo "Testing CurteadeApelALBAIULIA with SOAP API for sessions...\n";

try {
    $service = new SedinteService();
    $params = [
        'dataSedinta' => '2024-01-15T00:00:00',
        'institutie' => 'CurteadeApelALBAIULIA'
    ];
    
    echo "Calling cautareSedinte with params:\n";
    print_r($params);
    
    $results = $service->cautareSedinte($params);
    echo "SUCCESS: Codul CurteadeApelALBAIULIA functioneaza cu API-ul SOAP pentru sedinte.\n";
    echo "Rezultate: " . count($results) . "\n";
    
    if (!empty($results)) {
        echo "Prima sedinta:\n";
        print_r($results[0]);
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Error details:\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n\nTesting with DosarService for comparison...\n";

try {
    require_once 'services/DosarService.php';
    $dosarService = new DosarService();
    
    $searchParams = [
        'numarDosar' => '',
        'numeCompletParte' => 'test',
        'obiectDosar' => '',
        'institutie' => 'CurteadeApelALBAIULIA',
        'dataInceput' => '2024-01-01',
        'dataSfarsit' => '2024-01-31'
    ];
    
    echo "Calling cautareAvansata with params:\n";
    print_r($searchParams);
    
    $dosarResults = $dosarService->cautareAvansata($searchParams);
    echo "SUCCESS: Codul CurteadeApelALBAIULIA functioneaza cu API-ul SOAP pentru dosare.\n";
    echo "Rezultate: " . count($dosarResults) . "\n";
    
} catch (Exception $e) {
    echo "ERROR in DosarService: " . $e->getMessage() . "\n";
}
?>
