<?php
/**
 * Test to examine the actual decision text content and see why extraction isn't working
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🔍 Decision Text Content Analysis</h1>\n";
echo "<p><strong>Case:</strong> 130/98/2022 from Curtea de Apel BUCURESTI</p>\n";

try {
    $dosarService = new DosarService();
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    // Get reflection access
    $reflection = new ReflectionClass($dosarService);
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $response = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Content analysis");
    
    if (!isset($response->CautareDosare2Result->Dosar)) {
        echo "<p style='color: red;'>❌ No SOAP data found</p>\n";
        exit;
    }
    
    $rawDosar = is_array($response->CautareDosare2Result->Dosar) ? $response->CautareDosare2Result->Dosar[0] : $response->CautareDosare2Result->Dosar;
    
    echo "<h2>📄 Decision Text Content</h2>\n";
    
    if (isset($rawDosar->sedinte) && isset($rawDosar->sedinte->DosarSedinta)) {
        $sedinte = $rawDosar->sedinte->DosarSedinta;
        if (!is_array($sedinte)) {
            $sedinte = [$sedinte];
        }
        
        echo "<p><strong>Number of court sessions:</strong> " . count($sedinte) . "</p>\n";
        
        foreach ($sedinte as $index => $sedinta) {
            echo "<h3>Session " . ($index + 1) . "</h3>\n";
            echo "<p><strong>Date:</strong> " . ($sedinta->data ?? 'N/A') . "</p>\n";
            echo "<p><strong>Time:</strong> " . ($sedinta->ora ?? 'N/A') . "</p>\n";
            
            // Show solution text
            if (isset($sedinta->solutie) && !empty($sedinta->solutie)) {
                $solutieLength = strlen($sedinta->solutie);
                echo "<h4>Solution Text ({$solutieLength} characters)</h4>\n";
                
                // Show the full text in a scrollable div
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9; max-height: 400px; overflow-y: auto;'>\n";
                echo "<pre style='white-space: pre-wrap; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($sedinta->solutie) . "</pre>\n";
                echo "</div>\n";
                
                // Look for specific patterns that might contain party names
                echo "<h5>Pattern Analysis:</h5>\n";
                
                // Check for common party list patterns
                $patterns = [
                    'apelanţii' => '/apelanţii\s+([^.]+)/i',
                    'creditorii' => '/creditorii\s+([^.]+)/i',
                    'formulate de' => '/formulate de\s+([^.]+)/i',
                    'comma lists' => '/([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+(?:,\s*[A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+){2,})/u'
                ];
                
                foreach ($patterns as $patternName => $pattern) {
                    if (preg_match_all($pattern, $sedinta->solutie, $matches)) {
                        echo "<p><strong>{$patternName}:</strong> " . count($matches[1]) . " matches found</p>\n";
                        foreach ($matches[1] as $i => $match) {
                            if ($i < 3) { // Show first 3 matches
                                echo "<div style='margin-left: 20px; font-size: 12px;'>" . htmlspecialchars(substr($match, 0, 200)) . "...</div>\n";
                            }
                        }
                    } else {
                        echo "<p><strong>{$patternName}:</strong> No matches</p>\n";
                    }
                }
                
                // Search for specific names mentioned in the task
                $searchNames = ['SARAGEA', 'TUDORITA', 'Saragea', 'Tudorita'];
                foreach ($searchNames as $searchName) {
                    $pos = stripos($sedinta->solutie, $searchName);
                    if ($pos !== false) {
                        echo "<p style='color: green;'><strong>Found '{$searchName}' at position {$pos}</strong></p>\n";
                        $start = max(0, $pos - 100);
                        $context = substr($sedinta->solutie, $start, 200);
                        echo "<div style='background: #ffffcc; padding: 5px; margin: 5px 0;'>" . htmlspecialchars($context) . "</div>\n";
                    } else {
                        echo "<p style='color: red;'>'{$searchName}' not found</p>\n";
                    }
                }
                
            } else {
                echo "<p>No solution text available</p>\n";
            }
            
            // Show summary text
            if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                $sumarLength = strlen($sedinta->solutieSumar);
                echo "<h4>Summary Text ({$sumarLength} characters)</h4>\n";
                
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f0f8ff; max-height: 200px; overflow-y: auto;'>\n";
                echo "<pre style='white-space: pre-wrap; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($sedinta->solutieSumar) . "</pre>\n";
                echo "</div>\n";
            } else {
                echo "<p>No summary text available</p>\n";
            }
            
            echo "<hr>\n";
        }
        
    } else {
        echo "<p style='color: red;'>❌ No court sessions found</p>\n";
    }
    
    echo "<h2>🧪 Manual Pattern Testing</h2>\n";
    
    // Test the extraction method manually
    $extractMethod = $reflection->getMethod('extractPartiesFromDecisionText');
    $extractMethod->setAccessible(true);
    
    $extractedParties = $extractMethod->invoke($dosarService, $rawDosar);
    echo "<p><strong>Parties extracted by current patterns:</strong> " . count($extractedParties) . "</p>\n";
    
    if (count($extractedParties) > 0) {
        echo "<h4>Extracted parties:</h4>\n";
        echo "<ul>\n";
        foreach ($extractedParties as $party) {
            echo "<li>" . htmlspecialchars($party['nume']) . " (" . $party['calitate'] . ")</li>\n";
        }
        echo "</ul>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>📋 Next Steps</h2>\n";
echo "<p>Based on this analysis, we can:</p>\n";
echo "<ul>\n";
echo "<li>See exactly what decision text content is available</li>\n";
echo "<li>Understand why current patterns aren't matching</li>\n";
echo "<li>Identify what new patterns need to be added</li>\n";
echo "<li>Determine if the issue is with extraction or with the data itself</li>\n";
echo "</ul>\n";
