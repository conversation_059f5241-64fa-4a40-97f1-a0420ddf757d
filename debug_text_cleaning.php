<?php
/**
 * Debug text cleaning to see what happens to specific parties
 */

require_once 'config/config.php';

echo "=== DEBUGGING TEXT CLEANING ===" . PHP_EOL;

try {
    // Read the SOAP response file
    $responseFile = 'soap_response_2025-07-04_17-14-14.xml';
    $xmlContent = file_get_contents($responseFile);
    
    // Extract solutieSumar content
    if (preg_match('/<solutieSumar>(.*?)<\/solutieSumar>/s', $xmlContent, $matches)) {
        $solutieSumar = $matches[1];
        
        // Apply the same pattern as in the service
        $pattern = '/formulate de creditorii ([^;]+(?:;[^;]+)*)/';
        if (preg_match($pattern, $solutieSumar, $matches)) {
            $creditorsText = $matches[1];
            
            echo "Original creditors text length: " . strlen($creditorsText) . PHP_EOL;
            echo "Original semicolons: " . substr_count($creditorsText, ';') . PHP_EOL;
            echo PHP_EOL;
            
            // Apply the cleaning (new pattern)
            $cleanedText = preg_replace('/\.\s*Suma de 200 de lei.*$/s', '', $creditorsText);
            
            echo "Cleaned creditors text length: " . strlen($cleanedText) . PHP_EOL;
            echo "Cleaned semicolons: " . substr_count($cleanedText, ';') . PHP_EOL;
            echo PHP_EOL;
            
            // Check for target parties in both versions
            $targetParties = ['SARAGEA TUDORIŢA', 'ZAMFIR NICOLETA'];
            
            foreach ($targetParties as $party) {
                echo "=== CHECKING: $party ===" . PHP_EOL;
                
                $posOriginal = strpos($creditorsText, $party);
                $posCleaned = strpos($cleanedText, $party);
                
                echo "In original text: " . ($posOriginal !== false ? "✅ YES (pos $posOriginal)" : "❌ NO") . PHP_EOL;
                echo "In cleaned text: " . ($posCleaned !== false ? "✅ YES (pos $posCleaned)" : "❌ NO") . PHP_EOL;
                
                if ($posOriginal !== false) {
                    // Show context in original
                    $start = max(0, $posOriginal - 50);
                    $end = min(strlen($creditorsText), $posOriginal + strlen($party) + 50);
                    $context = substr($creditorsText, $start, $end - $start);
                    echo "Original context: ...{$context}..." . PHP_EOL;
                }
                
                if ($posCleaned !== false) {
                    // Show context in cleaned
                    $start = max(0, $posCleaned - 50);
                    $end = min(strlen($cleanedText), $posCleaned + strlen($party) + 50);
                    $context = substr($cleanedText, $start, $end - $start);
                    echo "Cleaned context: ...{$context}..." . PHP_EOL;
                }
                
                echo PHP_EOL;
            }
            
            // Show the end of both texts
            echo "=== TEXT ENDINGS ===" . PHP_EOL;
            echo "Original ending (last 200 chars):" . PHP_EOL;
            echo "..." . substr($creditorsText, -200) . PHP_EOL;
            echo PHP_EOL;
            
            echo "Cleaned ending (last 200 chars):" . PHP_EOL;
            echo "..." . substr($cleanedText, -200) . PHP_EOL;
            echo PHP_EOL;
            
            // Split and analyze
            $creditorNames = explode(';', $cleanedText);
            echo "Total names after split: " . count($creditorNames) . PHP_EOL;
            
            // Check last 10 names
            echo "Last 10 names:" . PHP_EOL;
            $start = max(0, count($creditorNames) - 10);
            for ($i = $start; $i < count($creditorNames); $i++) {
                $name = trim($creditorNames[$i]);
                echo ($i + 1) . ". '$name' (length: " . strlen($name) . ")" . PHP_EOL;
                
                // Check if this is one of our target parties
                foreach ($targetParties as $target) {
                    if (strpos($name, $target) !== false) {
                        echo "   ✅ Contains target party: $target" . PHP_EOL;
                    }
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== DEBUG COMPLETE ===" . PHP_EOL;
?>
