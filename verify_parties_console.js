/**
 * <PERSON><PERSON>er Consol<PERSON> to Verify All Parties Are Displayed
 * 
 * Instructions:
 * 1. Open detalii_dosar.php?numar=130%2F98%2F2022&institutie=TribunalulIALOMITA
 * 2. Open browser developer tools (F12)
 * 3. Go to Console tab
 * 4. Copy and paste this entire script
 * 5. Press Enter to run
 * 
 * This script will verify that all 100 parties are properly displayed
 */

console.log('🔍 Starting Parties Display Verification...');
console.log('=====================================');

// Find the parties table
const tabelParti = document.getElementById('tabelParti');
if (!tabelParti) {
    console.error('❌ Parties table not found!');
} else {
    console.log('✅ Parties table found');
}

// Find all party rows
const partyRows = tabelParti ? tabelParti.querySelectorAll('tbody tr.parte-row') : [];
console.log(`📊 Total party rows in DOM: ${partyRows.length}`);

// Expected count for this specific case
const expectedCount = 100;
console.log(`🎯 Expected party count: ${expectedCount}`);

// Check if counts match
if (partyRows.length === expectedCount) {
    console.log('✅ SUCCESS: All parties are present in the DOM!');
} else {
    console.log(`❌ ISSUE: Expected ${expectedCount} parties, found ${partyRows.length}`);
}

// Check visibility of all rows
let visibleCount = 0;
let hiddenCount = 0;

partyRows.forEach((row, index) => {
    const computedStyle = window.getComputedStyle(row);
    const isVisible = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden';
    
    if (isVisible) {
        visibleCount++;
    } else {
        hiddenCount++;
        console.log(`⚠️ Row ${index + 1} is hidden:`, row);
    }
});

console.log(`👁️ Visible parties: ${visibleCount}`);
console.log(`🙈 Hidden parties: ${hiddenCount}`);

// Check party counter
const partiCounter = document.querySelector('.parti-counter');
if (partiCounter) {
    console.log(`🔢 Party counter text: "${partiCounter.textContent}"`);
} else {
    console.log('⚠️ Party counter not found');
}

// Performance test
console.log('\n🚀 Running Performance Test...');
const startTime = performance.now();

// Force reflow on all rows
partyRows.forEach(row => {
    row.offsetHeight;
});

const endTime = performance.now();
const duration = endTime - startTime;

console.log(`⏱️ Time to process ${partyRows.length} rows: ${duration.toFixed(2)}ms`);
console.log(`📈 Average time per row: ${(duration / partyRows.length).toFixed(4)}ms`);

if (duration < 100) {
    console.log('✅ Performance: Excellent');
} else if (duration < 500) {
    console.log('✅ Performance: Good');
} else {
    console.log('⚠️ Performance: Needs optimization');
}

// Test search functionality
console.log('\n🔍 Testing Search Functionality...');
const searchInput = document.querySelector('#searchParti');
if (searchInput) {
    console.log('✅ Search input found');
    
    // Test search with a common term
    console.log('Testing search with "ELENA"...');
    searchInput.value = 'ELENA';
    searchInput.dispatchEvent(new Event('input'));
    
    // Wait a bit for debounce, then check results
    setTimeout(() => {
        const visibleAfterSearch = Array.from(partyRows).filter(row => 
            window.getComputedStyle(row).display !== 'none'
        ).length;
        console.log(`🔍 Visible parties after search: ${visibleAfterSearch}`);
        
        // Clear search
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
        
        setTimeout(() => {
            const visibleAfterClear = Array.from(partyRows).filter(row => 
                window.getComputedStyle(row).display !== 'none'
            ).length;
            console.log(`🔄 Visible parties after clearing search: ${visibleAfterClear}`);
            
            if (visibleAfterClear === partyRows.length) {
                console.log('✅ Search functionality working correctly');
            } else {
                console.log('❌ Search functionality has issues');
            }
        }, 500);
    }, 500);
} else {
    console.log('❌ Search input not found');
}

// Summary
console.log('\n📋 SUMMARY');
console.log('==========');
console.log(`Total parties in DOM: ${partyRows.length}/${expectedCount}`);
console.log(`Visible parties: ${visibleCount}`);
console.log(`Hidden parties: ${hiddenCount}`);
console.log(`Performance: ${duration.toFixed(2)}ms`);

if (partyRows.length === expectedCount && visibleCount === expectedCount && hiddenCount === 0) {
    console.log('🎉 ALL TESTS PASSED! All parties are correctly displayed.');
} else {
    console.log('⚠️ ISSUES DETECTED! Some parties may not be displayed correctly.');
}

console.log('\n💡 TIP: If you see issues, check:');
console.log('   - Network tab for failed requests');
console.log('   - Console for JavaScript errors');
console.log('   - Elements tab for missing table rows');
console.log('   - Performance tab for rendering issues');
