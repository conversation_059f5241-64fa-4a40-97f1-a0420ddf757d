<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 DEBUGGING DUPLICATE DETECTION\n";
echo "=================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current total parties: " . count($dosar->parti) . "\n\n";
    
    // Count parties by source
    $soapParties = 0;
    $decisionParties = 0;
    $apellantParties = 0;
    
    foreach ($dosar->parti as $party) {
        $partyArray = (array) $party;
        if (isset($partyArray['source'])) {
            if ($partyArray['source'] === 'soap_api') {
                $soapParties++;
            } elseif ($partyArray['source'] === 'decision_text') {
                $decisionParties++;
                if ($partyArray['calitate'] === 'Apelant') {
                    $apellantParties++;
                }
            }
        }
    }
    
    echo "📊 CURRENT BREAKDOWN:\n";
    echo "SOAP API parties: {$soapParties}\n";
    echo "Decision text parties: {$decisionParties}\n";
    echo "  - Apelant parties: {$apellantParties}\n\n";
    
    // Get the solutieSumar content and manually extract
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    // Manually extract from both sections
    $manualParties = [];
    
    // Extract from Anulează section
    if (preg_match('/Anulează\s+apelurile\s+formulate\s+de\s+apelanţii\s+([^.]+?)(?:\s*ca\s+(?:netimbrate|nefondate))?\./', $solutieSumarText, $anuleazaMatch)) {
        $apellantsText = $anuleazaMatch[1];
        $apellantNames = explode(',', $apellantsText);
        
        foreach ($apellantNames as $apellantName) {
            $apellantName = trim($apellantName);
            $apellantName = preg_replace('/\s*şi\s*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\?.*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\(.*?\)/', '', $apellantName);
            $apellantName = trim($apellantName);
            
            if (strlen($apellantName) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $apellantName)) {
                $manualParties[] = [
                    'nume' => $apellantName,
                    'section' => 'Anulează'
                ];
            }
        }
    }
    
    // Extract from Respinge section
    if (preg_match('/Respinge\s+apelurile\s+formulate\s+de\s+apelan[ţ?]ii\s+(.+?)(?:\s*ca\s+(?:netimbrate|nefondate))?(?:\.\s*Admite|$)/s', $solutieSumarText, $respingeMatch)) {
        $apellantsText = $respingeMatch[1];
        
        // Apply cleanup
        $apellantsText = preg_replace('/\.\s*[A-Z][^,]*(?:Georgeta|Elisabeta|Marioara|Anişoara|Florica|Steliana|Florenţa|Sorin)[^,]*/', '', $apellantsText);
        $apellantsText = preg_replace('/\s*ca\s+(?:netimbrate|nefondate)\s*/', '', $apellantsText);
        
        $apellantNames = explode(',', $apellantsText);
        
        foreach ($apellantNames as $apellantName) {
            $apellantName = trim($apellantName);
            $apellantName = preg_replace('/\s*şi\s*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\?.*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\(.*?\)/', '', $apellantName);
            $apellantName = trim($apellantName);
            
            if (strlen($apellantName) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $apellantName)) {
                $manualParties[] = [
                    'nume' => $apellantName,
                    'section' => 'Respinge'
                ];
            }
        }
    }
    
    echo "📊 MANUAL EXTRACTION RESULTS:\n";
    echo "Total manually extracted: " . count($manualParties) . "\n";
    
    $anuleazaCount = 0;
    $respingeCount = 0;
    foreach ($manualParties as $party) {
        if ($party['section'] === 'Anulează') {
            $anuleazaCount++;
        } else {
            $respingeCount++;
        }
    }
    
    echo "  - From Anulează: {$anuleazaCount}\n";
    echo "  - From Respinge: {$respingeCount}\n\n";
    
    // Check for duplicates within manual extraction
    $uniqueNames = [];
    $duplicates = [];
    
    foreach ($manualParties as $party) {
        $normalizedName = strtolower(trim($party['nume']));
        $normalizedName = str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], $normalizedName);
        
        if (isset($uniqueNames[$normalizedName])) {
            $duplicates[] = [
                'name' => $party['nume'],
                'section' => $party['section'],
                'duplicate_of' => $uniqueNames[$normalizedName]
            ];
        } else {
            $uniqueNames[$normalizedName] = $party;
        }
    }
    
    echo "📊 DUPLICATE ANALYSIS:\n";
    echo "Unique names after normalization: " . count($uniqueNames) . "\n";
    echo "Duplicates found: " . count($duplicates) . "\n\n";
    
    if (count($duplicates) > 0) {
        echo "First 10 duplicates:\n";
        for ($i = 0; $i < min(10, count($duplicates)); $i++) {
            $dup = $duplicates[$i];
            echo "  " . ($i + 1) . ". \"{$dup['name']}\" ({$dup['section']}) = \"{$dup['duplicate_of']['nume']}\" ({$dup['duplicate_of']['section']})\n";
        }
        echo "\n";
    }
    
    // Check overlap with SOAP API
    $soapNames = [];
    foreach ($dosar->parti as $party) {
        $partyArray = (array) $party;
        if (isset($partyArray['source']) && $partyArray['source'] === 'soap_api') {
            $normalizedName = strtolower(trim($partyArray['nume']));
            $normalizedName = str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], $normalizedName);
            $soapNames[$normalizedName] = $partyArray['nume'];
        }
    }
    
    $soapOverlap = 0;
    foreach ($uniqueNames as $normalizedName => $party) {
        if (isset($soapNames[$normalizedName])) {
            $soapOverlap++;
        }
    }
    
    echo "📊 SOAP API OVERLAP:\n";
    echo "Manual names that overlap with SOAP: {$soapOverlap}\n";
    echo "Manual names that are unique: " . (count($uniqueNames) - $soapOverlap) . "\n\n";
    
    echo "📊 EXPECTED FINAL COUNT:\n";
    echo "SOAP API: {$soapParties}\n";
    echo "Unique manual names: " . (count($uniqueNames) - $soapOverlap) . "\n";
    echo "Expected total: " . ($soapParties + count($uniqueNames) - $soapOverlap) . "\n";
    echo "Actual total: " . count($dosar->parti) . "\n";
    echo "Difference: " . (($soapParties + count($uniqueNames) - $soapOverlap) - count($dosar->parti)) . "\n\n";
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
