<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <title>Test Funcționalitate Expandare - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 15px 0; background: #f8f9fa; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
        
        .term-results {
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .term-header {
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .term-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .term-content {
            padding: 1.5rem;
            background: white;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Test Funcționalitate Expandare/Restrângere</h1>
        
        <div class="test-section">
            <h3>Butoane de Control</h3>
            <div class="mb-3">
                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="expandAllBtn">
                    <i class="fas fa-expand-alt me-1"></i>
                    Expandează toate
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="collapseAllBtn">
                    <i class="fas fa-compress-alt me-1"></i>
                    Restrânge toate
                </button>
                <button type="button" class="btn btn-sm btn-warning ms-3" onclick="clearActiveFilters()">
                    <i class="fas fa-filter-circle-xmark me-1"></i>
                    Șterge Filtre
                </button>
            </div>
            
            <div id="testResults" class="mt-3"></div>
        </div>

        <div class="test-section">
            <h3>Rezultate Test</h3>
            
            <!-- Simulăm structura de rezultate din index.php -->
            <div class="term-results">
                <div class="term-header" onclick="toggleTermResults(0)">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-search me-2"></i>
                                Căutare pentru: <strong>POPESCU</strong>
                                <span class="badge bg-primary ms-2">5 rezultate</span>
                            </h6>
                        </div>
                        <div>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon0"></i>
                        </div>
                    </div>
                </div>

                <div class="term-content" id="termContent0" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><i class="fas fa-hashtag me-1"></i>Nr. Dosar</th>
                                    <th><i class="fas fa-university me-1"></i>Instanța</th>
                                    <th><i class="fas fa-users me-1"></i>Părți</th>
                                    <th><i class="fas fa-file-text me-1"></i>Obiect</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>123/2024</strong></td>
                                    <td>Judecătoria Sector 1</td>
                                    <td>POPESCU MARIA vs IONESCU GHEORGHE</td>
                                    <td>Acțiune în despăgubiri</td>
                                </tr>
                                <tr>
                                    <td><strong>456/2024</strong></td>
                                    <td>Tribunalul București</td>
                                    <td>POPESCU ADRIAN vs SC TEST SRL</td>
                                    <td>Litigiu comercial</td>
                                </tr>
                                <tr>
                                    <td><strong>789/2024</strong></td>
                                    <td>Curtea de Apel București</td>
                                    <td>POPESCU ELENA vs STATUL ROMÂN</td>
                                    <td>Contencios administrativ</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="term-results">
                <div class="term-header" onclick="toggleTermResults(1)">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-search me-2"></i>
                                Căutare pentru: <strong>IONESCU</strong>
                                <span class="badge bg-primary ms-2">3 rezultate</span>
                            </h6>
                        </div>
                        <div>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon1"></i>
                        </div>
                    </div>
                </div>

                <div class="term-content" id="termContent1" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><i class="fas fa-hashtag me-1"></i>Nr. Dosar</th>
                                    <th><i class="fas fa-university me-1"></i>Instanța</th>
                                    <th><i class="fas fa-users me-1"></i>Părți</th>
                                    <th><i class="fas fa-file-text me-1"></i>Obiect</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>321/2024</strong></td>
                                    <td>Judecătoria Sector 2</td>
                                    <td>IONESCU MARIA vs GEORGESCU ADRIAN</td>
                                    <td>Divorț</td>
                                </tr>
                                <tr>
                                    <td><strong>654/2024</strong></td>
                                    <td>Tribunalul Ilfov</td>
                                    <td>IONESCU GHEORGHE vs PRIMĂRIA VOLUNTARI</td>
                                    <td>Contencios administrativ</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Verificare SessionStorage</h3>
            <button onclick="checkSessionStorage()" class="btn btn-info">Verifică SessionStorage</button>
            <div id="sessionStorageResult" class="mt-2"></div>
        </div>
    </div>

    <script>
        // Notification system
        function showNotification(message, type = 'info') {
            const resultDiv = document.getElementById('testResults');
            const alertClass = type === 'success' ? 'success' : type === 'danger' ? 'error' : type === 'warning' ? 'warning' : 'info';
            
            resultDiv.innerHTML = `<div class="${alertClass}">${message}</div>`;
            
            setTimeout(() => {
                resultDiv.innerHTML = '';
            }, 3000);
        }

        // Clear any active filters that might hide results - GLOBAL FUNCTION
        function clearActiveFilters() {
            try {
                // Clear exact match filter from sessionStorage
                sessionStorage.removeItem('exactMatchFilter');
                
                // Show all rows and cards
                const allRows = document.querySelectorAll('.table tbody tr');
                
                allRows.forEach(row => {
                    row.style.display = '';
                    row.classList.remove('filtered-exact-match');
                });
                
                showNotification('Filtrele active au fost șterse.', 'success');
                console.log('Active filters cleared');
            } catch (error) {
                console.error('Error clearing filters:', error);
                showNotification('Eroare la ștergerea filtrelor.', 'danger');
            }
        }

        // Expand all results function - GLOBAL FUNCTION
        function expandAllResults() {
            try {
                // First clear any active filters that might hide results
                clearActiveFilters();
                
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Expanding all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru expandare.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'block';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-up toggle-icon';
                });

                showNotification('Toate secțiunile au fost expandate.', 'success');
            } catch (error) {
                console.error('Error in expandAllResults:', error);
                showNotification('Eroare la expandarea secțiunilor.', 'danger');
            }
        }

        // Collapse all results function - GLOBAL FUNCTION
        function collapseAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Collapsing all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru restrângere.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'none';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-down toggle-icon';
                });

                showNotification('Toate secțiunile au fost restrânse.', 'info');
            } catch (error) {
                console.error('Error in collapseAllResults:', error);
                showNotification('Eroare la restrângerea secțiunilor.', 'danger');
            }
        }

        // Toggle individual term results - GLOBAL FUNCTION
        function toggleTermResults(index) {
            try {
                const content = document.getElementById('termContent' + index);
                const icon = document.getElementById('toggleIcon' + index);

                console.log('Toggling term results for index:', index);
                console.log('Content element:', content);
                console.log('Icon element:', icon);

                if (!content) {
                    console.error('Content element not found for index:', index);
                    showNotification('Eroare: Secțiunea nu a fost găsită.', 'danger');
                    return;
                }

                if (!icon) {
                    console.warn('Icon element not found for index:', index);
                }

                if (content.style.display === 'none' || content.style.display === '') {
                    content.style.display = 'block';
                    if (icon) {
                        icon.className = 'fas fa-chevron-up toggle-icon';
                    }
                } else {
                    content.style.display = 'none';
                    if (icon) {
                        icon.className = 'fas fa-chevron-down toggle-icon';
                    }
                }
            } catch (error) {
                console.error('Error in toggleTermResults:', error);
                showNotification('Eroare la comutarea secțiunii.', 'danger');
            }
        }

        // Check sessionStorage function
        function checkSessionStorage() {
            const resultDiv = document.getElementById('sessionStorageResult');
            const exactMatchFilter = sessionStorage.getItem('exactMatchFilter');
            
            let html = '<div class="mt-2">';
            html += '<strong>SessionStorage Status:</strong><br>';
            html += `exactMatchFilter: ${exactMatchFilter || 'null'}<br>`;
            
            if (exactMatchFilter === 'true') {
                html += '<div class="warning mt-2">⚠️ Filtrul exact match este activ! Aceasta poate ascunde rezultatele.</div>';
            } else {
                html += '<div class="success mt-2">✓ Nu există filtre active în sessionStorage.</div>';
            }
            
            html += '</div>';
            resultDiv.innerHTML = html;
        }

        // Initialize expand/collapse buttons
        function initExpandCollapseButtons() {
            const expandBtn = document.getElementById('expandAllBtn');
            const collapseBtn = document.getElementById('collapseAllBtn');

            if (expandBtn) {
                expandBtn.addEventListener('click', function() {
                    console.log('Expand All button clicked');
                    expandAllResults();
                });
            }

            if (collapseBtn) {
                collapseBtn.addEventListener('click', function() {
                    console.log('Collapse All button clicked');
                    collapseAllResults();
                });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initExpandCollapseButtons();
            checkSessionStorage();
            
            console.log('Test page initialized');
            showNotification('Pagina de test a fost încărcată. Testați funcționalitatea de expandare/restrângere.', 'info');
        });
    </script>
</body>
</html>
