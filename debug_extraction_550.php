<?php
/**
 * Debug the extraction process for the 550+ party case
 * to see exactly what's happening step by step
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🐛 Debug Extraction Process</h1>\n";
echo "<p><strong>Case:</strong> 130/98/2022 from CurteadeApelBUCURESTI</p>\n";

// Enable detailed debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    $dosarService = new DosarService();
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    echo "<h2>🔍 Step-by-Step Extraction Debug</h2>\n";
    
    // Get raw SOAP response first
    $soapClient = new SoapClient('http://portalquery.just.ro/query.asmx?WSDL');
    
    $params = [
        'institutie' => $institutie,
        'numarDosar' => $numarDosar,
        'anDosar' => null,
        'numarSedinta' => null,
        'dataUltimeiModificari' => null
    ];
    
    $response = $soapClient->CautareDosar($params);
    
    if (isset($response->CautareDosarResult->DosarCautat)) {
        $dosar = $response->CautareDosarResult->DosarCautat;
        
        echo "<h3>📡 SOAP Response Analysis</h3>\n";
        
        // Count SOAP parties
        $soapParties = [];
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $partiArray = is_array($dosar->parti->DosarParte) ? $dosar->parti->DosarParte : [$dosar->parti->DosarParte];
            $soapParties = $partiArray;
        }
        
        echo "<p><strong>SOAP Parties Count:</strong> " . count($soapParties) . "</p>\n";
        
        // Show first few SOAP parties
        echo "<h4>Sample SOAP Parties:</h4>\n";
        echo "<ul>\n";
        for ($i = 0; $i < min(5, count($soapParties)); $i++) {
            $parte = $soapParties[$i];
            echo "<li><strong>" . htmlspecialchars($parte->nume ?? 'N/A') . "</strong> (" . htmlspecialchars($parte->calitate ?? 'N/A') . ")</li>\n";
        }
        echo "</ul>\n";
        
        // Now test decision text extraction manually
        echo "<h3>📝 Decision Text Extraction Test</h3>\n";
        
        $allDecisionTexts = [];
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = is_array($dosar->sedinte->DosarSedinta) ? $dosar->sedinte->DosarSedinta : [$dosar->sedinte->DosarSedinta];
            
            foreach ($sedinte as $sessionIndex => $sedinta) {
                echo "<h4>Session " . ($sessionIndex + 1) . ":</h4>\n";
                
                $textSources = [];
                
                if (isset($sedinta->solutie) && !empty($sedinta->solutie)) {
                    $textSources[] = $sedinta->solutie;
                    echo "<p>✅ Found 'solutie' text (" . strlen($sedinta->solutie) . " chars)</p>\n";
                }
                
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $textSources[] = $sedinta->solutieSumar;
                    echo "<p>✅ Found 'solutieSumar' text (" . strlen($sedinta->solutieSumar) . " chars)</p>\n";
                }
                
                if (empty($textSources)) {
                    echo "<p style='color: #dc3545;'>❌ No decision text found in this session</p>\n";
                    continue;
                }
                
                // Test extraction on each text source
                foreach ($textSources as $textIndex => $solutieText) {
                    echo "<h5>Testing extraction on text source " . ($textIndex + 1) . ":</h5>\n";
                    
                    // Test the actual extraction method
                    $extractedParties = [];
                    
                    // Use reflection to access private method
                    $reflection = new ReflectionClass($dosarService);
                    $method = $reflection->getMethod('extractPartiesFromDecisionText');
                    $method->setAccessible(true);
                    
                    // Create a mock dosar object for testing
                    $mockDosar = new stdClass();
                    $mockDosar->sedinte = new stdClass();
                    $mockDosar->sedinte->DosarSedinta = new stdClass();
                    $mockDosar->sedinte->DosarSedinta->solutie = $solutieText;
                    
                    $extractedParties = $method->invoke($dosarService, $mockDosar);
                    
                    echo "<p><strong>Extracted parties from this text:</strong> " . count($extractedParties) . "</p>\n";
                    
                    if (count($extractedParties) > 0) {
                        echo "<h6>Sample extracted parties:</h6>\n";
                        echo "<ul>\n";
                        for ($i = 0; $i < min(10, count($extractedParties)); $i++) {
                            $party = $extractedParties[$i];
                            echo "<li><strong>" . htmlspecialchars($party['nume']) . "</strong> (" . $party['calitate'] . ") [" . $party['source'] . "]</li>\n";
                        }
                        echo "</ul>\n";
                    } else {
                        echo "<p style='color: #dc3545;'>❌ No parties extracted from this text</p>\n";
                        
                        // Let's test individual patterns manually
                        echo "<h6>Manual pattern testing:</h6>\n";
                        
                        // Test creditor pattern
                        if (preg_match_all('/creditor[ii]?\s+([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+)/ui', $solutieText, $creditorMatches)) {
                            echo "<p>✅ Creditor pattern found " . count($creditorMatches[1]) . " matches</p>\n";
                        } else {
                            echo "<p>❌ No creditor pattern matches</p>\n";
                        }
                        
                        // Test capitalized names
                        if (preg_match_all('/\b([A-ZĂÂÎȘȚăâîșțţşŞţŢ]{2,}(?:\s+[A-ZĂÂÎȘȚăâîșțţşŞţŢ]{2,})+)\b/u', $solutieText, $nameMatches)) {
                            echo "<p>✅ Capitalized names pattern found " . count($nameMatches[1]) . " matches</p>\n";
                            
                            // Show some samples
                            echo "<ul>\n";
                            for ($i = 0; $i < min(5, count($nameMatches[1])); $i++) {
                                echo "<li>" . htmlspecialchars($nameMatches[1][$i]) . "</li>\n";
                            }
                            echo "</ul>\n";
                        } else {
                            echo "<p>❌ No capitalized names found</p>\n";
                        }
                        
                        // Show text sample for manual inspection
                        echo "<h6>Text sample (first 1000 chars):</h6>\n";
                        echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; font-size: 11px; max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6;'>\n";
                        echo htmlspecialchars(substr($solutieText, 0, 1000)) . "\n";
                        echo "</div>\n";
                    }
                }
            }
        } else {
            echo "<p style='color: #dc3545;'>❌ No sessions found in SOAP response</p>\n";
        }
        
        // Now test the full service method
        echo "<h3>🔄 Full Service Method Test</h3>\n";
        
        $fullResult = $dosarService->getDetaliiDosar($numarDosar, $institutie);
        
        if ($fullResult && !empty($fullResult->numar)) {
            $totalParties = count($fullResult->parti ?? []);
            echo "<p><strong>Total parties from full service:</strong> {$totalParties}</p>\n";
            
            // Count by source
            $soapCount = 0;
            $decisionCount = 0;
            
            foreach ($fullResult->parti as $parte) {
                if ($parte->source === 'soap_api') {
                    $soapCount++;
                } elseif ($parte->source === 'decision_text') {
                    $decisionCount++;
                }
            }
            
            echo "<p><strong>SOAP API parties:</strong> {$soapCount}</p>\n";
            echo "<p><strong>Decision text parties:</strong> {$decisionCount}</p>\n";
            
            if ($decisionCount > 0) {
                echo "<h4>Sample decision text parties:</h4>\n";
                echo "<ul>\n";
                $count = 0;
                foreach ($fullResult->parti as $parte) {
                    if ($parte->source === 'decision_text' && $count < 10) {
                        echo "<li><strong>" . htmlspecialchars($parte->nume) . "</strong> (" . $parte->calitate . ")</li>\n";
                        $count++;
                    }
                }
                echo "</ul>\n";
            }
        }
        
    } else {
        echo "<p style='color: #dc3545;'><strong>❌ Case not found in SOAP response</strong></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<h2>📋 Summary</h2>\n";
echo "<p>This debug script helps identify:</p>\n";
echo "<ul>\n";
echo "<li>Whether SOAP API is returning the expected ~100 parties</li>\n";
echo "<li>Whether decision text is available in the sessions</li>\n";
echo "<li>Whether extraction patterns are finding matches in the text</li>\n";
echo "<li>Whether the full service method is combining results correctly</li>\n";
echo "</ul>\n";
?>
