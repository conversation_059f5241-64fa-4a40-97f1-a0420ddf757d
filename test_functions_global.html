<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Funcții Globale - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-success { color: #28a745; }
        .status-danger { color: #dc3545; }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-cogs me-2"></i>
            Test Funcții Globale JavaScript
        </h1>
        
        <div class="test-section">
            <h3><i class="fas fa-check-circle me-2"></i>Test Accesibilitate Funcții</h3>
            <div id="functionTests">
                <p>Se testează accesibilitatea funcțiilor...</p>
            </div>
            
            <button type="button" class="btn btn-primary me-2" onclick="testExpandFunction()">
                <i class="fas fa-expand-alt me-1"></i>
                Test expandAllResults()
            </button>
            
            <button type="button" class="btn btn-secondary me-2" onclick="testCollapseFunction()">
                <i class="fas fa-compress-alt me-1"></i>
                Test collapseAllResults()
            </button>
            
            <button type="button" class="btn btn-info" onclick="testToggleFunction()">
                <i class="fas fa-exchange-alt me-1"></i>
                Test toggleTermResults(0)
            </button>
            
            <div class="console-output" id="consoleOutput">
                <div>Test Console - Ready</div>
            </div>
        </div>
        
        <!-- Test Elements -->
        <div class="test-section">
            <h3><i class="fas fa-sitemap me-2"></i>Elemente Test</h3>
            
            <div class="term-results">
                <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-light border rounded" onclick="toggleTermResults(0)">
                    <div>
                        <h6 class="mb-1">Secțiune Test 1</h6>
                        <small class="text-muted">Click pentru toggle</small>
                    </div>
                    <i class="fas fa-chevron-down toggle-icon" id="toggleIcon0"></i>
                </div>
                
                <div id="termContent0" style="display: none;" class="p-3 border rounded">
                    <p>Conținut test pentru secțiunea 1</p>
                    <p>Această secțiune poate fi expandată/restrânsă</p>
                </div>
            </div>
            
            <div class="term-results mt-3">
                <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-light border rounded" onclick="toggleTermResults(1)">
                    <div>
                        <h6 class="mb-1">Secțiune Test 2</h6>
                        <small class="text-muted">Click pentru toggle</small>
                    </div>
                    <i class="fas fa-chevron-down toggle-icon" id="toggleIcon1"></i>
                </div>
                
                <div id="termContent1" style="display: none;" class="p-3 border rounded">
                    <p>Conținut test pentru secțiunea 2</p>
                    <p>Această secțiune poate fi expandată/restrânsă</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console logging function
        function logToConsole(message, type = 'info') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const colorMap = {
                'info': '#0f0',
                'warning': '#ff0',
                'error': '#f00',
                'success': '#0f0'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colorMap[type] || '#0f0';
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        // Notification system
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            
            const typeClasses = {
                'success': 'alert-success',
                'danger': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            };
            
            const icons = {
                'success': 'fa-check-circle',
                'danger': 'fa-exclamation-triangle',
                'warning': 'fa-exclamation-circle',
                'info': 'fa-info-circle'
            };
            
            notification.className = `alert ${typeClasses[type]} alert-dismissible fade show`;
            notification.innerHTML = `
                <i class="fas ${icons[type]} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            container.appendChild(notification);
            logToConsole(`Notification: ${message}`, type);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Test functions from index.php
        function toggleTermResults(index) {
            try {
                const content = document.getElementById('termContent' + index);
                const icon = document.getElementById('toggleIcon' + index);

                logToConsole(`Toggling term results for index: ${index}`);

                if (!content) {
                    logToConsole(`ERROR: Content element not found for index: ${index}`, 'error');
                    showNotification('Eroare: Secțiunea nu a fost găsită.', 'danger');
                    return;
                }

                if (!icon) {
                    logToConsole(`WARNING: Icon element not found for index: ${index}`, 'warning');
                }

                const isVisible = content.style.display !== 'none';

                if (isVisible) {
                    content.style.display = 'none';
                    if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                    logToConsole(`Collapsed section ${index}`, 'success');
                } else {
                    content.style.display = 'block';
                    if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                    logToConsole(`Expanded section ${index}`, 'success');
                }

            } catch (error) {
                logToConsole(`ERROR in toggleTermResults: ${error.message}`, 'error');
                showNotification('Eroare la comutarea secțiunii.', 'danger');
            }
        }

        function expandAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                logToConsole(`Expanding all results - found ${termContents.length} content elements and ${toggleIcons.length} icon elements`);

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru expandare.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'block';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-up toggle-icon';
                });

                showNotification('Toate secțiunile au fost expandate.', 'info');
                logToConsole(`Successfully expanded ${termContents.length} sections`, 'success');

            } catch (error) {
                logToConsole(`ERROR in expandAllResults: ${error.message}`, 'error');
                showNotification('Eroare la expandarea rezultatelor.', 'danger');
            }
        }

        function collapseAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                logToConsole(`Collapsing all results - found ${termContents.length} content elements and ${toggleIcons.length} icon elements`);

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru restrângere.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'none';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-down toggle-icon';
                });

                showNotification('Toate secțiunile au fost restrânse.', 'info');
                logToConsole(`Successfully collapsed ${termContents.length} sections`, 'success');

            } catch (error) {
                logToConsole(`ERROR in collapseAllResults: ${error.message}`, 'error');
                showNotification('Eroare la restrângerea rezultatelor.', 'danger');
            }
        }
        
        // Test function accessibility
        function testExpandFunction() {
            logToConsole('Testing expandAllResults function...', 'info');
            if (typeof expandAllResults === 'function') {
                logToConsole('✓ expandAllResults is accessible', 'success');
                expandAllResults();
            } else {
                logToConsole('✗ expandAllResults is not accessible', 'error');
                showNotification('Funcția expandAllResults nu este accesibilă!', 'danger');
            }
        }
        
        function testCollapseFunction() {
            logToConsole('Testing collapseAllResults function...', 'info');
            if (typeof collapseAllResults === 'function') {
                logToConsole('✓ collapseAllResults is accessible', 'success');
                collapseAllResults();
            } else {
                logToConsole('✗ collapseAllResults is not accessible', 'error');
                showNotification('Funcția collapseAllResults nu este accesibilă!', 'danger');
            }
        }
        
        function testToggleFunction() {
            logToConsole('Testing toggleTermResults function...', 'info');
            if (typeof toggleTermResults === 'function') {
                logToConsole('✓ toggleTermResults is accessible', 'success');
                toggleTermResults(0);
            } else {
                logToConsole('✗ toggleTermResults is not accessible', 'error');
                showNotification('Funcția toggleTermResults nu este accesibilă!', 'danger');
            }
        }
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('DOM loaded, running function accessibility tests...', 'info');
            
            const functionTests = document.getElementById('functionTests');
            let results = '<h5>Rezultate Test:</h5>';
            
            // Test function accessibility
            const functions = [
                'expandAllResults',
                'collapseAllResults', 
                'toggleTermResults',
                'showNotification'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results += `<p class="status-success"><i class="fas fa-check me-2"></i>Funcția <code>${func}</code> este accesibilă</p>`;
                    logToConsole(`✓ Function ${func} is accessible`, 'success');
                } else {
                    results += `<p class="status-danger"><i class="fas fa-times me-2"></i>Funcția <code>${func}</code> nu este accesibilă</p>`;
                    logToConsole(`✗ Function ${func} is not accessible`, 'error');
                }
            });
            
            functionTests.innerHTML = results;
            logToConsole('Function accessibility tests completed', 'success');
            showNotification('Test de accesibilitate funcții completat!', 'success');
        });
    </script>
</body>
</html>
