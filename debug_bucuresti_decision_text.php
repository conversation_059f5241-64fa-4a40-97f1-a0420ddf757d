<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 DEBUGGING CurteadeApelBUCURESTI DECISION TEXT\n";
echo "===============================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current parties: " . count($dosar->parti) . "\n\n";
    
    // Examine all session data
    echo "📋 SESSION DATA ANALYSIS:\n";
    echo "========================\n\n";
    
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        echo "Number of sessions: " . count($dosar->sedinte) . "\n\n";
        
        foreach ($dosar->sedinte as $i => $sedinta) {
            echo "Session " . ($i + 1) . ":\n";
            echo "  Data: " . ($sedinta['data'] ?? 'N/A') . "\n";
            echo "  Ora: " . ($sedinta['ora'] ?? 'N/A') . "\n";
            echo "  Complet: " . ($sedinta['complet'] ?? 'N/A') . "\n";
            echo "  Solutie length: " . strlen($sedinta['solutie'] ?? '') . " chars\n";
            echo "  SolutieSumar length: " . strlen($sedinta['solutieSumar'] ?? '') . " chars\n";
            
            if (!empty($sedinta['solutie'])) {
                echo "  Solutie content:\n";
                echo "    \"" . substr($sedinta['solutie'], 0, 200) . "...\"\n";
            }
            
            if (!empty($sedinta['solutieSumar'])) {
                echo "  SolutieSumar content:\n";
                echo "    \"" . substr($sedinta['solutieSumar'], 0, 200) . "...\"\n";
            }
            
            echo "\n";
        }
    } else {
        echo "❌ No sessions found or sessions not in expected format\n";
    }
    
    // Check if we can get raw SOAP data to see if there are more parties available
    echo "📋 RAW SOAP ANALYSIS:\n";
    echo "====================\n\n";
    
    // Let's try to get the raw SOAP response to see what's available
    try {
        $searchParams = [
            'numarDosar' => '130/98/2022',
            'institutie' => 'CurteadeApelBUCURESTI',
            'obiectDosar' => '',
            'numeParte' => '',
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        // Use reflection to access the private method
        $reflection = new ReflectionClass($dosarService);
        $method = $reflection->getMethod('executeSoapCallWithRetry');
        $method->setAccessible(true);
        
        $response = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Debug call");
        
        if (isset($response->CautareDosare2Result->Dosar)) {
            $rawDosar = $response->CautareDosare2Result->Dosar;
            if (is_array($rawDosar)) {
                $rawDosar = $rawDosar[0]; // Take first match
            }
            
            echo "Raw SOAP parties analysis:\n";
            if (isset($rawDosar->parti) && isset($rawDosar->parti->DosarParte)) {
                $rawParti = $rawDosar->parti->DosarParte;
                if (!is_array($rawParti)) {
                    $rawParti = [$rawParti];
                }
                echo "  Raw SOAP parties count: " . count($rawParti) . "\n";
                
                // Show first few parties
                echo "  First 5 raw SOAP parties:\n";
                for ($i = 0; $i < min(5, count($rawParti)); $i++) {
                    $parte = $rawParti[$i];
                    echo "    " . ($i + 1) . ". " . ($parte->nume ?? 'N/A') . " (" . ($parte->calitateParte ?? 'N/A') . ")\n";
                }
            } else {
                echo "  ❌ No raw SOAP parties found\n";
            }
            
            echo "\nRaw SOAP sessions analysis:\n";
            if (isset($rawDosar->sedinte) && isset($rawDosar->sedinte->DosarSedinta)) {
                $rawSedinte = $rawDosar->sedinte->DosarSedinta;
                if (!is_array($rawSedinte)) {
                    $rawSedinte = [$rawSedinte];
                }
                echo "  Raw SOAP sessions count: " . count($rawSedinte) . "\n";
                
                foreach ($rawSedinte as $i => $sedinta) {
                    echo "  Session " . ($i + 1) . ":\n";
                    echo "    Solutie length: " . strlen($sedinta->solutie ?? '') . " chars\n";
                    echo "    SolutieSumar length: " . strlen($sedinta->solutieSumar ?? '') . " chars\n";
                    
                    if (!empty($sedinta->solutie)) {
                        echo "    Solutie preview: \"" . substr($sedinta->solutie, 0, 100) . "...\"\n";
                    }
                }
            } else {
                echo "  ❌ No raw SOAP sessions found\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error accessing raw SOAP data: " . $e->getMessage() . "\n";
    }
    
    echo "\n✅ Debug analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
