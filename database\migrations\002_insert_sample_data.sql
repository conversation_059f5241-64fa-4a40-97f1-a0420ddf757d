-- <PERSON> Judiciar - Sample Data for Testing
-- Created: 2025-07-03
-- Description: Insert sample data for development and testing

USE portal_judiciar;

-- =====================================================
-- SAMPLE USERS
-- =====================================================
INSERT INTO users (
    email, 
    password_hash, 
    first_name, 
    last_name, 
    phone, 
    email_verified, 
    gdpr_consent, 
    gdpr_consent_date, 
    gdpr_consent_ip,
    notification_preferences
) VALUES 
(
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'Ion',
    'Popescu',
    '+40721234567',
    TRUE,
    TRUE,
    NOW(),
    '127.0.0.1',
    JSON_OBJECT(
        'immediate_notifications', true,
        'daily_digest', true,
        'weekly_summary', false,
        'email_format', 'html'
    )
),
(
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'Maria',
    'Ionescu',
    '+40722345678',
    TRUE,
    TRUE,
    NOW(),
    '127.0.0.1',
    JSON_OBJECT(
        'immediate_notifications', false,
        'daily_digest', true,
        'weekly_summary', true,
        'email_format', 'text'
    )
),
(
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'Administrator',
    'Portal',
    '+40723456789',
    TRUE,
    TRUE,
    NOW(),
    '127.0.0.1',
    JSON_OBJECT(
        'immediate_notifications', true,
        'daily_digest', true,
        'weekly_summary', true,
        'email_format', 'html'
    )
);

-- =====================================================
-- SAMPLE MONITORED CASES
-- =====================================================
INSERT INTO monitored_cases (
    user_id,
    case_number,
    institution_code,
    institution_name,
    case_object,
    monitoring_reason,
    notification_frequency,
    last_checked,
    is_active
) VALUES 
(
    1,
    '1234/2024',
    'TribunalulBUCURESTI',
    'Tribunalul București',
    'Acțiune în răspundere civilă delictuală',
    'Monitorizez acest dosar pentru clientul meu',
    'daily',
    DATE_SUB(NOW(), INTERVAL 2 HOUR),
    TRUE
),
(
    1,
    '5678/2024',
    'JudecatoriaBUCURESTI',
    'Judecătoria București',
    'Divorț prin acordul părților',
    'Dosar personal',
    'immediate',
    DATE_SUB(NOW(), INTERVAL 1 HOUR),
    TRUE
),
(
    2,
    '9876/2024',
    'CurteadeApelBUCURESTI',
    'Curtea de Apel București',
    'Apel în materie comercială',
    'Urmăresc evoluția procesului pentru firma mea',
    'weekly',
    DATE_SUB(NOW(), INTERVAL 30 MINUTE),
    TRUE
),
(
    2,
    '1111/2024',
    'TribunalulCLUJ',
    'Tribunalul Cluj',
    'Acțiune în anulare contract',
    'Caz de interes profesional',
    'daily',
    DATE_SUB(NOW(), INTERVAL 45 MINUTE),
    TRUE
),
(
    3,
    '2222/2024',
    'InaltaCurtedeCasatiesiJustitie',
    'Înalta Curte de Casație și Justiție',
    'Recurs în materie penală',
    'Monitorizare pentru statistici',
    'immediate',
    DATE_SUB(NOW(), INTERVAL 15 MINUTE),
    TRUE
);

-- =====================================================
-- SAMPLE CASE SNAPSHOTS
-- =====================================================
INSERT INTO case_snapshots (
    monitored_case_id,
    snapshot_data,
    snapshot_hash,
    case_status,
    case_stage,
    next_hearing_date,
    last_modification_date
) VALUES 
(
    1,
    JSON_OBJECT(
        'numar', '1234/2024',
        'institutie', 'TribunalulBUCURESTI',
        'departament', 'Civil',
        'categorieCaz', 'Civil',
        'stadiuProcesual', 'În curs de judecată',
        'obiect', 'Acțiune în răspundere civilă delictuală',
        'parti', JSON_ARRAY(
            JSON_OBJECT('nume', 'POPESCU ION', 'calitate', 'Reclamant'),
            JSON_OBJECT('nume', 'IONESCU MARIA', 'calitate', 'Pârât')
        ),
        'sedinte', JSON_ARRAY(
            JSON_OBJECT(
                'data', '2024-07-15',
                'ora', '10:00',
                'complet', 'Judecător: Georgescu Ana',
                'solutie', ''
            )
        )
    ),
    SHA256('1234/2024_TribunalulBUCURESTI_2024-07-03_10:00'),
    'În curs de judecată',
    'Fond',
    '2024-07-15',
    '2024-07-03 10:00:00'
),
(
    2,
    JSON_OBJECT(
        'numar', '5678/2024',
        'institutie', 'JudecatoriaBUCURESTI',
        'departament', 'Civil',
        'categorieCaz', 'Familie',
        'stadiuProcesual', 'Soluționat',
        'obiect', 'Divorț prin acordul părților',
        'parti', JSON_ARRAY(
            JSON_OBJECT('nume', 'MARINESCU PAUL', 'calitate', 'Reclamant'),
            JSON_OBJECT('nume', 'MARINESCU ELENA', 'calitate', 'Pârât')
        ),
        'sedinte', JSON_ARRAY(
            JSON_OBJECT(
                'data', '2024-06-20',
                'ora', '14:00',
                'complet', 'Judecător: Vasilescu Mihai',
                'solutie', 'Admite cererea de divorț'
            )
        )
    ),
    SHA256('5678/2024_JudecatoriaBUCURESTI_2024-06-20_14:00'),
    'Soluționat',
    'Definitiv',
    NULL,
    '2024-06-20 14:30:00'
);

-- =====================================================
-- SAMPLE CASE CHANGES
-- =====================================================
INSERT INTO case_changes (
    monitored_case_id,
    old_snapshot_id,
    new_snapshot_id,
    change_type,
    change_description,
    change_details
) VALUES 
(
    1,
    NULL,
    1,
    'other',
    'Dosar adăugat în monitorizare',
    JSON_OBJECT(
        'action', 'case_added',
        'timestamp', '2024-07-03 10:00:00'
    )
),
(
    2,
    NULL,
    2,
    'status',
    'Dosarul a fost soluționat - Admite cererea de divorț',
    JSON_OBJECT(
        'old_status', 'În curs de judecată',
        'new_status', 'Soluționat',
        'solution', 'Admite cererea de divorț',
        'timestamp', '2024-06-20 14:30:00'
    )
);

-- =====================================================
-- SAMPLE NOTIFICATION QUEUE
-- =====================================================
INSERT INTO notification_queue (
    user_id,
    monitored_case_id,
    case_change_id,
    notification_type,
    email_subject,
    email_body,
    email_html_body,
    priority,
    status,
    scheduled_for
) VALUES 
(
    1,
    1,
    1,
    'immediate',
    'Dosar 1234/2024 - Adăugat în monitorizare',
    'Dosarul 1234/2024 de la Tribunalul București a fost adăugat cu succes în lista de monitorizare.',
    '<p>Dosarul <strong>1234/2024</strong> de la <em>Tribunalul București</em> a fost adăugat cu succes în lista de monitorizare.</p>',
    1,
    'sent',
    DATE_SUB(NOW(), INTERVAL 2 HOUR)
),
(
    2,
    2,
    2,
    'immediate',
    'Dosar 5678/2024 - Soluționat',
    'Dosarul 5678/2024 de la Judecătoria București a fost soluționat: Admite cererea de divorț.',
    '<p>Dosarul <strong>5678/2024</strong> de la <em>Judecătoria București</em> a fost soluționat:</p><p><strong>Soluție:</strong> Admite cererea de divorț</p>',
    1,
    'sent',
    DATE_SUB(NOW(), INTERVAL 1 HOUR)
),
(
    1,
    1,
    NULL,
    'daily_digest',
    'Raport zilnic - Dosarele monitorizate',
    'Raportul zilnic pentru dosarele monitorizate.',
    '<h2>Raport zilnic - Dosarele monitorizate</h2><p>Aveți 2 dosare în monitorizare.</p>',
    5,
    'pending',
    DATE_ADD(NOW(), INTERVAL 1 DAY)
);

-- =====================================================
-- SAMPLE SYSTEM LOGS
-- =====================================================
INSERT INTO system_logs (
    level,
    message,
    context,
    user_id,
    ip_address,
    user_agent
) VALUES 
(
    'info',
    'User logged in successfully',
    JSON_OBJECT('user_email', '<EMAIL>', 'login_method', 'email'),
    1,
    '127.0.0.1',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
),
(
    'info',
    'Case added to monitoring',
    JSON_OBJECT('case_number', '1234/2024', 'institution', 'TribunalulBUCURESTI'),
    1,
    '127.0.0.1',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
),
(
    'warning',
    'SOAP API timeout during case check',
    JSON_OBJECT('case_number', '9876/2024', 'timeout_seconds', 30),
    NULL,
    NULL,
    'Cron Job'
),
(
    'info',
    'Daily digest email sent',
    JSON_OBJECT('recipient', '<EMAIL>', 'cases_count', 2),
    1,
    NULL,
    'Email Service'
);
