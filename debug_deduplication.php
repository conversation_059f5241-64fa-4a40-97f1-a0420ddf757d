<?php
// Debug the deduplication issue
require_once 'bootstrap.php';

use App\Services\DosarService;

echo "<h1>🔍 Debug Deduplication Issue</h1>";

$searchTerm = "14096/3/2024*";

try {
    $dosarService = new DosarService();
    
    echo "<h2>Step 1: Direct Database Query</h2>";
    
    // Let's check what's actually in the database
    $pdo = new PDO('mysql:host=localhost;dbname=portal_judiciar;charset=utf8mb4', 'root', '');
    
    // Search for all cases that match the pattern
    $sql = "SELECT numar, institutie, data, obiect, materie, stadiu 
            FROM dosare 
            WHERE numar LIKE :pattern 
            ORDER BY numar";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['pattern' => '14096/3/2024%']);
    $dbResults = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
    echo "<strong>Direct database results for pattern '14096/3/2024%':</strong><br>";
    echo "<strong>Total found:</strong> " . count($dbResults) . "<br><br>";
    
    foreach ($dbResults as $index => $row) {
        $hasAsterisk = strpos($row->numar, '*') !== false;
        echo "<div style='background: " . ($hasAsterisk ? "#fff3cd" : "#e7f3ff") . "; padding: 5px; margin: 2px 0; border: 1px solid " . ($hasAsterisk ? "#ffc107" : "#007bff") . ";'>";
        echo "<strong>DB Result #" . ($index + 1) . ":</strong><br>";
        echo "Case Number: " . ($row->numar ?? 'NULL') . "<br>";
        echo "Institution: " . ($row->institutie ?? 'NULL') . "<br>";
        echo "Date: " . ($row->data ?? 'NULL') . "<br>";
        echo "Object: " . substr($row->obiect ?? 'NULL', 0, 100) . "...<br>";
        if ($hasAsterisk) {
            echo "<strong style='color: #856404;'>🌟 This has the literal asterisk!</strong><br>";
        }
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>Step 2: DosarService Search</h2>";
    
    // Now test the DosarService search
    $serviceResults = $dosarService->cautareAvansata(['numarDosar' => $searchTerm]);
    
    echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
    echo "<strong>DosarService results for '$searchTerm':</strong><br>";
    echo "<strong>Total found:</strong> " . count($serviceResults) . "<br><br>";
    
    foreach ($serviceResults as $index => $dosar) {
        $hasAsterisk = strpos($dosar->numar, '*') !== false;
        echo "<div style='background: " . ($hasAsterisk ? "#fff3cd" : "#f8f9fa") . "; padding: 5px; margin: 2px 0; border: 1px solid " . ($hasAsterisk ? "#ffc107" : "#dee2e6") . ";'>";
        echo "<strong>Service Result #" . ($index + 1) . ":</strong><br>";
        echo "Case Number: " . ($dosar->numar ?? 'NULL') . "<br>";
        echo "Institution: " . ($dosar->institutie ?? 'NULL') . "<br>";
        echo "Date: " . ($dosar->data ?? 'NULL') . "<br>";
        echo "Object: " . substr($dosar->obiect ?? 'NULL', 0, 100) . "...<br>";
        if ($hasAsterisk) {
            echo "<strong style='color: #856404;'>🌟 This has the literal asterisk!</strong><br>";
        }
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>Step 3: Deduplication Key Analysis</h2>";
    
    // Analyze the deduplication keys
    echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
    echo "<strong>Deduplication Key Analysis:</strong><br>";
    echo "The mergeAndDeduplicateResults function uses this key: numar + '|' + institutie<br><br>";
    
    $keys = [];
    foreach ($serviceResults as $index => $dosar) {
        $key = ($dosar->numar ?? '') . '|' . ($dosar->institutie ?? '');
        $keys[] = $key;
        
        echo "<strong>Result #" . ($index + 1) . ":</strong><br>";
        echo "Case Number: '" . ($dosar->numar ?? 'NULL') . "'<br>";
        echo "Institution: '" . ($dosar->institutie ?? 'NULL') . "'<br>";
        echo "Deduplication Key: '$key'<br>";
        echo "<hr style='margin: 5px 0;'>";
    }
    
    $uniqueKeys = array_unique($keys);
    echo "<strong>Unique keys:</strong> " . count($uniqueKeys) . " out of " . count($keys) . " total<br>";
    
    if (count($keys) != count($uniqueKeys)) {
        echo "<strong style='color: red;'>⚠️ DUPLICATES DETECTED!</strong><br>";
        $duplicateKeys = array_diff_assoc($keys, $uniqueKeys);
        echo "Duplicate keys: " . implode(', ', $duplicateKeys) . "<br>";
    } else {
        echo "<strong style='color: green;'>✅ No duplicates in deduplication keys</strong><br>";
    }
    echo "</div>";
    
    echo "<h2>Step 4: Test Different Search Approaches</h2>";
    
    // Test exact search for the asterisk case
    echo "<h3>4a. Exact search for '14096/3/2024*':</h3>";
    $exactResults = $dosarService->cautareAvansata(['numarDosar' => '14096/3/2024*']);
    echo "<p>Results: " . count($exactResults) . "</p>";
    
    // Test search without asterisk
    echo "<h3>4b. Search for '14096/3/2024' (without asterisk):</h3>";
    $noAsteriskResults = $dosarService->cautareAvansata(['numarDosar' => '14096/3/2024']);
    echo "<p>Results: " . count($noAsteriskResults) . "</p>";
    
    // Test wildcard search
    echo "<h3>4c. Search for '14096/3/2024%' (SQL wildcard):</h3>";
    $wildcardResults = $dosarService->cautareAvansata(['numarDosar' => '14096/3/2024%']);
    echo "<p>Results: " . count($wildcardResults) . "</p>";
    
    echo "<h2>Step 5: Root Cause Analysis</h2>";
    
    $totalDbResults = count($dbResults);
    $totalServiceResults = count($serviceResults);
    
    if ($totalDbResults > $totalServiceResults) {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
        echo "<strong>❌ PROBLEM IDENTIFIED:</strong><br>";
        echo "Database has $totalDbResults results, but DosarService returns only $totalServiceResults<br>";
        echo "This suggests the deduplication logic is removing valid results!<br>";
        echo "</div>";
        
        echo "<h3>Recommended Fix:</h3>";
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
        echo "<strong>Solution:</strong><br>";
        echo "1. Modify the deduplication key to include more unique identifiers<br>";
        echo "2. Or disable deduplication for wildcard searches<br>";
        echo "3. Or use a more sophisticated deduplication that preserves all valid cases<br>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
        echo "<strong>✅ Backend seems OK:</strong><br>";
        echo "Database and DosarService return the same number of results<br>";
        echo "The issue might be in the frontend display logic<br>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🎯 Summary</h2>";
echo "<p>This test will help identify if the issue is:</p>";
echo "<ul>";
echo "<li><strong>Database level:</strong> Not enough records in the database</li>";
echo "<li><strong>Service level:</strong> Deduplication removing valid results</li>";
echo "<li><strong>Frontend level:</strong> Display logic hiding results</li>";
echo "</ul>";
?>
