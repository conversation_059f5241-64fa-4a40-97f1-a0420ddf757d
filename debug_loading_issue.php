<?php
/**
 * Debug Loading Issue - TribunalulIALOMITA Case
 * Investigates why case 130/98/2022 from TribunalulIALOMITA is not loading
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Debug Loading Issue - Romanian Judicial Portal</title>";
echo "<meta charset='UTF-8'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
.container { max-width: 1200px; margin: 0 auto; }
.section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.header { background: linear-gradient(135deg, #dc3545, #c82333); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
.metric-card { background: #f8f9fa; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0; }
.timing-info { background: #f8d7da; border: 1px solid #dc3545; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap; border-radius: 4px; }
.success { background: #d4edda; border-left: 4px solid #28a745; }
.warning { background: #fff3cd; border-left: 4px solid #ffc107; }
.critical { background: #f8d7da; border-left: 4px solid #dc3545; }
.comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
.comparison-table th, .comparison-table td { padding: 12px; border: 1px solid #ddd; text-align: left; }
.comparison-table th { background-color: #dc3545; color: white; }
.debug-output { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 11px; max-height: 300px; overflow-y: auto; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🔍 Debug Loading Issue</h1>";
echo "<p>Investigating case 130/98/2022 from TribunalulIALOMITA</p>";
echo "<p><strong>Problem:</strong> Page gets stuck on loading message</p>";
echo "</div>";

// Test cases for comparison
$testCases = [
    [
        'name' => 'Working Case (CurteadeApelBUCURESTI)',
        'number' => '130/98/2022',
        'institution' => 'CurteadeApelBUCURESTI',
        'expected' => 'Should work (recently optimized)'
    ],
    [
        'name' => 'Problem Case (TribunalulIALOMITA)',
        'number' => '130/98/2022',
        'institution' => 'TribunalulIALOMITA',
        'expected' => 'Gets stuck on loading'
    ]
];

echo "<div class='section'>";
echo "<h2>🧪 SOAP API Response Testing</h2>";

foreach ($testCases as $index => $testCase) {
    echo "<div class='metric-card " . ($index == 0 ? 'success' : 'critical') . "'>";
    echo "<h4>📋 {$testCase['name']}</h4>";
    echo "<p><strong>Case:</strong> {$testCase['number']} from {$testCase['institution']}</p>";
    echo "<p><strong>Expected:</strong> {$testCase['expected']}</p>";
    
    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);
    
    try {
        $dosarService = new DosarService();
        
        // Test SOAP API call
        echo "<div class='debug-output'>";
        echo "Testing SOAP API call...\n";
        
        $soapStartTime = microtime(true);
        $dosare = $dosarService->cautareDupaNumarDosar(
            $testCase['number'],
            $testCase['institution'],
            '', // obiectDosar
            '', // dataInceput
            ''  // dataSfarsit
        );
        $soapEndTime = microtime(true);
        
        $soapTime = ($soapEndTime - $soapStartTime) * 1000;
        
        echo "SOAP API Response Time: " . round($soapTime, 2) . " ms\n";
        echo "Response Status: " . (empty($dosare) ? "❌ No data returned" : "✅ Data received") . "\n";
        
        if (!empty($dosare)) {
            $dosar = $dosare[0];
            echo "Case Found: ✅ YES\n";
            echo "Case Number: " . ($dosar->numarDosar ?? 'N/A') . "\n";
            echo "Institution: " . ($dosar->instanta ?? 'N/A') . "\n";
            echo "Parties Count: " . count($dosar->parti ?? []) . "\n";
            
            // Check for specific issues
            $parties = $dosar->parti ?? [];
            $soapParties = 0;
            $decisionParties = 0;
            $unknownSources = 0;
            
            foreach ($parties as $party) {
                if (isset($party->source)) {
                    if ($party->source === 'soap_api') {
                        $soapParties++;
                    } elseif ($party->source === 'decision_text') {
                        $decisionParties++;
                    } else {
                        $unknownSources++;
                    }
                } else {
                    $unknownSources++;
                }
            }
            
            echo "SOAP API Parties: $soapParties\n";
            echo "Decision Text Parties: $decisionParties\n";
            echo "Unknown Source Parties: $unknownSources\n";
            
            // Check for potential issues
            $totalParties = count($parties);
            if ($totalParties > 500) {
                echo "⚠️ WARNING: Large number of parties ($totalParties) - potential performance issue\n";
            }
            
            // Check decision text
            if (isset($dosar->textDecizie) && !empty($dosar->textDecizie)) {
                $textLength = strlen($dosar->textDecizie);
                echo "Decision Text Length: " . number_format($textLength) . " characters\n";
                if ($textLength > 100000) {
                    echo "⚠️ WARNING: Very large decision text - potential parsing issue\n";
                }
            } else {
                echo "Decision Text: ❌ Not available\n";
            }
            
        } else {
            echo "Case Found: ❌ NO\n";
            echo "Possible Issues:\n";
            echo "- Case number doesn't exist in this institution\n";
            echo "- Institution name encoding issue\n";
            echo "- SOAP API connection problem\n";
            echo "- Institution code mapping issue\n";
        }
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='debug-output'>";
        echo "❌ EXCEPTION CAUGHT:\n";
        echo "Error: " . $e->getMessage() . "\n";
        echo "File: " . $e->getFile() . "\n";
        echo "Line: " . $e->getLine() . "\n";
        echo "Stack Trace:\n" . $e->getTraceAsString() . "\n";
        echo "</div>";
    }
    
    $endTime = microtime(true);
    $endMemory = memory_get_usage(true);
    
    $totalTime = ($endTime - $startTime) * 1000;
    $totalMemory = ($endMemory - $startMemory) / 1024 / 1024;
    
    echo "<div class='timing-info'>";
    echo "Total Processing Time: " . round($totalTime, 2) . " ms\n";
    echo "Memory Usage: " . round($totalMemory, 2) . " MB\n";
    echo "Status: " . ($totalTime < 5000 ? "✅ Normal" : "❌ Slow") . "\n";
    echo "</div>";
    
    echo "</div>";
}

echo "</div>";

// Institution comparison
echo "<div class='section'>";
echo "<h2>🏛️ Institution Comparison</h2>";

$institutions = [
    'CurteadeApelBUCURESTI' => 'Working institution',
    'TribunalulIALOMITA' => 'Problem institution',
    'TribunalulBUCURESTI' => 'Test institution',
    'JudecatoriaBUCURESTI' => 'Test institution'
];

echo "<table class='comparison-table'>";
echo "<tr><th>Institution</th><th>Status</th><th>Test Result</th><th>Response Time</th><th>Notes</th></tr>";

foreach ($institutions as $institution => $description) {
    echo "<tr>";
    echo "<td><strong>$institution</strong><br><small>$description</small></td>";
    
    $instStartTime = microtime(true);
    
    try {
        $dosarService = new DosarService();
        $testResult = $dosarService->cautareDupaNumarDosar('130/98/2022', $institution, '', '', '');
        
        $instEndTime = microtime(true);
        $instTime = ($instEndTime - $instStartTime) * 1000;
        
        if (!empty($testResult)) {
            echo "<td style='background-color: #d4edda;'>✅ Working</td>";
            echo "<td>Data found (" . count($testResult[0]->parti ?? []) . " parties)</td>";
            echo "<td>" . round($instTime, 2) . " ms</td>";
            echo "<td>Normal operation</td>";
        } else {
            echo "<td style='background-color: #fff3cd;'>⚠️ No Data</td>";
            echo "<td>No case found</td>";
            echo "<td>" . round($instTime, 2) . " ms</td>";
            echo "<td>Case may not exist in this institution</td>";
        }
        
    } catch (Exception $e) {
        $instEndTime = microtime(true);
        $instTime = ($instEndTime - $instStartTime) * 1000;
        
        echo "<td style='background-color: #f8d7da;'>❌ Error</td>";
        echo "<td>Exception: " . htmlspecialchars(substr($e->getMessage(), 0, 50)) . "...</td>";
        echo "<td>" . round($instTime, 2) . " ms</td>";
        echo "<td>SOAP API or connection issue</td>";
    }
    
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// URL and parameter testing
echo "<div class='section'>";
echo "<h2>🔗 URL and Parameter Testing</h2>";

$testUrl = 'http://localhost/just/detalii_dosar.php?numar=130%2F98%2F2022&institutie=TribunalulIALOMITA';

echo "<div class='metric-card'>";
echo "<h4>📄 Problem URL Analysis</h4>";
echo "<p><strong>URL:</strong> <code>$testUrl</code></p>";

// Parse URL components
$urlParts = parse_url($testUrl);
parse_str($urlParts['query'], $queryParams);

echo "<div class='debug-output'>";
echo "URL Components:\n";
echo "Scheme: " . ($urlParts['scheme'] ?? 'N/A') . "\n";
echo "Host: " . ($urlParts['host'] ?? 'N/A') . "\n";
echo "Path: " . ($urlParts['path'] ?? 'N/A') . "\n";
echo "Query: " . ($urlParts['query'] ?? 'N/A') . "\n";
echo "\nQuery Parameters:\n";
foreach ($queryParams as $key => $value) {
    echo "$key: " . urldecode($value) . " (encoded: $value)\n";
}

// Test parameter decoding
echo "\nParameter Decoding Test:\n";
echo "Raw numar parameter: " . ($queryParams['numar'] ?? 'N/A') . "\n";
echo "Decoded numar: " . urldecode($queryParams['numar'] ?? '') . "\n";
echo "Raw institutie parameter: " . ($queryParams['institutie'] ?? 'N/A') . "\n";
echo "Decoded institutie: " . urldecode($queryParams['institutie'] ?? '') . "\n";

// Check for encoding issues
$expectedNumber = '130/98/2022';
$expectedInstitution = 'TribunalulIALOMITA';
$actualNumber = urldecode($queryParams['numar'] ?? '');
$actualInstitution = urldecode($queryParams['institutie'] ?? '');

echo "\nEncoding Verification:\n";
echo "Number match: " . ($actualNumber === $expectedNumber ? "✅ OK" : "❌ MISMATCH") . "\n";
echo "Institution match: " . ($actualInstitution === $expectedInstitution ? "✅ OK" : "❌ MISMATCH") . "\n";
echo "</div>";

echo "</div>";
echo "</div>";

// Frontend debugging suggestions
echo "<div class='section'>";
echo "<h2>🖥️ Frontend Debugging Recommendations</h2>";

echo "<div class='metric-card warning'>";
echo "<h4>🔧 Next Steps for Frontend Investigation</h4>";
echo "<ul>";
echo "<li><strong>Check Browser Console:</strong> Open developer tools and look for JavaScript errors</li>";
echo "<li><strong>Network Tab:</strong> Monitor AJAX requests to see if they're completing</li>";
echo "<li><strong>Check detalii_dosar.php:</strong> Examine the actual page source for PHP errors</li>";
echo "<li><strong>Test Direct Access:</strong> <a href='$testUrl' target='_blank'>Click here to test the problem URL</a></li>";
echo "<li><strong>Compare with Working URL:</strong> <a href='http://localhost/just/detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI' target='_blank'>Test working case</a></li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body></html>";
?>
