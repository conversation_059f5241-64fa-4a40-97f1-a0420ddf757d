<?php
/**
 * Test pentru căutarea reală cu mai mulți termeni
 * Simulează POST request pentru a testa funcționalitatea
 */

// Simulăm o căutare cu mai mulți termeni
$_POST['searchTerms'] = "test\nSaragea\nTudorita";
$_POST['searchType'] = 'auto';

// Redirect către index.php cu parametrii POST
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Căutare Reală - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-search me-2"></i>
            Test Căutare Reală
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>Instrucțiuni Test</h5>
            <p>Pentru a testa funcționalitatea "Expandează toate" / "Restrânge toate":</p>
            <ol>
                <li>Accesați pagina principală: <a href="index.php" target="_blank" class="btn btn-sm btn-primary">index.php</a></li>
                <li>Introduceți mai mulți termeni de căutare (câte unul pe linie):</li>
                <ul>
                    <li><code>test</code></li>
                    <li><code>Saragea</code></li>
                    <li><code>Tudorita</code></li>
                </ul>
                <li>Apăsați butonul "Căutare"</li>
                <li>Verificați dacă apar butoanele "Expandează toate" și "Restrânge toate"</li>
                <li>Testați funcționalitatea butoanelor</li>
            </ol>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-play me-2"></i>Test Automat</h5>
            </div>
            <div class="card-body">
                <p>Sau folosiți formularul de mai jos pentru a trimite automat căutarea:</p>
                
                <form method="POST" action="index.php" target="_blank">
                    <div class="mb-3">
                        <label for="searchTerms" class="form-label">Termeni de căutare:</label>
                        <textarea class="form-control" id="searchTerms" name="searchTerms" rows="4" readonly>test
Saragea
Tudorita</textarea>
                    </div>
                    
                    <input type="hidden" name="searchType" value="auto">
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        Efectuează căutarea în index.php
                    </button>
                </form>
            </div>
        </div>
        
        <div class="alert alert-warning mt-4">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>Ce să verificați</h5>
            <ul>
                <li>Butoanele "Expandează toate" și "Restrânge toate" apar în partea de sus a rezultatelor</li>
                <li>Butoanele răspund la click (nu se întâmplă nimic = problemă)</li>
                <li>Secțiunile se expandează/restrâng când apăsați butoanele</li>
                <li>Iconițele se schimbă (chevron-up/chevron-down)</li>
                <li>Apare notificarea de succes când folosiți butoanele</li>
                <li>Consola browser-ului (F12) nu arată erori JavaScript</li>
            </ul>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5><i class="fas fa-check-circle me-2"></i>Modificări efectuate</h5>
            <p>Am efectuat următoarele modificări în <code>index.php</code>:</p>
            <ul>
                <li>✅ Mutat funcția <code>showNotification</code> înainte de funcțiile de expandare</li>
                <li>✅ Funcțiile <code>expandAllResults</code>, <code>collapseAllResults</code> și <code>toggleTermResults</code> sunt în scope-ul global</li>
                <li>✅ Eliminat funcțiile duplicate</li>
                <li>✅ Adăugat comentarii pentru claritate</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
