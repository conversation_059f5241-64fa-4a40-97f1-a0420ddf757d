<?php
/**
 * Test page for PDF generation functionality in case details page
 * This page tests the complete PDF generation system
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

$testResults = [];
$error = null;
$pdfTestResults = [];

// Test PDF functionality
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['test'])) {
    $testCaseNumber = $_POST['testCaseNumber'] ?? $_GET['testCaseNumber'] ?? '';
    $testInstitution = $_POST['testInstitution'] ?? $_GET['testInstitution'] ?? '';
    
    if (!empty($testCaseNumber) && !empty($testInstitution)) {
        try {
            // Test case details retrieval
            $dosarService = new DosarService();
            $dosarDetails = $dosarService->getDetaliiDosar($testCaseNumber, $testInstitution);
            
            if ($dosarDetails) {
                $testResults['case_found'] = true;
                $testResults['case_data'] = $dosarDetails;
                
                // Test PDF service availability
                if (class_exists('PdfService')) {
                    require_once 'services/PdfService.php';
                    $pdfService = new PdfService();
                    
                    $pdfTestResults['service_exists'] = true;
                    $pdfTestResults['is_available'] = $pdfService->isAvailable();
                    $pdfTestResults['version'] = $pdfService->getVersion();
                    
                    // Test PDF generation URLs
                    $pdfTestResults['pdf_url_inline'] = "generate_pdf.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution) . "&disposition=inline";
                    $pdfTestResults['pdf_url_download'] = "generate_pdf.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution) . "&disposition=attachment";
                    
                } else {
                    $pdfTestResults['service_exists'] = false;
                    $pdfTestResults['error'] = 'PdfService class not found';
                }
                
                // Test browser print functionality
                $pdfTestResults['browser_print_available'] = true;
                $pdfTestResults['case_details_url'] = "detalii_dosar.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution);
                
            } else {
                $testResults['case_found'] = false;
                $error = "Nu s-au găsit detalii pentru dosarul specificat.";
            }
        } catch (Exception $e) {
            $error = 'Eroare la testarea funcționalității PDF: ' . $e->getMessage();
        }
    } else {
        $error = "Vă rugăm să specificați numărul dosarului și instituția.";
    }
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Funcționalitate PDF - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
        }
        .test-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px 8px 0 0;
        }
        .test-body {
            padding: 1.5rem;
        }
        .test-form {
            background: #e9ecef;
            border-radius: 6px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning-message {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            text-decoration: none;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        .test-link.pdf {
            background: #dc3545;
        }
        .test-link.pdf:hover {
            background: #c82333;
        }
        .test-link.print {
            background: #6c757d;
        }
        .test-link.print:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-pdf mr-2"></i>
                    Test Funcționalitate PDF
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    Testare completă a funcționalității de generare PDF pentru detaliile dosarelor
                </p>
            </div>
            <div class="test-body">
                <div class="test-form">
                    <h4><i class="fas fa-cog mr-2"></i>Parametri de Test</h4>
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="testCaseNumber" class="form-label">Numărul dosarului:</label>
                                <input type="text" class="form-control" id="testCaseNumber" name="testCaseNumber" 
                                       value="<?php echo htmlspecialchars($_POST['testCaseNumber'] ?? ''); ?>"
                                       placeholder="ex: 123/2024" required>
                                <small class="form-text text-muted">Introduceți un număr de dosar valid pentru test</small>
                            </div>
                            <div class="col-md-6">
                                <label for="testInstitution" class="form-label">Instituția:</label>
                                <select class="form-control" id="testInstitution" name="testInstitution" required>
                                    <option value="">-- Selectați instituția --</option>
                                    <?php 
                                    $institutii = getInstanteList();
                                    foreach ($institutii as $cod => $nume): 
                                    ?>
                                        <option value="<?php echo htmlspecialchars($cod); ?>"
                                                <?php echo (isset($_POST['testInstitution']) && $_POST['testInstitution'] === $cod) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($nume); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-play mr-2"></i>Testează Funcționalitatea PDF
                                </button>
                                <a href="?test=1&testCaseNumber=123/2024&testInstitution=TribunalulBUCURESTI" class="btn btn-secondary ml-2">
                                    <i class="fas fa-vial mr-2"></i>Test Rapid (Exemplu)
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <?php if ($error): ?>
                <div class="error-message">
                    <strong>Eroare:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($testResults)): ?>
                
                <?php if ($testResults['case_found']): ?>
                <div class="success-message">
                    <strong>✓ Dosarul a fost găsit!</strong> Testarea funcționalității PDF poate continua.
                </div>

                <h4><i class="fas fa-clipboard-check mr-2"></i>Rezultate Test PDF</h4>

                <div class="test-result">
                    <h5>1. Verificare Serviciu PDF</h5>
                    <ul>
                        <li>
                            <span class="status-indicator <?php echo $pdfTestResults['service_exists'] ? 'status-success' : 'status-error'; ?>"></span>
                            <strong>PdfService există:</strong> 
                            <?php echo $pdfTestResults['service_exists'] ? 'Da' : 'Nu'; ?>
                        </li>
                        <?php if ($pdfTestResults['service_exists']): ?>
                        <li>
                            <span class="status-indicator <?php echo $pdfTestResults['is_available'] ? 'status-success' : 'status-warning'; ?>"></span>
                            <strong>wkhtmltopdf disponibil:</strong> 
                            <?php echo $pdfTestResults['is_available'] ? 'Da' : 'Nu (se va folosi browser print)'; ?>
                        </li>
                        <li>
                            <strong>Versiune:</strong> 
                            <code><?php echo htmlspecialchars($pdfTestResults['version']); ?></code>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>

                <div class="test-result">
                    <h5>2. Test Generare PDF Server-side</h5>
                    <?php if ($pdfTestResults['service_exists']): ?>
                    <p>Testați generarea PDF folosind wkhtmltopdf:</p>
                    <div class="mb-3">
                        <a href="<?php echo $pdfTestResults['pdf_url_inline']; ?>" target="_blank" class="test-link pdf">
                            <i class="fas fa-eye mr-2"></i>Vizualizare PDF în Browser
                        </a>
                        <a href="<?php echo $pdfTestResults['pdf_url_download']; ?>" class="test-link pdf">
                            <i class="fas fa-download mr-2"></i>Descărcare PDF
                        </a>
                    </div>
                    <small class="text-muted">
                        Aceste link-uri vor testa generarea PDF folosind generate_pdf.php și wkhtmltopdf.
                    </small>
                    <?php else: ?>
                    <div class="warning-message">
                        <strong>Atenție:</strong> PdfService nu este disponibil. Generarea PDF server-side nu va funcționa.
                    </div>
                    <?php endif; ?>
                </div>

                <div class="test-result">
                    <h5>3. Test Browser Print (Fallback)</h5>
                    <p>Testați funcționalitatea de printare din browser (fallback pentru PDF):</p>
                    <div class="mb-3">
                        <a href="<?php echo $pdfTestResults['case_details_url']; ?>" target="_blank" class="test-link print">
                            <i class="fas fa-external-link-alt mr-2"></i>Deschide Detalii Dosar
                        </a>
                    </div>
                    <small class="text-muted">
                        Deschideți pagina de detalii și testați butonul "Salvează PDF" care folosește window.print().
                    </small>
                </div>

                <div class="test-result">
                    <h5>4. Verificare Conținut Dosar</h5>
                    <p><strong>Informații despre dosarul testat:</strong></p>
                    <ul>
                        <li><strong>Număr:</strong> <?php echo htmlspecialchars($testResults['case_data']->numar); ?></li>
                        <li><strong>Obiect:</strong> <?php echo htmlspecialchars($testResults['case_data']->obiect ?? 'N/A'); ?></li>
                        <li><strong>Data:</strong> <?php echo htmlspecialchars($testResults['case_data']->data ?? 'N/A'); ?></li>
                        <li><strong>Stadiu:</strong> <?php echo htmlspecialchars($testResults['case_data']->stadiuProcesualNume ?? 'N/A'); ?></li>
                        <li><strong>Părți:</strong> <?php echo !empty($testResults['case_data']->parti) ? count($testResults['case_data']->parti) : '0'; ?> părți</li>
                        <li><strong>Ședințe:</strong> <?php echo !empty($testResults['case_data']->sedinte) ? count($testResults['case_data']->sedinte) : '0'; ?> ședințe</li>
                    </ul>
                </div>

                <?php else: ?>
                <div class="error-message">
                    <strong>✗ Dosarul nu a fost găsit!</strong> Nu se poate testa funcționalitatea PDF fără date valide.
                </div>
                <?php endif; ?>

                <?php endif; ?>

                <div class="mt-4">
                    <h4><i class="fas fa-list-check mr-2"></i>Checklist Manual pentru Test PDF</h4>
                    <div class="test-result">
                        <h5>Pași de testare manuală:</h5>
                        <ol>
                            <li><strong>Test Server-side PDF:</strong>
                                <ul>
                                    <li>Faceți click pe "Vizualizare PDF în Browser"</li>
                                    <li>Verificați că se deschide un PDF în browser</li>
                                    <li>Verificați că PDF-ul conține toate informațiile dosarului</li>
                                    <li>Verificați că caracterele românești (ă, â, î, ș, ț) sunt afișate corect</li>
                                </ul>
                            </li>
                            <li><strong>Test Descărcare PDF:</strong>
                                <ul>
                                    <li>Faceți click pe "Descărcare PDF"</li>
                                    <li>Verificați că fișierul se descarcă cu numele corect: "Dosar_[numar]_[data].pdf"</li>
                                    <li>Deschideți fișierul descărcat și verificați conținutul</li>
                                </ul>
                            </li>
                            <li><strong>Test Browser Print:</strong>
                                <ul>
                                    <li>Deschideți pagina de detalii dosar</li>
                                    <li>Faceți click pe butonul "Salvează PDF"</li>
                                    <li>Verificați că se deschide dialogul de printare</li>
                                    <li>Selectați "Save as PDF" și verificați rezultatul</li>
                                </ul>
                            </li>
                            <li><strong>Test Encoding Românesc:</strong>
                                <ul>
                                    <li>Verificați că toate diacriticele românești sunt corecte în PDF</li>
                                    <li>Testați cu dosare care conțin caractere speciale în nume părți</li>
                                    <li>Verificați că formatarea tabelelor este corectă</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>

                <div class="mt-4">
                    <h4><i class="fas fa-tools mr-2"></i>Informații Tehnice</h4>
                    <div class="test-result">
                        <h5>Implementare PDF:</h5>
                        <ul>
                            <li><strong>Metoda principală:</strong> wkhtmltopdf via PdfService</li>
                            <li><strong>Fallback:</strong> Browser print via window.print()</li>
                            <li><strong>Fișiere implicate:</strong>
                                <ul>
                                    <li><code>generate_pdf.php</code> - Generator PDF server-side</li>
                                    <li><code>services/PdfService.php</code> - Serviciu wkhtmltopdf</li>
                                    <li><code>detalii_dosar.php</code> - Pagina cu butonul PDF</li>
                                </ul>
                            </li>
                            <li><strong>Format nume fișier:</strong> "Dosar nr[numar_dosar].pdf"</li>
                            <li><strong>Encoding:</strong> UTF-8 pentru suport caractere românești</li>
                        </ul>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <a href="detalii_dosar.php" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-folder-open mr-2"></i>Detalii Dosar
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test_export.php" class="btn btn-outline-success btn-block">
                            <i class="fas fa-download mr-2"></i>Test Export
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test_complet_encoding.php" class="btn btn-outline-info btn-block">
                            <i class="fas fa-search mr-2"></i>Test Encoding
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="sedinte.php" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-calendar-alt mr-2"></i>Căutare Ședințe
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
