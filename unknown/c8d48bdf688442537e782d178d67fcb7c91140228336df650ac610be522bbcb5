
# Portal Judiciar România - Production Configuration
RewriteEngine On

# Detectăm automat dacă suntem în subdirector sau root
# Pentru producție (just.gabrielanghel.ro) - root directory
# Pentru dezvoltare (localhost/just) - subdirectory

# Setăm DirectoryIndex pentru toate scenariile
DirectoryIndex index.php index.html

# Redirecționăm cererile către fișierele PHP corespunzătoare
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ $1.php [L]

# Dezactivăm listarea directoarelor
Options -Indexes

# Setăm codificarea implicită
AddDefaultCharset UTF-8

# Performance Optimization - Cache Rules
<IfModule mod_expires.c>
    ExpiresActive On

    # CSS și JavaScript
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"

    # Imagini
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"

    # Fonturi
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"

    # HTML, XML, JSON
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
</IfModule>

<IfModule mod_headers.c>
    # Cache pentru resurse statice
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|woff|woff2)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>

    # Cache pentru HTML
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>

    # Compresie GZIP
    <FilesMatch "\.(css|js|html|htm|xml|json)$">
        Header set Vary "Accept-Encoding"
    </FilesMatch>
</IfModule>

<IfModule mod_deflate.c>
    # Compresie pentru text
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>