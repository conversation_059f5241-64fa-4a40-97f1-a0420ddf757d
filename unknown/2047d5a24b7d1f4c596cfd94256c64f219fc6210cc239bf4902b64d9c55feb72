<?php
/**
 * SOAP API Institution Code Validation Script
 * Comprehensive testing of all institution codes against live SOAP API
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/SedinteService.php';

// Set execution time limit for comprehensive testing
set_time_limit(300); // 5 minutes

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validare Coduri Instituții - Portal Judiciar</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .validation-success { background-color: #d4edda; border-left: 4px solid #28a745; }
        .validation-error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .validation-warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .code-block { 
            background-color: #f8f9fa; 
            border: 1px solid #e9ecef; 
            border-radius: 0.375rem; 
            padding: 0.75rem; 
            font-family: 'Courier New', monospace; 
            font-size: 0.875rem; 
        }
        .progress-container { position: sticky; top: 0; z-index: 1000; background: white; padding: 1rem 0; }
        .institution-result { margin: 0.25rem 0; padding: 0.5rem; border-radius: 0.25rem; }
        .summary-stats { background: linear-gradient(135deg, #007bff, #2c3e50); color: white; }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-check-circle text-primary"></i>
                    Validare Coduri Instituții SOAP API
                </h1>
            </div>
        </div>

        <?php
        $startTime = microtime(true);
        $testDate = date('Y-m-d', strtotime('+1 day')) . 'T00:00:00'; // Tomorrow to avoid real data
        
        // Get all institution codes
        $allInstitutions = getInstanteList();
        $totalInstitutions = count($allInstitutions);
        
        echo '<div class="progress-container">';
        echo '<div class="card">';
        echo '<div class="card-body">';
        echo '<h5><i class="fas fa-info-circle"></i> Informații Test</h5>';
        echo '<p><strong>Data test:</strong> ' . $testDate . '</p>';
        echo '<p><strong>Total instituții de testat:</strong> ' . $totalInstitutions . '</p>';
        echo '<div class="progress mb-3">';
        echo '<div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>';
        echo '</div>';
        echo '<p id="progressText">Pregătire test...</p>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        // Initialize results arrays
        $validCodes = [];
        $invalidCodes = [];
        $errorCodes = [];
        $timeoutCodes = [];
        
        // Initialize SOAP service
        try {
            $sedinteService = new SedinteService();
            echo '<div class="alert alert-success"><i class="fas fa-check"></i> Serviciu SOAP inițializat cu succes</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger"><i class="fas fa-times"></i> Eroare la inițializarea serviciului SOAP: ' . htmlspecialchars($e->getMessage()) . '</div>';
            exit;
        }
        
        echo '<div class="row">';
        echo '<div class="col-md-8">';
        echo '<div class="card">';
        echo '<div class="card-header">';
        echo '<h5><i class="fas fa-list"></i> Rezultate Validare în Timp Real</h5>';
        echo '</div>';
        echo '<div class="card-body" style="max-height: 600px; overflow-y: auto;" id="resultsContainer">';
        
        // Flush output to show progress
        if (ob_get_level()) ob_flush();
        flush();
        
        $processed = 0;
        
        foreach ($allInstitutions as $code => $name) {
            $processed++;
            $progressPercent = round(($processed / $totalInstitutions) * 100);
            
            echo '<script>';
            echo 'document.getElementById("progressBar").style.width = "' . $progressPercent . '%";';
            echo 'document.getElementById("progressBar").textContent = "' . $progressPercent . '%";';
            echo 'document.getElementById("progressText").textContent = "Testare ' . $processed . '/' . $totalInstitutions . ': ' . htmlspecialchars($name) . '";';
            echo '</script>';
            
            try {
                // Test the institution code
                $searchParams = [
                    'dataSedinta' => $testDate,
                    'institutie' => $code
                ];
                
                $startTestTime = microtime(true);
                $results = $sedinteService->cautareSedinte($searchParams);
                $testDuration = round((microtime(true) - $startTestTime) * 1000); // milliseconds
                
                // Success - code is valid
                $validCodes[$code] = [
                    'name' => $name,
                    'duration' => $testDuration,
                    'result_count' => count($results)
                ];
                
                echo '<div class="institution-result validation-success">';
                echo '<i class="fas fa-check text-success"></i> ';
                echo '<strong>' . htmlspecialchars($code) . '</strong> - ' . htmlspecialchars($name);
                echo ' <small class="text-muted">(' . $testDuration . 'ms, ' . count($results) . ' rezultate)</small>';
                echo '</div>';
                
            } catch (Exception $e) {
                $errorMessage = $e->getMessage();
                
                // Categorize the error
                if (strpos($errorMessage, 'is not a valid value for Institutie') !== false) {
                    // Invalid institution code
                    $invalidCodes[$code] = [
                        'name' => $name,
                        'error' => $errorMessage
                    ];
                    
                    echo '<div class="institution-result validation-error">';
                    echo '<i class="fas fa-times text-danger"></i> ';
                    echo '<strong>' . htmlspecialchars($code) . '</strong> - ' . htmlspecialchars($name);
                    echo '<br><small class="text-danger">Cod invalid: ' . htmlspecialchars($errorMessage) . '</small>';
                    echo '</div>';
                    
                } elseif (strpos($errorMessage, 'timeout') !== false || strpos($errorMessage, 'connection') !== false) {
                    // Timeout or connection error
                    $timeoutCodes[$code] = [
                        'name' => $name,
                        'error' => $errorMessage
                    ];
                    
                    echo '<div class="institution-result validation-warning">';
                    echo '<i class="fas fa-clock text-warning"></i> ';
                    echo '<strong>' . htmlspecialchars($code) . '</strong> - ' . htmlspecialchars($name);
                    echo '<br><small class="text-warning">Timeout/Conexiune: ' . htmlspecialchars($errorMessage) . '</small>';
                    echo '</div>';
                    
                } else {
                    // Other error
                    $errorCodes[$code] = [
                        'name' => $name,
                        'error' => $errorMessage
                    ];
                    
                    echo '<div class="institution-result validation-warning">';
                    echo '<i class="fas fa-exclamation-triangle text-warning"></i> ';
                    echo '<strong>' . htmlspecialchars($code) . '</strong> - ' . htmlspecialchars($name);
                    echo '<br><small class="text-warning">Eroare: ' . htmlspecialchars($errorMessage) . '</small>';
                    echo '</div>';
                }
            }
            
            // Flush output for real-time display
            if (ob_get_level()) ob_flush();
            flush();
            
            // Small delay to avoid overwhelming the API
            usleep(100000); // 0.1 seconds
        }
        
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        // Summary statistics
        echo '<div class="col-md-4">';
        echo '<div class="card summary-stats">';
        echo '<div class="card-header">';
        echo '<h5><i class="fas fa-chart-pie"></i> Sumar Rezultate</h5>';
        echo '</div>';
        echo '<div class="card-body">';
        
        $totalTested = count($validCodes) + count($invalidCodes) + count($errorCodes) + count($timeoutCodes);
        $successRate = $totalTested > 0 ? round((count($validCodes) / $totalTested) * 100, 1) : 0;
        
        echo '<div class="text-center mb-3">';
        echo '<h2>' . $successRate . '%</h2>';
        echo '<p>Rata de succes</p>';
        echo '</div>';
        
        echo '<hr>';
        
        echo '<div class="mb-2">';
        echo '<i class="fas fa-check text-success"></i> ';
        echo '<strong>Coduri valide:</strong> ' . count($validCodes);
        echo '</div>';
        
        echo '<div class="mb-2">';
        echo '<i class="fas fa-times text-danger"></i> ';
        echo '<strong>Coduri invalide:</strong> ' . count($invalidCodes);
        echo '</div>';
        
        echo '<div class="mb-2">';
        echo '<i class="fas fa-clock text-warning"></i> ';
        echo '<strong>Timeout/Conexiune:</strong> ' . count($timeoutCodes);
        echo '</div>';
        
        echo '<div class="mb-2">';
        echo '<i class="fas fa-exclamation-triangle text-warning"></i> ';
        echo '<strong>Alte erori:</strong> ' . count($errorCodes);
        echo '</div>';
        
        $totalTime = round(microtime(true) - $startTime, 2);
        echo '<hr>';
        echo '<p><strong>Timp total:</strong> ' . $totalTime . ' secunde</p>';
        
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        // Detailed results sections
        if (!empty($invalidCodes)) {
            echo '<div class="row mt-4">';
            echo '<div class="col-12">';
            echo '<div class="card border-danger">';
            echo '<div class="card-header bg-danger text-white">';
            echo '<h5><i class="fas fa-times"></i> Coduri Instituții Invalide (' . count($invalidCodes) . ')</h5>';
            echo '</div>';
            echo '<div class="card-body">';
            echo '<p class="text-danger">Aceste coduri trebuie corectate în funcția <code>getInstanteList()</code>:</p>';
            echo '<div class="code-block">';
            foreach ($invalidCodes as $code => $info) {
                echo "❌ '{$code}' => '{$info['name']}'\n";
                echo "   Eroare: {$info['error']}\n\n";
            }
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
        
        // Generate corrected institution list
        if (!empty($validCodes)) {
            echo '<div class="row mt-4">';
            echo '<div class="col-12">';
            echo '<div class="card border-success">';
            echo '<div class="card-header bg-success text-white">';
            echo '<h5><i class="fas fa-check"></i> Coduri Instituții Verificate (' . count($validCodes) . ')</h5>';
            echo '</div>';
            echo '<div class="card-body">';
            echo '<p class="text-success">Aceste coduri sunt validate și funcționează cu SOAP API:</p>';
            echo '<div class="code-block" style="max-height: 400px; overflow-y: auto;">';
            foreach ($validCodes as $code => $info) {
                echo "✅ '{$code}' => '{$info['name']}', // {$info['duration']}ms\n";
            }
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
        ?>

        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center">
                    <a href="sedinte.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Înapoi la Căutare Ședințe
                    </a>
                    <a href="soap_api_analysis.php" class="btn btn-secondary">
                        <i class="fas fa-cogs"></i> Analiză SOAP API
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-scroll to bottom of results
        const resultsContainer = document.getElementById('resultsContainer');
        if (resultsContainer) {
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }
    </script>
</body>
</html>
