<?php
/**
 * Complete User Flow Test
 * Tests the entire search → expand → click → details workflow
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Complete User Flow Test - Romanian Judicial Portal</title>";
echo "<meta charset='UTF-8'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
.container { max-width: 1400px; margin: 0 auto; }
.section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.header { background: linear-gradient(135deg, #28a745, #20c997); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
.test-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
.test-table th, .test-table td { padding: 12px; border: 1px solid #ddd; text-align: left; vertical-align: top; }
.test-table th { background-color: #28a745; color: white; }
.status-success { background-color: #d4edda; color: #155724; }
.status-warning { background-color: #fff3cd; color: #856404; }
.status-error { background-color: #f8d7da; color: #721c24; }
.test-link { display: inline-block; background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 2px; font-size: 12px; }
.test-link:hover { background: #218838; color: white; text-decoration: none; }
.step-box { border-left: 4px solid #28a745; background: #d4edda; padding: 15px; margin: 10px 0; }
.issue-box { border-left: 4px solid #dc3545; background: #f8d7da; padding: 15px; margin: 10px 0; }
.fix-box { border-left: 4px solid #007bff; background: #d1ecf1; padding: 15px; margin: 10px 0; }
.code-snippet { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; border-radius: 4px; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🔄 Complete User Flow Test</h1>";
echo "<p>Testing the entire search → expand → click → details workflow</p>";
echo "<p><strong>Goal:</strong> Verify all functionality works end-to-end</p>";
echo "</div>";

// Step 1: Test Search Functionality
echo "<div class='section'>";
echo "<h2>🔍 Step 1: Search Functionality Test</h2>";

echo "<div class='step-box'>";
echo "<h4>✅ Test Search with Known Working Case</h4>";
echo "<p><strong>Test Case:</strong> 130/98/2022 (should return results from multiple institutions)</p>";
echo "<p><strong>Expected:</strong> Results from TribunalulIALOMITA and CurteadeApelBUCURESTI</p>";
echo "</div>";

// Perform actual search test
try {
    $dosarService = new DosarService();
    $searchTerm = '130/98/2022';
    
    // Test search for this case number
    $searchResults = [];
    $institutions = ['TribunalulIALOMITA', 'CurteadeApelBUCURESTI', 'TribunalulBUCURESTI'];
    
    foreach ($institutions as $institution) {
        try {
            $dosare = $dosarService->cautareDupaNumarDosar($searchTerm, $institution, '', '', '');
            if (!empty($dosare)) {
                $searchResults[] = [
                    'institution' => $institution,
                    'count' => count($dosare),
                    'first_case' => $dosare[0]
                ];
            }
        } catch (Exception $e) {
            // Institution might not have this case
        }
    }
    
    echo "<table class='test-table'>";
    echo "<tr><th>Institution</th><th>Results Found</th><th>Status</th><th>Test Link</th></tr>";
    
    foreach ($institutions as $institution) {
        $found = false;
        $count = 0;
        
        foreach ($searchResults as $result) {
            if ($result['institution'] === $institution) {
                $found = true;
                $count = $result['count'];
                break;
            }
        }
        
        echo "<tr>";
        echo "<td><strong>$institution</strong></td>";
        echo "<td>$count cases</td>";
        
        if ($found) {
            echo "<td class='status-success'>✅ <strong>FOUND</strong><br>Data available for testing</td>";
        } else {
            echo "<td class='status-warning'>⚠️ <strong>NO DATA</strong><br>No cases found</td>";
        }
        
        $searchUrl = "index.php?search=1&bulkSearchTerms=" . urlencode($searchTerm);
        echo "<td><a href='$searchUrl' target='_blank' class='test-link'>Test Search</a></td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='issue-box'>";
    echo "<h4>❌ Search Test Failed</h4>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Step 2: Test Expansion Functionality
echo "<div class='section'>";
echo "<h2>📊 Step 2: Results Expansion Test</h2>";

echo "<div class='step-box'>";
echo "<h4>🎯 Test Expansion Functions</h4>";
echo "<p><strong>Functions to Test:</strong></p>";
echo "<ul>";
echo "<li><code>expandAllResults()</code> - Should expand all result sections</li>";
echo "<li><code>collapseAllResults()</code> - Should collapse all result sections</li>";
echo "<li><code>toggleTermResults(index)</code> - Should toggle individual sections</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔧 JavaScript Function Analysis</h3>";

$jsAnalysis = [
    [
        'function' => 'expandAllResults()',
        'selector' => 'document.querySelectorAll(\'[id^="termContent"]\')',
        'action' => 'Sets display: block',
        'status' => 'Should work if elements exist'
    ],
    [
        'function' => 'collapseAllResults()',
        'selector' => 'document.querySelectorAll(\'[id^="termContent"]\')',
        'action' => 'Sets display: none',
        'status' => 'Should work if elements exist'
    ],
    [
        'function' => 'toggleTermResults(index)',
        'selector' => 'document.getElementById(\'termContent\' + index)',
        'action' => 'Toggles display property',
        'status' => 'Should work if specific element exists'
    ]
];

echo "<table class='test-table'>";
echo "<tr><th>Function</th><th>Element Selector</th><th>Action</th><th>Analysis</th></tr>";

foreach ($jsAnalysis as $analysis) {
    echo "<tr>";
    echo "<td><code>{$analysis['function']}</code></td>";
    echo "<td><code>{$analysis['selector']}</code></td>";
    echo "<td>{$analysis['action']}</td>";
    echo "<td class='status-success'>✅ {$analysis['status']}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<div class='fix-box'>";
echo "<h4>🔍 Potential Issues Identified</h4>";
echo "<p><strong>Issue 1:</strong> Functions may be called before DOM elements are created</p>";
echo "<p><strong>Issue 2:</strong> CSS or JavaScript conflicts preventing proper execution</p>";
echo "<p><strong>Issue 3:</strong> Missing initialization in DOMContentLoaded event</p>";
echo "</div>";

echo "</div>";

// Step 3: Test Case Details Access
echo "<div class='section'>";
echo "<h2>🏛️ Step 3: Case Details Access Test</h2>";

echo "<div class='step-box'>";
echo "<h4>🎯 Test Case Details Page Access</h4>";
echo "<p><strong>Test Cases:</strong></p>";
echo "<ul>";
echo "<li>TribunalulIALOMITA case (recently fixed)</li>";
echo "<li>CurteadeApelBUCURESTI case (known working)</li>";
echo "<li>Non-existent case (error handling)</li>";
echo "</ul>";
echo "</div>";

$detailsTests = [
    [
        'description' => 'TribunalulIALOMITA - Fixed Case',
        'url' => 'detalii_dosar.php?numar=130%2F98%2F2022&institutie=TribunalulIALOMITA',
        'expected' => 'Should load with 316 parties after parameter fix',
        'status' => 'primary'
    ],
    [
        'description' => 'CurteadeApelBUCURESTI - Working Case',
        'url' => 'detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI',
        'expected' => 'Should load with 161 parties (known working)',
        'status' => 'success'
    ],
    [
        'description' => 'Non-existent Case',
        'url' => 'detalii_dosar.php?numar=999%2F99%2F9999&institutie=TribunalulIALOMITA',
        'expected' => 'Should show "Nu s-au găsit dosare" message',
        'status' => 'warning'
    ],
    [
        'description' => 'Invalid Parameters',
        'url' => 'detalii_dosar.php?numar=&institutie=',
        'expected' => 'Should show parameter error message',
        'status' => 'danger'
    ]
];

echo "<table class='test-table'>";
echo "<tr><th>Test Case</th><th>Expected Result</th><th>Test Link</th><th>Status</th></tr>";

foreach ($detailsTests as $test) {
    echo "<tr>";
    echo "<td><strong>{$test['description']}</strong></td>";
    echo "<td>{$test['expected']}</td>";
    echo "<td><a href='{$test['url']}' target='_blank' class='test-link'>Test Page</a></td>";
    
    switch ($test['status']) {
        case 'primary':
            echo "<td class='status-success'>🔧 <strong>RECENTLY FIXED</strong></td>";
            break;
        case 'success':
            echo "<td class='status-success'>✅ <strong>KNOWN WORKING</strong></td>";
            break;
        case 'warning':
            echo "<td class='status-warning'>⚠️ <strong>ERROR CASE</strong></td>";
            break;
        case 'danger':
            echo "<td class='status-error'>❌ <strong>INVALID INPUT</strong></td>";
            break;
    }
    echo "</tr>";
}

echo "</table>";

echo "</div>";

// Step 4: Recommended Testing Workflow
echo "<div class='section'>";
echo "<h2>📋 Step 4: Complete Testing Workflow</h2>";

echo "<div class='step-box'>";
echo "<h4>🔄 Manual Testing Steps</h4>";
echo "<ol>";
echo "<li><strong>Search Test:</strong> Go to homepage, search for '130/98/2022'</li>";
echo "<li><strong>Results Display:</strong> Verify search results appear with expand/collapse buttons</li>";
echo "<li><strong>Individual Expansion:</strong> Click on result headers to expand/collapse individual sections</li>";
echo "<li><strong>Bulk Expansion:</strong> Test 'Expandează toate' and 'Restrânge toate' buttons</li>";
echo "<li><strong>Case Access:</strong> Click 'Detalii' button on a case to open details page</li>";
echo "<li><strong>Details Loading:</strong> Verify case details page loads without infinite loading</li>";
echo "<li><strong>Navigation:</strong> Test back button and navigation between pages</li>";
echo "</ol>";
echo "</div>";

echo "<div class='fix-box'>";
echo "<h4>🛠️ If Issues Persist</h4>";
echo "<p><strong>JavaScript Debug:</strong> Open browser console and check for errors</p>";
echo "<p><strong>Network Tab:</strong> Verify all CSS/JS resources load correctly</p>";
echo "<p><strong>Element Inspection:</strong> Check if termContent and toggleIcon elements exist</p>";
echo "<p><strong>Function Testing:</strong> Test functions manually in browser console</p>";
echo "</div>";

echo "</div>";

// Step 5: Quick Fix Recommendations
echo "<div class='section'>";
echo "<h2>🔧 Step 5: Quick Fix Recommendations</h2>";

echo "<div class='fix-box'>";
echo "<h4>✅ Applied Fixes</h4>";
echo "<p><strong>1. Parameter Consistency:</strong> Fixed Open Graph URL to use 'numar' instead of 'numar_dosar'</p>";
echo "<p><strong>2. Backward Compatibility:</strong> detalii_dosar.php supports both parameter formats</p>";
echo "</div>";

echo "<div class='issue-box'>";
echo "<h4>🔍 Potential Additional Fixes Needed</h4>";
echo "<p><strong>1. JavaScript Initialization:</strong> Ensure expansion functions are available globally</p>";
echo "<p><strong>2. DOM Loading:</strong> Add proper initialization for expansion functionality</p>";
echo "<p><strong>3. Error Handling:</strong> Add try-catch blocks around expansion functions</p>";
echo "</div>";

echo "<div class='code-snippet'>";
echo "<strong>Suggested JavaScript Addition to index.php:</strong><br>";
echo "// Add to DOMContentLoaded event:<br>";
echo "function initResultsExpansion() {<br>";
echo "&nbsp;&nbsp;// Ensure expansion functions are available<br>";
echo "&nbsp;&nbsp;console.log('Results expansion initialized');<br>";
echo "}<br><br>";
echo "// Add to existing DOMContentLoaded:<br>";
echo "initResultsExpansion(); // Add this line";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body></html>";
?>
