<?php
// Examine the actual decision text content to understand party formats

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

function examineDecisionText($caseNumber, $institution) {
    echo "=== EXAMINING DECISION TEXT: $caseNumber - $institution ===" . PHP_EOL;
    
    try {
        $dosarService = new DosarService();
        
        // Get raw SOAP data
        $reflection = new ReflectionClass($dosarService);
        $method = $reflection->getMethod('executeSoapCallWithRetry');
        $method->setAccessible(true);
        
        $searchParams = [
            'numarDosar' => $caseNumber,
            'institutie' => $institution,
            'obiectDosar' => '',
            'numeParte' => '',
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        $rawResponse = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Decision text examination");
        
        if (!$rawResponse || !isset($rawResponse->CautareDosare2Result)) {
            echo "❌ No SOAP response" . PHP_EOL;
            return;
        }
        
        $dosare = $rawResponse->CautareDosare2Result->Dosar ?? null;
        if (!$dosare) {
            echo "❌ No Dosar in response" . PHP_EOL;
            return;
        }
        
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $targetDosar = null;
        foreach ($dosare as $d) {
            if (isset($d->numar) && $d->numar === $caseNumber && $d->institutie === $institution) {
                $targetDosar = $d;
                break;
            }
        }
        
        if (!$targetDosar) {
            echo "❌ Case not found" . PHP_EOL;
            return;
        }
        
        // Examine decision text in all sessions
        if (isset($targetDosar->sedinte) && isset($targetDosar->sedinte->DosarSedinta)) {
            $sedinte = $targetDosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            foreach ($sedinte as $index => $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;
                    
                    echo PHP_EOL . "📄 SESSION " . ($index + 1) . " CONTENT (" . strlen($solutieText) . " chars):" . PHP_EOL;
                    echo "----------------------------------------" . PHP_EOL;
                    
                    // Show first 2000 characters to understand structure
                    echo substr($solutieText, 0, 2000) . PHP_EOL;
                    echo "..." . PHP_EOL;
                    
                    // Show last 1000 characters
                    if (strlen($solutieText) > 2000) {
                        echo "Last 1000 characters:" . PHP_EOL;
                        echo substr($solutieText, -1000) . PHP_EOL;
                    }
                    
                    echo "----------------------------------------" . PHP_EOL;
                    
                    // Look for specific patterns
                    echo "🔍 PATTERN ANALYSIS:" . PHP_EOL;
                    
                    // Count semicolons and commas
                    $semicolonCount = substr_count($solutieText, ';');
                    $commaCount = substr_count($solutieText, ',');
                    echo "  - Semicolons: $semicolonCount" . PHP_EOL;
                    echo "  - Commas: $commaCount" . PHP_EOL;
                    
                    // Look for creditor patterns
                    if (preg_match_all('/creditor[ui]?/i', $solutieText, $creditorMatches)) {
                        echo "  - 'creditor' mentions: " . count($creditorMatches[0]) . PHP_EOL;
                    }
                    
                    // Look for appellant patterns
                    if (preg_match_all('/apelan[tţ][ui]?/i', $solutieText, $appellantMatches)) {
                        echo "  - 'apelant' mentions: " . count($appellantMatches[0]) . PHP_EOL;
                    }
                    
                    // Look for name-like patterns
                    if (preg_match_all('/\b[A-ZĂÂÎȘȚŢ][a-zăâîșțţ]+\s+[A-ZĂÂÎȘȚŢ][a-zăâîșțţ]+/u', $solutieText, $nameMatches)) {
                        echo "  - Potential names (FirstName LastName): " . count($nameMatches[0]) . PHP_EOL;
                        echo "  - Sample names: " . implode(', ', array_slice($nameMatches[0], 0, 5)) . PHP_EOL;
                    }
                    
                    // Look for lists with specific separators
                    if (preg_match_all('/[A-ZĂÂÎȘȚŢ][^;]{10,}(?:;[A-ZĂÂÎȘȚŢ][^;]{10,}){3,}/u', $solutieText, $semiListMatches)) {
                        echo "  - Semicolon-separated lists (4+ items): " . count($semiListMatches[0]) . PHP_EOL;
                        foreach ($semiListMatches[0] as $i => $list) {
                            $itemCount = count(explode(';', $list));
                            echo "    List " . ($i + 1) . ": $itemCount items - " . substr($list, 0, 100) . "..." . PHP_EOL;
                        }
                    }
                    
                    if (preg_match_all('/[A-ZĂÂÎȘȚŢ][^,]{10,}(?:,[A-ZĂÂÎȘȚŢ][^,]{10,}){5,}/u', $solutieText, $commaListMatches)) {
                        echo "  - Comma-separated lists (6+ items): " . count($commaListMatches[0]) . PHP_EOL;
                        foreach ($commaListMatches[0] as $i => $list) {
                            $itemCount = count(explode(',', $list));
                            echo "    List " . ($i + 1) . ": $itemCount items - " . substr($list, 0, 100) . "..." . PHP_EOL;
                        }
                    }
                    
                    echo PHP_EOL;
                }
            }
        } else {
            echo "❌ No sessions with decision text found" . PHP_EOL;
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . PHP_EOL;
    }
}

// Examine both test cases
echo "🔍 DECISION TEXT CONTENT EXAMINATION" . PHP_EOL;
echo "====================================" . PHP_EOL;

examineDecisionText('130/98/2022', 'TribunalulIALOMITA');
echo PHP_EOL . "====================================" . PHP_EOL;
examineDecisionText('130/98/2022', 'CurteadeApelBUCURESTI');

echo PHP_EOL . "✅ Examination complete!" . PHP_EOL;
?>
