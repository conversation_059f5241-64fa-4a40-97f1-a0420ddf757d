<?php
require_once 'src/Helpers/PerformanceHelper.php';
use App\Helpers\PerformanceHelper;

echo "=== WebP Optimization Test ===\n";
$logoPath = 'images/logo.jpg';
echo "Original logo: " . $logoPath . "\n";
echo "Original size: " . filesize($logoPath) . " bytes\n";

$webpPath = PerformanceHelper::optimizeImage($logoPath, 80);
if ($webpPath) {
    echo "WebP created: " . $webpPath . "\n";
    echo "WebP size: " . filesize($webpPath) . " bytes\n";
    echo "Size reduction: " . round((1 - filesize($webpPath) / filesize($logoPath)) * 100, 2) . "%\n";
} else {
    echo "WebP optimization failed\n";
}

echo "\n=== Responsive Image Tag Test ===\n";
$responsiveTag = PerformanceHelper::generateResponsiveImageTag('images/logo.jpg', 'Portal Judiciar România Logo', 'img-fluid');
echo $responsiveTag . "\n";

echo "\n=== File System Check ===\n";
if (file_exists('images/logo.webp')) {
    echo "✓ WebP file exists: images/logo.webp\n";
    echo "  Size: " . filesize('images/logo.webp') . " bytes\n";
    echo "  Modified: " . date('Y-m-d H:i:s', filemtime('images/logo.webp')) . "\n";
} else {
    echo "✗ WebP file not found\n";
}
?>
