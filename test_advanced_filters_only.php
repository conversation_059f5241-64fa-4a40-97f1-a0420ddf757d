<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <title>Test Filtre Avansate - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 15px 0; background: #f8f9fa; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
        
        /* CSS pentru filtrele avansate - copiat exact din index.php */
        #advancedFiltersToggle {
            text-decoration: none;
            padding: 0.75rem 1rem;
            border: 1px solid #007bff;
            border-radius: 6px;
            background-color: rgba(0, 123, 255, 0.05);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        #advancedFiltersToggle:hover {
            background-color: rgba(0, 123, 255, 0.1);
            text-decoration: none;
            color: #007bff !important;
            transform: translateY(-1px);
        }

        #advancedFiltersToggle i.fa-chevron-down {
            transition: transform 0.3s ease;
        }

        #advancedFiltersToggle.expanded i.fa-chevron-down {
            transform: rotate(180deg);
        }

        /* Advanced Filters Collapsible Section */
        #advancedFilters {
            overflow: hidden;
            transition: all 0.3s ease;
            max-height: 0;
            opacity: 0;
            padding: 0;
            margin-top: 0;
            border: none;
            background-color: transparent;
        }

        #advancedFilters.show {
            max-height: 1000px;
            opacity: 1;
            padding: 1.25rem;
            margin-top: 1.25rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: rgba(0, 123, 255, 0.02);
        }

        .advanced-filters-section {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(44, 62, 80, 0.01) 100%);
            border: 1px solid rgba(0, 123, 255, 0.1);
            border-radius: 8px;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Test Filtre Avansate Portal Judiciar</h1>
        
        <div class="test-section">
            <h3>Test Funcționalitate Filtre Avansate</h3>
            
            <!-- Advanced Filters Toggle Button -->
            <div class="mt-3">
                <a href="#" id="advancedFiltersToggle" class="text-primary d-flex align-items-center justify-content-center">
                    <i class="fas fa-filter me-2"></i>
                    <span>Arată filtrele avansate</span>
                    <i class="fas fa-chevron-down ms-2"></i>
                </a>
            </div>

            <!-- Advanced Filters Section -->
            <div id="advancedFilters" class="advanced-filters-section mt-4">
                <h6 class="mb-3">
                    <i class="fas fa-filter me-2"></i>
                    Filtre avansate
                    <small class="text-muted ms-2">(opționale - pot fi folosite independent sau împreună cu termenii de căutare)</small>
                </h6>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="institutie" class="form-label">Instanța</label>
                        <select class="form-select" id="institutie" name="institutie">
                            <option value="">Toate instanțele</option>
                            <option value="test1">Test Instanță 1</option>
                            <option value="test2">Test Instanță 2</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="categorieCaz" class="form-label">Categoria cazului</label>
                        <select class="form-select" id="categorieCaz" name="categorieCaz">
                            <option value="">Toate categoriile</option>
                            <option value="civil">Civil</option>
                            <option value="penal">Penal</option>
                        </select>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="dataInceput" class="form-label">Data început</label>
                        <input type="text" class="form-control" id="dataInceput" name="dataInceput" placeholder="DD.MM.YYYY">
                    </div>
                    <div class="col-md-6">
                        <label for="dataSfarsit" class="form-label">Data sfârșit</label>
                        <input type="text" class="form-control" id="dataSfarsit" name="dataSfarsit" placeholder="DD.MM.YYYY">
                    </div>
                </div>
            </div>
            
            <div id="testResult" class="mt-3"></div>
            
            <div class="mt-3">
                <button onclick="testToggleManually()" class="btn btn-primary">Test Manual Toggle</button>
                <button onclick="showFiltersManually()" class="btn btn-success">Arată Filtrele</button>
                <button onclick="hideFiltersManually()" class="btn btn-secondary">Ascunde Filtrele</button>
                <button onclick="checkElementsStatus()" class="btn btn-info">Verifică Elemente</button>
            </div>
        </div>
    </div>

    <script>
        // Funcția de inițializare copiată exact din index.php
        function initAdvancedFiltersToggle() {
            const toggleButton = document.getElementById('advancedFiltersToggle');
            const filtersSection = document.getElementById('advancedFilters');
            const toggleText = toggleButton?.querySelector('span');
            const chevronIcon = toggleButton?.querySelector('i.fa-chevron-down');

            console.log('Advanced filters elements:', {
                toggleButton: !!toggleButton,
                filtersSection: !!filtersSection,
                toggleText: !!toggleText,
                chevronIcon: !!chevronIcon
            });

            if (!toggleButton || !filtersSection || !toggleText) {
                console.warn('Advanced filters toggle elements not found');
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Elementele pentru filtrele avansate nu au fost găsite!</div>';
                return;
            }

            // Function to show filters
            function showFilters() {
                filtersSection.classList.add('show');
                toggleText.textContent = 'Ascunde filtrele avansate';
                toggleButton.classList.add('expanded');
                toggleButton.setAttribute('aria-expanded', 'true');
                console.log('Filters shown');
            }

            // Function to hide filters
            function hideFilters() {
                filtersSection.classList.remove('show');
                toggleText.textContent = 'Arată filtrele avansate';
                toggleButton.classList.remove('expanded');
                toggleButton.setAttribute('aria-expanded', 'false');
                console.log('Filters hidden');
            }

            // Toggle click handler
            toggleButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Advanced filters toggle clicked');
                
                if (filtersSection.classList.contains('show')) {
                    hideFilters();
                    document.getElementById('testResult').innerHTML = 
                        '<div class="success">✅ Filtrele avansate au fost ascunse</div>';
                } else {
                    showFilters();
                    document.getElementById('testResult').innerHTML = 
                        '<div class="success">✅ Filtrele avansate au fost afișate</div>';
                }
            });

            // Keyboard accessibility
            toggleButton.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggleButton.click();
                }
            });

            document.getElementById('testResult').innerHTML = 
                '<div class="success">✅ Toggle-ul pentru filtrele avansate a fost inițializat cu succes</div>';
        }

        // Funcții de test manual
        function testToggleManually() {
            const filtersSection = document.getElementById('advancedFilters');
            const hasShow = filtersSection.classList.contains('show');
            
            if (hasShow) {
                filtersSection.classList.remove('show');
                document.getElementById('testResult').innerHTML = 
                    '<div class="warning">⚠️ Clasa "show" a fost eliminată manual</div>';
            } else {
                filtersSection.classList.add('show');
                document.getElementById('testResult').innerHTML = 
                    '<div class="success">✅ Clasa "show" a fost adăugată manual</div>';
            }
        }

        function showFiltersManually() {
            const filtersSection = document.getElementById('advancedFilters');
            filtersSection.classList.add('show');
            document.getElementById('testResult').innerHTML = 
                '<div class="success">✅ Filtrele au fost afișate manual</div>';
        }

        function hideFiltersManually() {
            const filtersSection = document.getElementById('advancedFilters');
            filtersSection.classList.remove('show');
            document.getElementById('testResult').innerHTML = 
                '<div class="warning">⚠️ Filtrele au fost ascunse manual</div>';
        }

        function checkElementsStatus() {
            const toggleButton = document.getElementById('advancedFiltersToggle');
            const filtersSection = document.getElementById('advancedFilters');
            const toggleText = toggleButton?.querySelector('span');
            
            let html = '<div class="mt-2"><strong>Status Elemente:</strong><br>';
            html += `Toggle Button: ${toggleButton ? '✅ Găsit' : '❌ Lipsește'}<br>`;
            html += `Filters Section: ${filtersSection ? '✅ Găsit' : '❌ Lipsește'}<br>`;
            html += `Toggle Text: ${toggleText ? '✅ Găsit' : '❌ Lipsește'}<br>`;
            
            if (filtersSection) {
                const hasShow = filtersSection.classList.contains('show');
                const computedStyle = window.getComputedStyle(filtersSection);
                html += `<br><strong>CSS Status:</strong><br>`;
                html += `Clasa "show": ${hasShow ? '✅ Prezentă' : '❌ Lipsește'}<br>`;
                html += `Max-height: ${computedStyle.maxHeight}<br>`;
                html += `Opacity: ${computedStyle.opacity}<br>`;
                html += `Display: ${computedStyle.display}<br>`;
            }
            
            html += '</div>';
            document.getElementById('testResult').innerHTML = html;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            initAdvancedFiltersToggle();
            
            // Test initial status
            setTimeout(checkElementsStatus, 500);
        });
    </script>
</body>
</html>
