<?php
/**
 * Test both legacy and PSR-4 architectures for the 550+ party case
 * to ensure they produce consistent results
 */

echo "<h1>🔄 Architecture Comparison Test</h1>\n";
echo "<p><strong>Case:</strong> 130/98/2022 from CurteadeApelBUCURESTI</p>\n";
echo "<p><strong>Goal:</strong> Verify both legacy and PSR-4 versions extract 550+ parties consistently</p>\n";

// Test Legacy Architecture
echo "<h2>🏛️ Legacy Architecture Test</h2>\n";

try {
    require_once 'bootstrap.php';
    require_once 'includes/config.php';
    require_once 'includes/functions.php';
    require_once 'services/DosarService.php';
    
    $legacyService = new DosarService();
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    $startTime = microtime(true);
    $legacyResult = $legacyService->getDetaliiDosar($numarDosar, $institutie);
    $legacyTime = round((microtime(true) - $startTime) * 1000, 2);
    
    if ($legacyResult && !empty($legacyResult->numar)) {
        $legacyTotal = count($legacyResult->parti ?? []);
        
        // Count by source
        $legacySoap = 0;
        $legacyDecision = 0;
        $legacyUnknown = 0;
        
        foreach ($legacyResult->parti as $parte) {
            switch ($parte->source ?? 'unknown') {
                case 'soap_api': $legacySoap++; break;
                case 'decision_text': $legacyDecision++; break;
                default: $legacyUnknown++; break;
            }
        }
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "<h3 style='margin-top: 0; color: #0066cc;'>✅ Legacy Results</h3>\n";
        echo "<p><strong>Total Parties:</strong> <span style='font-size: 1.5em; color: " . ($legacyTotal >= 550 ? '#28a745' : ($legacyTotal >= 200 ? '#ffc107' : '#dc3545')) . ";'>{$legacyTotal}</span></p>\n";
        echo "<p><strong>SOAP API:</strong> {$legacySoap}</p>\n";
        echo "<p><strong>Decision Text:</strong> {$legacyDecision}</p>\n";
        echo "<p><strong>Unknown:</strong> {$legacyUnknown}</p>\n";
        echo "<p><strong>Processing Time:</strong> {$legacyTime}ms</p>\n";
        echo "<p><strong>Status:</strong> " . ($legacyTotal >= 550 ? "🎯 Target achieved" : "❌ Below target") . "</p>\n";
        echo "</div>\n";
        
    } else {
        echo "<p style='color: #dc3545;'><strong>❌ Legacy: Case not found</strong></p>\n";
        $legacyTotal = 0;
        $legacySoap = 0;
        $legacyDecision = 0;
        $legacyTime = 0;
    }
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'><strong>❌ Legacy Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    $legacyTotal = 0;
    $legacySoap = 0;
    $legacyDecision = 0;
    $legacyTime = 0;
}

// Test PSR-4 Architecture
echo "<h2>🏗️ PSR-4 Architecture Test</h2>\n";

try {
    // Reset and load PSR-4
    if (class_exists('DosarService')) {
        // Clear the legacy class from memory if possible
        unset($legacyService);
    }
    
    require_once dirname(__DIR__) . '/bootstrap.php';
    use App\Services\DosarService as PSR4DosarService;
    
    $psr4Service = new PSR4DosarService();
    
    $startTime = microtime(true);
    $psr4Result = $psr4Service->getDetaliiDosar($numarDosar, $institutie);
    $psr4Time = round((microtime(true) - $startTime) * 1000, 2);
    
    if ($psr4Result && !empty($psr4Result->numar)) {
        $psr4Total = count($psr4Result->parti ?? []);
        
        // Count by source
        $psr4Soap = 0;
        $psr4Decision = 0;
        $psr4Unknown = 0;
        
        foreach ($psr4Result->parti as $parte) {
            switch ($parte->source ?? 'unknown') {
                case 'soap_api': $psr4Soap++; break;
                case 'decision_text': $psr4Decision++; break;
                default: $psr4Unknown++; break;
            }
        }
        
        echo "<div style='background: #f0f8e7; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "<h3 style='margin-top: 0; color: #28a745;'>✅ PSR-4 Results</h3>\n";
        echo "<p><strong>Total Parties:</strong> <span style='font-size: 1.5em; color: " . ($psr4Total >= 550 ? '#28a745' : ($psr4Total >= 200 ? '#ffc107' : '#dc3545')) . ";'>{$psr4Total}</span></p>\n";
        echo "<p><strong>SOAP API:</strong> {$psr4Soap}</p>\n";
        echo "<p><strong>Decision Text:</strong> {$psr4Decision}</p>\n";
        echo "<p><strong>Unknown:</strong> {$psr4Unknown}</p>\n";
        echo "<p><strong>Processing Time:</strong> {$psr4Time}ms</p>\n";
        echo "<p><strong>Status:</strong> " . ($psr4Total >= 550 ? "🎯 Target achieved" : "❌ Below target") . "</p>\n";
        echo "</div>\n";
        
    } else {
        echo "<p style='color: #dc3545;'><strong>❌ PSR-4: Case not found</strong></p>\n";
        $psr4Total = 0;
        $psr4Soap = 0;
        $psr4Decision = 0;
        $psr4Time = 0;
    }
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'><strong>❌ PSR-4 Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    $psr4Total = 0;
    $psr4Soap = 0;
    $psr4Decision = 0;
    $psr4Time = 0;
}

// Comparison Analysis
echo "<h2>📊 Architecture Comparison</h2>\n";

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>\n";
echo "<thead style='background: #e9ecef;'>\n";
echo "<tr><th style='padding: 12px;'>Metric</th><th style='padding: 12px;'>Legacy</th><th style='padding: 12px;'>PSR-4</th><th style='padding: 12px;'>Difference</th><th style='padding: 12px;'>Status</th></tr>\n";
echo "</thead>\n";
echo "<tbody>\n";

// Total parties comparison
$totalDiff = $psr4Total - $legacyTotal;
$totalStatus = abs($totalDiff) <= 5 ? "✅ Consistent" : "⚠️ Different";
echo "<tr><td style='padding: 10px;'>Total Parties</td><td style='padding: 10px; text-align: center;'>{$legacyTotal}</td><td style='padding: 10px; text-align: center;'>{$psr4Total}</td><td style='padding: 10px; text-align: center;'>" . ($totalDiff >= 0 ? "+{$totalDiff}" : $totalDiff) . "</td><td style='padding: 10px;'>{$totalStatus}</td></tr>\n";

// SOAP parties comparison
$soapDiff = $psr4Soap - $legacySoap;
$soapStatus = abs($soapDiff) <= 2 ? "✅ Consistent" : "⚠️ Different";
echo "<tr><td style='padding: 10px;'>SOAP API Parties</td><td style='padding: 10px; text-align: center;'>{$legacySoap}</td><td style='padding: 10px; text-align: center;'>{$psr4Soap}</td><td style='padding: 10px; text-align: center;'>" . ($soapDiff >= 0 ? "+{$soapDiff}" : $soapDiff) . "</td><td style='padding: 10px;'>{$soapStatus}</td></tr>\n";

// Decision text parties comparison
$decisionDiff = $psr4Decision - $legacyDecision;
$decisionStatus = abs($decisionDiff) <= 5 ? "✅ Consistent" : "⚠️ Different";
echo "<tr><td style='padding: 10px;'>Decision Text Parties</td><td style='padding: 10px; text-align: center;'>{$legacyDecision}</td><td style='padding: 10px; text-align: center;'>{$psr4Decision}</td><td style='padding: 10px; text-align: center;'>" . ($decisionDiff >= 0 ? "+{$decisionDiff}" : $decisionDiff) . "</td><td style='padding: 10px;'>{$decisionStatus}</td></tr>\n";

// Performance comparison
$timeDiff = $psr4Time - $legacyTime;
$timeStatus = abs($timeDiff) <= 100 ? "✅ Similar" : ($timeDiff > 0 ? "⚠️ PSR-4 slower" : "⚠️ Legacy slower");
echo "<tr><td style='padding: 10px;'>Processing Time (ms)</td><td style='padding: 10px; text-align: center;'>{$legacyTime}</td><td style='padding: 10px; text-align: center;'>{$psr4Time}</td><td style='padding: 10px; text-align: center;'>" . ($timeDiff >= 0 ? "+{$timeDiff}" : $timeDiff) . "</td><td style='padding: 10px;'>{$timeStatus}</td></tr>\n";

echo "</tbody>\n";
echo "</table>\n";

// Overall Assessment
echo "<h3>🎯 Overall Assessment</h3>\n";

$overallSuccess = ($legacyTotal >= 550 || $psr4Total >= 550);
$architectureConsistency = (abs($totalDiff) <= 10);

if ($overallSuccess && $architectureConsistency) {
    echo "<div style='background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ SUCCESS: Target Achieved with Consistent Architectures</h4>\n";
    echo "<p style='color: #155724; margin: 0;'>Both architectures are extracting 550+ parties and producing consistent results.</p>\n";
    echo "</div>\n";
} elseif ($overallSuccess) {
    echo "<div style='background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0;'>\n";
    echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ PARTIAL SUCCESS: Target Achieved but Architectures Differ</h4>\n";
    echo "<p style='color: #856404; margin: 0;'>At least one architecture is extracting 550+ parties, but there are differences between legacy and PSR-4 results.</p>\n";
    echo "</div>\n";
} elseif ($architectureConsistency) {
    echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ CONSISTENT FAILURE: Both Architectures Under Target</h4>\n";
    echo "<p style='color: #721c24; margin: 0;'>Both architectures are producing consistent results but neither is reaching the 550+ party target.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ FAILURE: Target Not Met and Architectures Inconsistent</h4>\n";
    echo "<p style='color: #721c24; margin: 0;'>Neither architecture is reaching the target and they're producing different results.</p>\n";
    echo "</div>\n";
}

echo "<h2>🌐 Test URLs</h2>\n";
echo "<ul>\n";
echo "<li><strong>Legacy:</strong> <a href='detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI' target='_blank'>detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI</a></li>\n";
echo "<li><strong>PSR-4:</strong> <a href='public/detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI' target='_blank'>public/detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI</a></li>\n";
echo "</ul>\n";

echo "<h2>🔧 Next Steps</h2>\n";
echo "<p>Based on these results:</p>\n";
echo "<ul>\n";
if ($overallSuccess) {
    echo "<li>✅ <strong>Success:</strong> The enhanced extraction system is working</li>\n";
    echo "<li>🔍 Verify the results manually on the case detail pages</li>\n";
    echo "<li>📊 Check source attribution is working correctly</li>\n";
} else {
    echo "<li>🔍 <strong>Investigate:</strong> Why the enhanced patterns aren't extracting 550+ parties</li>\n";
    echo "<li>📝 Analyze the actual decision text content more deeply</li>\n";
    echo "<li>🎯 Consider that the case may not actually contain 550+ parties</li>\n";
}
echo "<li>🧪 Test with other known large cases to validate the system</li>\n";
echo "</ul>\n";
?>
