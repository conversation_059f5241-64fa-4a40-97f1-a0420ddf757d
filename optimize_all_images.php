<?php
require_once 'src/Helpers/PerformanceHelper.php';
use App\Helpers\PerformanceHelper;

echo "=== Batch Image Optimization ===\n";

// Directoarele unde să căutăm imagini
$directories = ['images', 'assets', 'public/assets', 'src/assets'];

// Extensiile de imagini suportate
$supportedExtensions = ['jpg', 'jpeg', 'png'];

$totalOriginalSize = 0;
$totalOptimizedSize = 0;
$optimizedCount = 0;

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        continue;
    }
    
    echo "\nScanning directory: $dir\n";
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $extension = strtolower($file->getExtension());
            
            if (in_array($extension, $supportedExtensions)) {
                $imagePath = $file->getPathname();
                $originalSize = filesize($imagePath);
                
                echo "  Processing: $imagePath ($originalSize bytes)\n";
                
                $webpPath = PerformanceHelper::optimizeImage($imagePath, 80);
                
                if ($webpPath && file_exists($webpPath)) {
                    $optimizedSize = filesize($webpPath);
                    $reduction = round((1 - $optimizedSize / $originalSize) * 100, 2);
                    
                    echo "    ✓ WebP created: $webpPath ($optimizedSize bytes, {$reduction}% reduction)\n";
                    
                    $totalOriginalSize += $originalSize;
                    $totalOptimizedSize += $optimizedSize;
                    $optimizedCount++;
                } else {
                    echo "    ✗ WebP optimization failed\n";
                }
            }
        }
    }
}

echo "\n=== Optimization Summary ===\n";
echo "Images optimized: $optimizedCount\n";
echo "Total original size: " . formatBytes($totalOriginalSize) . "\n";
echo "Total optimized size: " . formatBytes($totalOptimizedSize) . "\n";

if ($totalOriginalSize > 0) {
    $totalReduction = round((1 - $totalOptimizedSize / $totalOriginalSize) * 100, 2);
    echo "Total size reduction: {$totalReduction}%\n";
    echo "Space saved: " . formatBytes($totalOriginalSize - $totalOptimizedSize) . "\n";
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== WebP Files Created ===\n";
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        continue;
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && strtolower($file->getExtension()) === 'webp') {
            $webpPath = $file->getPathname();
            $size = filesize($webpPath);
            echo "  ✓ $webpPath (" . formatBytes($size) . ")\n";
        }
    }
}
?>
