<?php
/**
 * Test script to analyze decision text content for party extraction
 * Specifically checks if "Saragea Tudorita" exists in decision text
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>Decision Text Analysis for Party Extraction</h1>\n";
echo "<p>Analyzing decision text content to understand why 'Saragea Tudorita' might not be found</p>\n";

try {
    // Initialize service
    $dosarService = new DosarService();
    
    // Test parameters
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    echo "<h2>1. Raw SOAP Response Analysis</h2>\n";
    
    // Get raw SOAP response using reflection to access private method
    $reflection = new ReflectionClass($dosarService);
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $response = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Test call");
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        $dosar = is_array($dosare) ? $dosare[0] : $dosare;
        
        echo "<h3>✅ Raw SOAP Response Retrieved</h3>\n";
        echo "<p><strong>Case Number:</strong> " . ($dosar->numar ?? 'N/A') . "</p>\n";
        echo "<p><strong>Institution:</strong> " . ($dosar->institutie ?? 'N/A') . "</p>\n";
        
        // Analyze SOAP parties
        $soapPartyCount = 0;
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $parti = $dosar->parti->DosarParte;
            if (is_array($parti)) {
                $soapPartyCount = count($parti);
            } else {
                $soapPartyCount = 1;
            }
        }
        echo "<p><strong>SOAP API Parties Count:</strong> {$soapPartyCount}</p>\n";
        
        // Check if we hit the 100-party limit
        if ($soapPartyCount >= 100) {
            echo "<p style='color: orange;'><strong>⚠️ SOAP API limit reached ({$soapPartyCount} parties) - Decision text parsing should be triggered</strong></p>\n";
        } else {
            echo "<p style='color: green;'><strong>✅ SOAP API under limit ({$soapPartyCount} parties) - Decision text parsing may not be needed</strong></p>\n";
        }
        
        // Analyze decision text content
        echo "<h3>📄 Decision Text Analysis</h3>\n";
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            echo "<p><strong>Number of Court Sessions:</strong> " . count($sedinte) . "</p>\n";
            
            $sessionCount = 0;
            $totalDecisionTextLength = 0;
            $saragea_found_in_text = false;
            
            foreach ($sedinte as $sedinta) {
                $sessionCount++;
                echo "<h4>Session {$sessionCount}:</h4>\n";
                echo "<p><strong>Date:</strong> " . ($sedinta->data ?? 'N/A') . "</p>\n";
                echo "<p><strong>Time:</strong> " . ($sedinta->ora ?? 'N/A') . "</p>\n";
                
                // Check solutie field
                if (isset($sedinta->solutie) && !empty($sedinta->solutie)) {
                    $solutieLength = strlen($sedinta->solutie);
                    $totalDecisionTextLength += $solutieLength;
                    echo "<p><strong>Solution Text Length:</strong> {$solutieLength} characters</p>\n";
                    
                    // Search for Saragea in decision text
                    if (stripos($sedinta->solutie, 'SARAGEA') !== false) {
                        echo "<p style='color: green;'><strong>🎯 'SARAGEA' found in solution text!</strong></p>\n";
                        $saragea_found_in_text = true;
                        
                        // Show context around Saragea
                        $pos = stripos($sedinta->solutie, 'SARAGEA');
                        $start = max(0, $pos - 100);
                        $context = substr($sedinta->solutie, $start, 200);
                        echo "<p><strong>Context:</strong> ..." . htmlspecialchars($context) . "...</p>\n";
                    }
                    
                    if (stripos($sedinta->solutie, 'TUDORITA') !== false) {
                        echo "<p style='color: green;'><strong>🎯 'TUDORITA' found in solution text!</strong></p>\n";
                        
                        // Show context around Tudorita
                        $pos = stripos($sedinta->solutie, 'TUDORITA');
                        $start = max(0, $pos - 100);
                        $context = substr($sedinta->solutie, $start, 200);
                        echo "<p><strong>Context:</strong> ..." . htmlspecialchars($context) . "...</p>\n";
                    }
                } else {
                    echo "<p><strong>Solution Text:</strong> Empty or not available</p>\n";
                }
                
                // Check solutieSumar field
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $sumarLength = strlen($sedinta->solutieSumar);
                    $totalDecisionTextLength += $sumarLength;
                    echo "<p><strong>Summary Text Length:</strong> {$sumarLength} characters</p>\n";
                    
                    // Search for Saragea in summary text
                    if (stripos($sedinta->solutieSumar, 'SARAGEA') !== false) {
                        echo "<p style='color: green;'><strong>🎯 'SARAGEA' found in summary text!</strong></p>\n";
                        $saragea_found_in_text = true;
                    }
                    
                    if (stripos($sedinta->solutieSumar, 'TUDORITA') !== false) {
                        echo "<p style='color: green;'><strong>🎯 'TUDORITA' found in summary text!</strong></p>\n";
                    }
                } else {
                    echo "<p><strong>Summary Text:</strong> Empty or not available</p>\n";
                }
                
                echo "<hr>\n";
            }
            
            echo "<p><strong>Total Decision Text Length:</strong> {$totalDecisionTextLength} characters</p>\n";
            
            if ($saragea_found_in_text) {
                echo "<p style='color: green;'><strong>✅ 'SARAGEA' found in decision text - hybrid extraction should work</strong></p>\n";
            } else {
                echo "<p style='color: red;'><strong>❌ 'SARAGEA' NOT found in decision text - this explains why it's missing</strong></p>\n";
            }
            
        } else {
            echo "<p style='color: red;'><strong>❌ No court sessions found in SOAP response</strong></p>\n";
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ No case data found in SOAP response</strong></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>2. Conclusion</h2>\n";
echo "<p>This analysis helps determine whether:</p>\n";
echo "<ul>\n";
echo "<li>The SOAP API returns exactly 100 parties (triggering decision text parsing)</li>\n";
echo "<li>The decision text actually contains 'Saragea Tudorita'</li>\n";
echo "<li>The hybrid extraction system is working as expected</li>\n";
echo "</ul>\n";
echo "<p><strong>If 'Saragea Tudorita' is not in the decision text, then the hybrid system is working correctly by not extracting it.</strong></p>\n";
