<?php

namespace App\Helpers;

/**
 * Image Optimization Helper - Gestionează optimizarea imaginilor pentru SEO
 */
class ImageOptimizationHelper
{
    /**
     * Generează alt text pentru iconuri Font Awesome
     */
    public static function getIconAltText($iconClass, $context = '')
    {
        $iconMap = [
            'fas fa-gavel' => 'Iconiță ciocan de judecător',
            'fas fa-search' => 'Iconiță căutare',
            'fas fa-calendar-alt' => 'Iconiță calendar',
            'fas fa-envelope' => 'Iconiță email',
            'fas fa-phone' => 'Iconiță telefon',
            'fas fa-map-marker-alt' => 'Iconiță locație',
            'fas fa-home' => 'Iconiță acasă',
            'fas fa-file-pdf' => 'Iconiță PDF',
            'fas fa-file-excel' => 'Iconiță Excel',
            'fas fa-file-csv' => 'Iconiță CSV',
            'fas fa-download' => 'Iconiță descărcare',
            'fas fa-external-link-alt' => 'Iconiță link extern',
            'fas fa-filter' => 'Iconiță filtru',
            'fas fa-sort' => 'Iconiță sortare',
            'fas fa-sort-up' => 'Iconiță sortare crescătoare',
            'fas fa-sort-down' => 'Iconiță sortare descrescătoare',
            'fas fa-eye' => 'Iconiță vizualizare',
            'fas fa-edit' => 'Iconiță editare',
            'fas fa-trash' => 'Iconiță ștergere',
            'fas fa-plus' => 'Iconiță adăugare',
            'fas fa-minus' => 'Iconiță scădere',
            'fas fa-check' => 'Iconiță confirmare',
            'fas fa-times' => 'Iconiță închidere',
            'fas fa-info-circle' => 'Iconiță informații',
            'fas fa-exclamation-triangle' => 'Iconiță avertisment',
            'fas fa-spinner' => 'Iconiță încărcare',
            'fas fa-cog' => 'Iconiță setări',
            'fas fa-user' => 'Iconiță utilizator',
            'fas fa-users' => 'Iconiță utilizatori',
            'fas fa-building' => 'Iconiță clădire',
            'fas fa-balance-scale' => 'Iconiță balanță justiție',
            'fas fa-bookmark' => 'Iconiță marcaj',
            'fas fa-heart' => 'Iconiță inimă',
            'fas fa-star' => 'Iconiță stea',
            'fas fa-print' => 'Iconiță imprimare',
            'fas fa-share' => 'Iconiță partajare',
            'fas fa-copy' => 'Iconiță copiere',
            'fas fa-clipboard' => 'Iconiță clipboard',
            'fas fa-list' => 'Iconiță listă',
            'fas fa-table' => 'Iconiță tabel',
            'fas fa-th' => 'Iconiță grilă',
            'fas fa-bars' => 'Iconiță meniu',
            'fas fa-arrow-left' => 'Iconiță săgeată stânga',
            'fas fa-arrow-right' => 'Iconiță săgeată dreapta',
            'fas fa-arrow-up' => 'Iconiță săgeată sus',
            'fas fa-arrow-down' => 'Iconiță săgeată jos',
            'fas fa-chevron-left' => 'Iconiță chevron stânga',
            'fas fa-chevron-right' => 'Iconiță chevron dreapta',
            'fas fa-chevron-up' => 'Iconiță chevron sus',
            'fas fa-chevron-down' => 'Iconiță chevron jos'
        ];
        
        // Adăugăm contextul dacă este specificat
        $baseAlt = $iconMap[$iconClass] ?? 'Iconiță';
        
        if (!empty($context)) {
            return $baseAlt . ' - ' . $context;
        }
        
        return $baseAlt;
    }
    
    /**
     * Generează alt text pentru logo-uri și imagini specifice
     */
    public static function getImageAltText($imagePath, $context = '')
    {
        $filename = basename($imagePath);
        $imageMap = [
            'logo.png' => 'Logo Portal Judiciar România',
            'logo.jpg' => 'Logo Portal Judiciar România',
            'logo.svg' => 'Logo Portal Judiciar România',
            'favicon.ico' => 'Favicon Portal Judiciar România',
            'justice.png' => 'Simbol justiție România',
            'courthouse.jpg' => 'Clădire tribunal România',
            'gavel.png' => 'Ciocan de judecător',
            'scales.png' => 'Balanță justiție',
            'flag.png' => 'Steagul României',
            'emblem.png' => 'Emblema României'
        ];
        
        $baseAlt = $imageMap[$filename] ?? 'Imagine Portal Judiciar';
        
        if (!empty($context)) {
            return $baseAlt . ' - ' . $context;
        }
        
        return $baseAlt;
    }
    
    /**
     * Generează tag-uri de imagine optimizate pentru SEO
     */
    public static function generateOptimizedImageTag($src, $alt, $options = [])
    {
        $defaults = [
            'class' => '',
            'width' => null,
            'height' => null,
            'loading' => 'lazy',
            'decoding' => 'async',
            'title' => null
        ];
        
        $options = array_merge($defaults, $options);
        
        $attributes = [
            'src="' . htmlspecialchars($src) . '"',
            'alt="' . htmlspecialchars($alt) . '"'
        ];
        
        if (!empty($options['class'])) {
            $attributes[] = 'class="' . htmlspecialchars($options['class']) . '"';
        }
        
        if ($options['width']) {
            $attributes[] = 'width="' . intval($options['width']) . '"';
        }
        
        if ($options['height']) {
            $attributes[] = 'height="' . intval($options['height']) . '"';
        }
        
        if ($options['loading']) {
            $attributes[] = 'loading="' . htmlspecialchars($options['loading']) . '"';
        }
        
        if ($options['decoding']) {
            $attributes[] = 'decoding="' . htmlspecialchars($options['decoding']) . '"';
        }
        
        if ($options['title']) {
            $attributes[] = 'title="' . htmlspecialchars($options['title']) . '"';
        }
        
        return '<img ' . implode(' ', $attributes) . '>';
    }
    
    /**
     * Generează structured data pentru imagini
     */
    public static function generateImageStructuredData($imageUrl, $caption, $description = '')
    {
        $structuredData = [
            '@type' => 'ImageObject',
            'url' => $imageUrl,
            'caption' => $caption
        ];
        
        if (!empty($description)) {
            $structuredData['description'] = $description;
        }
        
        return $structuredData;
    }
    
    /**
     * Verifică dacă o imagine există și returnează dimensiunile
     */
    public static function getImageDimensions($imagePath)
    {
        if (!file_exists($imagePath)) {
            return null;
        }
        
        $imageInfo = getimagesize($imagePath);
        if ($imageInfo === false) {
            return null;
        }
        
        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'type' => $imageInfo[2],
            'mime' => $imageInfo['mime']
        ];
    }
    
    /**
     * Generează srcset pentru imagini responsive
     */
    public static function generateSrcSet($basePath, $sizes = [])
    {
        $srcSet = [];
        
        foreach ($sizes as $size => $suffix) {
            $imagePath = str_replace('.', $suffix . '.', $basePath);
            if (file_exists($imagePath)) {
                $srcSet[] = $imagePath . ' ' . $size . 'w';
            }
        }
        
        return implode(', ', $srcSet);
    }
    
    /**
     * Optimizează imaginile pentru web (placeholder pentru funcționalitate viitoare)
     */
    public static function optimizeImage($sourcePath, $destinationPath, $quality = 85)
    {
        // Placeholder pentru optimizarea imaginilor
        // Poate fi implementată cu GD, ImageMagick sau alte biblioteci
        return copy($sourcePath, $destinationPath);
    }
    
    /**
     * Generează placeholder pentru imagini care se încarcă
     */
    public static function generatePlaceholder($width, $height, $text = 'Se încarcă...')
    {
        $svg = '<svg width="' . $width . '" height="' . $height . '" xmlns="http://www.w3.org/2000/svg">';
        $svg .= '<rect width="100%" height="100%" fill="#f8f9fa"/>';
        $svg .= '<text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" fill="#6c757d" text-anchor="middle" dy=".3em">';
        $svg .= htmlspecialchars($text);
        $svg .= '</text>';
        $svg .= '</svg>';
        
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }
}
