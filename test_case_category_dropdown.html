<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Case Category Dropdown</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Searchable dropdown styles */
        .searchable-select-container {
            position: relative;
            margin-bottom: 0;
        }

        .searchable-select-input {
            border-radius: 0.375rem;
            border: 1px solid #ced4da;
            padding: 0.375rem 0.75rem;
            width: 100%;
            font-size: 1rem;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .searchable-select-input:focus {
            border-color: #007bff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .searchable-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #ffffff;
            border: 1px solid #007bff;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            max-height: 250px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: all 0.2s ease;
            font-size: 0.95rem;
            line-height: 1.4;
        }

        .dropdown-item:hover,
        .dropdown-item.highlighted {
            background-color: #f8f9fa;
            color: #007bff;
        }

        .dropdown-item.selected {
            background-color: #007bff;
            color: white;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }

        .test-pass {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .test-fail {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Test Case Category Dropdown</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Case Category Dropdown Test</h3>
                <div class="form-group">
                    <label for="categorieCaz">Categorie caz:</label>
                    <div class="searchable-select-container">
                        <input type="text" class="form-control searchable-select-input" id="categorieCazSearch" placeholder="Căutați și selectați o categorie..." autocomplete="off">
                        <select class="form-select d-none" id="categorieCaz" name="categorieCaz">
                            <option value="">-- Toate categoriile --</option>
                            <option value="civil">Civil</option>
                            <option value="penal">Penal</option>
                            <option value="comercial">Comercial</option>
                            <option value="contencios_administrativ">Contencios Administrativ</option>
                            <option value="fiscal">Fiscal</option>
                            <option value="munca">Muncă și Asigurări Sociale</option>
                            <option value="familie">Familie și Minori</option>
                            <option value="executare">Executare</option>
                            <option value="insolventa">Insolvență</option>
                        </select>
                        <div class="searchable-select-dropdown" id="categorieCazDropdown"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Selected Value:</label>
                    <input type="text" id="selectedValue" class="form-control" readonly>
                </div>
                
                <div class="form-group">
                    <label>Selected Text:</label>
                    <input type="text" id="selectedText" class="form-control" readonly>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Test Results</h3>
                <div id="testResults"></div>
                
                <button class="btn btn-primary" onclick="runTests()">Run All Tests</button>
                <button class="btn btn-secondary" onclick="testRomanianDiacritics()">Test Romanian Diacritics</button>
                <button class="btn btn-info" onclick="testKeyboardNavigation()">Test Keyboard Navigation</button>
            </div>
        </div>
    </div>

    <script>
        // Romanian diacritics normalization function
        function normalizeRomanianText(text) {
            if (!text) return '';
            return text.toLowerCase()
                .replace(/ă/g, 'a')
                .replace(/â/g, 'a')
                .replace(/î/g, 'i')
                .replace(/ș/g, 's')
                .replace(/ț/g, 't');
        }

        // Initialize searchable case category dropdown
        function initSearchableCaseCategoryDropdown() {
            const searchInput = document.getElementById('categorieCazSearch');
            const hiddenSelect = document.getElementById('categorieCaz');
            const dropdown = document.getElementById('categorieCazDropdown');
            
            if (!searchInput || !hiddenSelect || !dropdown) return;

            let options = [];
            let highlightedIndex = -1;

            // Build options array from select element
            Array.from(hiddenSelect.options).forEach(option => {
                if (option.value) {
                    options.push({
                        value: option.value,
                        text: option.textContent.trim()
                    });
                }
            });

            function renderDropdown(filteredOptions) {
                dropdown.innerHTML = '';
                
                if (filteredOptions.length === 0) {
                    dropdown.innerHTML = '<div class="dropdown-item text-muted">Nu au fost găsite rezultate</div>';
                    dropdown.style.display = 'block';
                    return;
                }

                filteredOptions.forEach((option, index) => {
                    const item = document.createElement('div');
                    item.className = 'dropdown-item';
                    item.textContent = option.text;
                    item.dataset.value = option.value;
                    item.dataset.index = index;
                    
                    item.addEventListener('click', function() {
                        selectOption(option);
                    });
                    
                    dropdown.appendChild(item);
                });
                
                dropdown.style.display = 'block';
                highlightedIndex = -1;
            }

            function selectOption(option) {
                searchInput.value = option.text;
                hiddenSelect.value = option.value;
                dropdown.style.display = 'none';
                highlightedIndex = -1;
                
                // Update test display
                document.getElementById('selectedValue').value = option.value;
                document.getElementById('selectedText').value = option.text;
            }

            function filterOptions(searchTerm) {
                const normalizedSearch = normalizeRomanianText(searchTerm);
                return options.filter(option => 
                    normalizeRomanianText(option.text).includes(normalizedSearch)
                );
            }

            // Event listeners
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length === 0) {
                    dropdown.style.display = 'none';
                    hiddenSelect.value = '';
                    document.getElementById('selectedValue').value = '';
                    document.getElementById('selectedText').value = '';
                    return;
                }

                const filteredOptions = filterOptions(searchTerm);
                renderDropdown(filteredOptions);
            });

            searchInput.addEventListener('focus', function() {
                if (this.value.trim().length > 0) {
                    const filteredOptions = filterOptions(this.value.trim());
                    renderDropdown(filteredOptions);
                }
            });

            searchInput.addEventListener('keydown', function(e) {
                const items = dropdown.querySelectorAll('.dropdown-item:not(.text-muted)');
                
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    highlightedIndex = Math.min(highlightedIndex + 1, items.length - 1);
                    updateHighlight(items);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    highlightedIndex = Math.max(highlightedIndex - 1, -1);
                    updateHighlight(items);
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (highlightedIndex >= 0 && items[highlightedIndex]) {
                        const value = items[highlightedIndex].dataset.value;
                        const text = items[highlightedIndex].textContent;
                        selectOption({ value, text });
                    }
                } else if (e.key === 'Escape') {
                    dropdown.style.display = 'none';
                    highlightedIndex = -1;
                }
            });

            function updateHighlight(items) {
                items.forEach((item, index) => {
                    item.classList.toggle('highlighted', index === highlightedIndex);
                });
            }

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                    highlightedIndex = -1;
                }
            });
        }

        // Test functions
        function addTestResult(testName, passed, message) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            resultDiv.innerHTML = `<strong>${testName}:</strong> ${passed ? 'PASS' : 'FAIL'} - ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function runTests() {
            document.getElementById('testResults').innerHTML = '';
            
            // Test 1: Check all 9 categories are loaded
            const select = document.getElementById('categorieCaz');
            const options = Array.from(select.options).filter(opt => opt.value);
            const expectedCategories = ['civil', 'penal', 'comercial', 'contencios_administrativ', 'fiscal', 'munca', 'familie', 'executare', 'insolventa'];
            const actualCategories = options.map(opt => opt.value);
            
            const allCategoriesPresent = expectedCategories.every(cat => actualCategories.includes(cat));
            addTestResult('All 9 Categories Loaded', allCategoriesPresent, `Found ${actualCategories.length}/9 categories: ${actualCategories.join(', ')}`);
            
            // Test 2: Check dropdown initialization
            const searchInput = document.getElementById('categorieCazSearch');
            const dropdown = document.getElementById('categorieCazDropdown');
            const elementsExist = searchInput && dropdown && select;
            addTestResult('Dropdown Elements Exist', elementsExist, elementsExist ? 'All required elements found' : 'Missing required elements');
            
            // Test 3: Test search functionality
            if (searchInput) {
                searchInput.value = 'civil';
                searchInput.dispatchEvent(new Event('input'));
                setTimeout(() => {
                    const dropdownItems = dropdown.querySelectorAll('.dropdown-item:not(.text-muted)');
                    const searchWorks = dropdownItems.length > 0;
                    addTestResult('Search Functionality', searchWorks, searchWorks ? `Found ${dropdownItems.length} matching items` : 'No search results found');
                }, 100);
            }
        }

        function testRomanianDiacritics() {
            const testCases = [
                { input: 'munca', expected: 'munca' },
                { input: 'muncă', expected: 'munca' },
                { input: 'Muncă și Asigurări', expected: 'munca si asigurari' }
            ];
            
            testCases.forEach(testCase => {
                const result = normalizeRomanianText(testCase.input);
                const passed = result.includes(testCase.expected.split(' ')[0]);
                addTestResult(`Romanian Diacritics: "${testCase.input}"`, passed, `Normalized to: "${result}"`);
            });
        }

        function testKeyboardNavigation() {
            const searchInput = document.getElementById('categorieCazSearch');
            searchInput.value = 'c';
            searchInput.dispatchEvent(new Event('input'));
            
            setTimeout(() => {
                // Simulate arrow down key
                const arrowDownEvent = new KeyboardEvent('keydown', { key: 'ArrowDown' });
                searchInput.dispatchEvent(arrowDownEvent);
                
                const highlightedItems = document.querySelectorAll('.dropdown-item.highlighted');
                const keyboardNavWorks = highlightedItems.length > 0;
                addTestResult('Keyboard Navigation', keyboardNavWorks, keyboardNavWorks ? 'Arrow key navigation working' : 'Keyboard navigation not working');
            }, 100);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initSearchableCaseCategoryDropdown();
            addTestResult('Initialization', true, 'Dropdown initialized successfully');
        });
    </script>
</body>
</html>
