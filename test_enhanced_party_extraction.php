<?php
/**
 * Test enhanced party extraction with hybrid approach
 */

require_once 'config/config.php';
require_once 'services/DosarService.php';

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "=== TESTING ENHANCED PARTY EXTRACTION ===" . PHP_EOL;
echo "Case: $numarDosar" . PHP_EOL;
echo "Institution: $institutie" . PHP_EOL;
echo PHP_EOL;

try {
    // Initialize DosarService
    $dosarService = new DosarService();
    
    echo "=== GETTING CASE DETAILS WITH ENHANCED EXTRACTION ===" . PHP_EOL;
    
    // Get case details using enhanced extraction
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if ($dosar) {
        echo "✓ Case details retrieved successfully" . PHP_EOL;
        echo "Case number: " . $dosar->numar . PHP_EOL;
        echo "Institution: " . $dosar->institutie . PHP_EOL;
        echo "Category: " . $dosar->categorieCazNume . PHP_EOL;
        echo "Object: " . $dosar->obiect . PHP_EOL;
        echo PHP_EOL;
        
        // Analyze parties
        echo "=== PARTY ANALYSIS ===" . PHP_EOL;
        echo "Total parties found: " . count($dosar->parti) . PHP_EOL;
        echo PHP_EOL;
        
        // Count parties by quality
        $qualityCounts = [];
        foreach ($dosar->parti as $parte) {
            $calitate = $parte['calitate'] ?? 'Unknown';
            $qualityCounts[$calitate] = ($qualityCounts[$calitate] ?? 0) + 1;
        }
        
        echo "Parties by quality:" . PHP_EOL;
        foreach ($qualityCounts as $calitate => $count) {
            echo "- $calitate: $count" . PHP_EOL;
        }
        echo PHP_EOL;
        
        // Show first 20 parties
        echo "First 20 parties:" . PHP_EOL;
        for ($i = 0; $i < min(20, count($dosar->parti)); $i++) {
            $parte = $dosar->parti[$i];
            echo ($i + 1) . ". " . $parte['nume'] . " (" . $parte['calitate'] . ")" . PHP_EOL;
        }
        
        if (count($dosar->parti) > 40) {
            echo "..." . PHP_EOL;
            echo "Last 20 parties:" . PHP_EOL;
            $start = max(0, count($dosar->parti) - 20);
            for ($i = $start; $i < count($dosar->parti); $i++) {
                $parte = $dosar->parti[$i];
                echo ($i + 1) . ". " . $parte['nume'] . " (" . $parte['calitate'] . ")" . PHP_EOL;
            }
        }
        
        echo PHP_EOL;
        
        // Check if we have more than 100 parties (indicating successful enhancement)
        if (count($dosar->parti) > 100) {
            echo "🎉 SUCCESS: Enhanced extraction working!" . PHP_EOL;
            echo "Found " . count($dosar->parti) . " parties (more than SOAP API limit of 100)" . PHP_EOL;
        } elseif (count($dosar->parti) == 100) {
            echo "⚠ WARNING: Still showing exactly 100 parties" . PHP_EOL;
            echo "Enhancement may not be working or decision text extraction failed" . PHP_EOL;
        } else {
            echo "ℹ INFO: Found " . count($dosar->parti) . " parties (less than 100)" . PHP_EOL;
        }
        
        echo PHP_EOL;
        
        // Analyze sessions for decision text
        echo "=== SESSION ANALYSIS ===" . PHP_EOL;
        echo "Total sessions: " . count($dosar->sedinte) . PHP_EOL;
        
        $sessionsWithDecision = 0;
        foreach ($dosar->sedinte as $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $sessionsWithDecision++;
                echo "Session with decision text: " . $sedinta['data'] . " - " . strlen($sedinta['solutieSumar']) . " chars" . PHP_EOL;
            }
        }
        
        echo "Sessions with decision text: $sessionsWithDecision" . PHP_EOL;
        
        if ($sessionsWithDecision == 0) {
            echo "⚠ WARNING: No sessions with decision text found!" . PHP_EOL;
            echo "This may explain why enhancement is not working." . PHP_EOL;
        }
        
    } else {
        echo "❌ ERROR: Could not retrieve case details" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace:" . PHP_EOL;
    echo $e->getTraceAsString() . PHP_EOL;
}

echo PHP_EOL . "=== TEST COMPLETE ===" . PHP_EOL;
?>
