<?php
/**
 * Test Actual Case Details Page
 * Direct test of the actual detalii_dosar.php page with debug information
 */

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Actual Case Page Test</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
.test-link:hover { background: #0056b3; color: white; text-decoration: none; }
</style></head><body>";

echo "<h1>🔍 Actual Case Details Page Test</h1>";
echo "<p><strong>Objective:</strong> Test the actual detalii_dosar.php page to see party display</p>";
echo "<hr>";

// Test case with known high party count
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<div class='section'>";
echo "<h2>📋 Test Links</h2>";
echo "<p>Click the links below to test the actual case details page:</p>";

// Standard link
$standardUrl = "detalii_dosar.php?numar=" . urlencode($numarDosar) . "&institutie=" . urlencode($institutie);
echo "<a href='{$standardUrl}' class='test-link' target='_blank'>";
echo "<i class='fas fa-external-link-alt'></i> Standard Case Details Page";
echo "</a>";

// Debug link
$debugUrl = "detalii_dosar.php?numar=" . urlencode($numarDosar) . "&institutie=" . urlencode($institutie) . "&debug=1";
echo "<a href='{$debugUrl}' class='test-link' target='_blank'>";
echo "<i class='fas fa-bug'></i> Case Details Page with Debug";
echo "</a>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>🔧 JavaScript Console Test</h2>";
echo "<p>After opening the case details page, run this JavaScript in the browser console:</p>";
echo "<div style='background: #f1f3f4; padding: 10px; font-family: monospace; white-space: pre-wrap; border: 1px solid #ddd;'>";
echo "// Party count verification script
console.log('🔍 Party Count Verification');

// Count table rows
const table = document.getElementById('tabelParti');
const tbody = table ? table.querySelector('tbody') : null;
const allRows = tbody ? tbody.querySelectorAll('tr') : [];
const partyRows = tbody ? tbody.querySelectorAll('tr.parte-row') : [];

console.log('Table Analysis:', {
    tableFound: !!table,
    tbodyFound: !!tbody,
    totalRows: allRows.length,
    partyRows: partyRows.length
});

// Check for hidden rows
const hiddenRows = Array.from(partyRows).filter(row => {
    const style = window.getComputedStyle(row);
    return style.display === 'none' || style.visibility === 'hidden';
});

console.log('Visibility Analysis:', {
    visibleParties: partyRows.length - hiddenRows.length,
    hiddenParties: hiddenRows.length,
    totalParties: partyRows.length
});

// Check party counter
const partiCounter = document.querySelector('.parti-counter');
if (partiCounter) {
    console.log('Party Counter Text:', partiCounter.textContent);
} else {
    console.log('⚠️ Party counter not found');
}

// Sample party names
if (partyRows.length > 0) {
    console.log('First 5 parties:');
    for (let i = 0; i < Math.min(5, partyRows.length); i++) {
        const nameCell = partyRows[i].querySelector('.nume-parte');
        console.log((i + 1) + '. ' + (nameCell ? nameCell.textContent : 'N/A'));
    }
    
    if (partyRows.length > 5) {
        console.log('Last 5 parties:');
        const start = Math.max(0, partyRows.length - 5);
        for (let i = start; i < partyRows.length; i++) {
            const nameCell = partyRows[i].querySelector('.nume-parte');
            console.log((i + 1) + '. ' + (nameCell ? nameCell.textContent : 'N/A'));
        }
    }
}

// Final result
if (partyRows.length >= 340) {
    console.log('✅ SUCCESS: Found ' + partyRows.length + ' parties (expected 340+)');
} else if (partyRows.length === 100) {
    console.log('❌ ISSUE: Only 100 parties found (SOAP API limit)');
} else {
    console.log('⚠️ UNEXPECTED: Found ' + partyRows.length + ' parties');
}";
echo "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📊 Expected Results</h2>";
echo "<p>Based on our investigation, we expect:</p>";
echo "<ul>";
echo "<li><strong>Backend:</strong> 340+ parties from hybrid extraction</li>";
echo "<li><strong>Frontend:</strong> All 340+ parties should be rendered in the table</li>";
echo "<li><strong>DOM:</strong> 340+ &lt;tr class='parte-row'&gt; elements</li>";
echo "<li><strong>Visibility:</strong> All parties should be visible (no hidden rows)</li>";
echo "</ul>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Investigation Steps</h2>";
echo "<p>Follow these steps to investigate:</p>";
echo "<ol>";
echo "<li>Open the <strong>Standard Case Details Page</strong> link above</li>";
echo "<li>Look at the party counter badge next to 'Părți implicate'</li>";
echo "<li>Scroll through the party table to see how many parties are displayed</li>";
echo "<li>Open browser Developer Tools (F12)</li>";
echo "<li>Go to the Console tab</li>";
echo "<li>Copy and paste the JavaScript code above</li>";
echo "<li>Press Enter to run the verification script</li>";
echo "<li>Compare the results with expected values</li>";
echo "</ol>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔍 Troubleshooting</h2>";
echo "<p>If you find fewer than 340 parties:</p>";
echo "<ul>";
echo "<li><strong>Check the debug version:</strong> Open the debug link to see PHP debug output</li>";
echo "<li><strong>Check browser console:</strong> Look for JavaScript errors that might stop rendering</li>";
echo "<li><strong>Check network tab:</strong> Verify the page loaded completely</li>";
echo "<li><strong>Check source code:</strong> View page source to see if all parties are in the HTML</li>";
echo "</ul>";
echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('🔍 Actual Case Page Test - Ready');";
echo "    console.log('Click the test links above to open the actual case details page');";
echo "    console.log('Expected case: {$numarDosar} from {$institutie}');";
echo "    console.log('Expected parties: 340+');";
echo "});";
echo "</script>";

echo "</body></html>";
?>
