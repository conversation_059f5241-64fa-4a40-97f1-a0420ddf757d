<?php
// Verifică instituțiile valide din API și din baza de date
require_once 'bootstrap.php';

echo "<h1>🔍 Verificare Instituții Valide</h1>";

// Verifică din baza de date
try {
    $pdo = new PDO('mysql:host=localhost;dbname=portal_judiciar;charset=utf8mb4', 'root', '');
    
    echo "<h2>📊 Instituții din baza de date:</h2>";
    
    $sql = "SELECT DISTINCT institutie, COUNT(*) as count 
            FROM dosare 
            WHERE institutie IS NOT NULL AND institutie != '' 
            ORDER BY count DESC, institutie ASC 
            LIMIT 20";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $institutii = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    if (empty($institutii)) {
        echo "<p>❌ Nu au fost găsite instituții în baza de date</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e9ecef;'><th>Instituție</th><th>Număr dosare</th></tr>";
        
        foreach ($institutii as $inst) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($inst->institutie) . "</td>";
            echo "<td>" . $inst->count . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<h2>🔍 Căutare dosare cu numărul 14096/3/2024:</h2>";
    
    $sql = "SELECT numar, institutie, obiect, data 
            FROM dosare 
            WHERE numar LIKE '14096/3/2024%' 
            ORDER BY numar, institutie";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $dosare = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    if (empty($dosare)) {
        echo "<p>❌ Nu au fost găsite dosare cu numărul 14096/3/2024</p>";
        
        // Încearcă o căutare mai largă
        echo "<h3>Căutare mai largă (14096%):</h3>";
        $sql = "SELECT numar, institutie, obiect, data 
                FROM dosare 
                WHERE numar LIKE '14096%' 
                ORDER BY numar, institutie 
                LIMIT 10";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $dosareGeneral = $stmt->fetchAll(PDO::FETCH_OBJ);
        
        if (!empty($dosareGeneral)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #e9ecef;'><th>Număr</th><th>Instituție</th><th>Obiect</th><th>Data</th></tr>";
            
            foreach ($dosareGeneral as $dosar) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($dosar->numar) . "</td>";
                echo "<td>" . htmlspecialchars($dosar->institutie) . "</td>";
                echo "<td>" . htmlspecialchars(substr($dosar->obiect, 0, 50)) . "...</td>";
                echo "<td>" . htmlspecialchars($dosar->data) . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e9ecef;'><th>Număr</th><th>Instituție</th><th>Obiect</th><th>Data</th></tr>";
        
        foreach ($dosare as $dosar) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($dosar->numar) . "</td>";
            echo "<td>" . htmlspecialchars($dosar->institutie) . "</td>";
            echo "<td>" . htmlspecialchars(substr($dosar->obiect, 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($dosar->data) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Eroare la conectarea la baza de date:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Testează cu instituții comune
echo "<h2>🧪 Test cu instituții comune:</h2>";

$institutiiTest = [
    "Tribunalul Bucuresti",
    "Tribunalul BUCURESTI", 
    "TRIBUNALUL BUCURESTI",
    "Curtea de Apel Bucuresti",
    "CURTEA DE APEL BUCURESTI",
    "Tribunalul București",
    "Curtea de Apel București"
];

use App\Services\DosarService;

try {
    $dosarService = new DosarService();
    
    foreach ($institutiiTest as $institutie) {
        echo "<h3>Test cu: " . htmlspecialchars($institutie) . "</h3>";
        
        try {
            // Testează cu un număr de dosar generic
            $dosar = $dosarService->getDetaliiDosar("1/2024", $institutie);
            
            if ($dosar) {
                echo "<div style='background: #d4edda; padding: 8px; margin: 3px 0; border: 1px solid #c3e6cb; border-radius: 3px;'>";
                echo "✅ Instituția este validă - dosar găsit";
                echo "</div>";
            } else {
                echo "<div style='background: #fff3cd; padding: 8px; margin: 3px 0; border: 1px solid #ffeaa7; border-radius: 3px;'>";
                echo "⚠️ Instituția este validă dar dosarul nu există";
                echo "</div>";
            }
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'not a valid value for Institutie') !== false) {
                echo "<div style='background: #f8d7da; padding: 8px; margin: 3px 0; border: 1px solid #f5c6cb; border-radius: 3px;'>";
                echo "❌ Instituția NU este validă";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 8px; margin: 3px 0; border: 1px solid #f5c6cb; border-radius: 3px;'>";
                echo "❌ Eroare: " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Eroare la inițializarea serviciului:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>💡 Recomandări:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; margin: 10px 0; border: 1px solid #bee5eb; border-radius: 5px;'>";
echo "<ol>";
echo "<li><strong>Verificați baza de date</strong> pentru a găsi dosare existente cu părți multiple</li>";
echo "<li><strong>Folosiți numele exact</strong> al instituțiilor din baza de date</li>";
echo "<li><strong>Testați cu dosare reale</strong> din baza de date pentru a verifica afișarea părților</li>";
echo "<li><strong>Activați debug-ul</strong> în interfața web pentru a vedea procesarea părților</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🔗 Link-uri utile:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<p><strong>Pentru testare manuală:</strong></p>";
echo "<ul>";
echo "<li><a href='index.php' target='_blank'>Interfața de căutare</a> - căutați dosare cu multe părți</li>";
echo "<li><a href='detalii_dosar.php?debug=1' target='_blank'>Detalii dosar cu debug</a> - pentru analiza detaliată</li>";
echo "</ul>";
echo "</div>";
?>
