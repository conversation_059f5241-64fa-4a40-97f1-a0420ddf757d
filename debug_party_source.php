<?php
/**
 * Debug where SARAGEA TUDORIŢA comes from - SOAP API or decision text
 */

require_once 'bootstrap.php';

use App\Services\DosarService;

echo "=== DEBUGGING PARTY SOURCE: SOAP API vs DECISION TEXT ===" . PHP_EOL;
echo "Case: 130/98/2022 from TribunalulIALOMITA" . PHP_EOL;
echo "Target: SARAGEA TUDORIŢA" . PHP_EOL;
echo PHP_EOL;

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$targetParty = 'SARAGEA TUDORIŢA';

try {
    $dosarService = new DosarService();
    
    // Get the case details using the SOAP API
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "=== STEP 1: Getting SOAP Response ===" . PHP_EOL;
    
    // Use reflection to access the private executeSoapCallWithRetry method
    $reflection = new ReflectionClass($dosarService);
    $method = $reflection->getMethod('executeSoapCallWithRetry');
    $method->setAccessible(true);
    
    $response = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Test error");
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $dosar = $dosare[0];
        echo "✅ Retrieved SOAP response for case: " . $dosar->numar . PHP_EOL;
        
        // Check SOAP API parties (first 100)
        echo PHP_EOL;
        echo "=== STEP 2: Checking SOAP API Parties ===" . PHP_EOL;
        
        $soapPartyFound = false;
        $soapPartyCount = 0;
        $soapPartyQuality = '';
        
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $parti = $dosar->parti->DosarParte;
            if (!is_array($parti)) {
                $parti = [$parti];
            }
            
            $soapPartyCount = count($parti);
            echo "SOAP API returned {$soapPartyCount} parties" . PHP_EOL;
            
            foreach ($parti as $index => $parte) {
                if (isset($parte->nume) && stripos($parte->nume, $targetParty) !== false) {
                    $soapPartyFound = true;
                    $soapPartyQuality = $parte->calitateParte ?? '';
                    echo "✅ FOUND in SOAP API at position " . ($index + 1) . ":" . PHP_EOL;
                    echo "   Name: '{$parte->nume}'" . PHP_EOL;
                    echo "   Quality: '{$soapPartyQuality}'" . PHP_EOL;
                    break;
                }
            }
            
            if (!$soapPartyFound) {
                echo "❌ NOT FOUND in SOAP API parties (positions 1-{$soapPartyCount})" . PHP_EOL;
                echo "   This means the party is beyond the 100-party SOAP API limit" . PHP_EOL;
            }
        }
        
        // Check decision text
        echo PHP_EOL;
        echo "=== STEP 3: Checking Decision Text ===" . PHP_EOL;
        
        $decisionPartyFound = false;
        $decisionTextExcerpt = '';
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            foreach ($sedinte as $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;
                    
                    if (stripos($solutieText, $targetParty) !== false) {
                        $decisionPartyFound = true;
                        
                        // Extract context around the party name
                        $pos = stripos($solutieText, $targetParty);
                        $start = max(0, $pos - 150);
                        $length = 300;
                        $decisionTextExcerpt = substr($solutieText, $start, $length);
                        
                        echo "✅ FOUND in decision text" . PHP_EOL;
                        echo "Context excerpt:" . PHP_EOL;
                        echo "..." . $decisionTextExcerpt . "..." . PHP_EOL;
                        break;
                    }
                }
            }
            
            if (!$decisionPartyFound) {
                echo "❌ NOT FOUND in decision text" . PHP_EOL;
            }
        }
        
        echo PHP_EOL;
        echo "=== STEP 4: Analysis and Recommendation ===" . PHP_EOL;
        
        if ($soapPartyFound) {
            echo "🔍 ISSUE IDENTIFIED: Party is in SOAP API with quality '{$soapPartyQuality}'" . PHP_EOL;
            echo "   But hybrid extraction is overriding it with 'Creditor'" . PHP_EOL;
            echo "   SOLUTION: Fix deduplication logic to preserve SOAP API quality" . PHP_EOL;
        } elseif ($decisionPartyFound) {
            echo "🔍 EXPECTED BEHAVIOR: Party is only in decision text (beyond SOAP limit)" . PHP_EOL;
            echo "   But quality should be extracted from context, not defaulted to 'Creditor'" . PHP_EOL;
            echo "   SOLUTION: Enhance decision text parsing to extract party quality" . PHP_EOL;
        } else {
            echo "❌ UNEXPECTED: Party not found in either source" . PHP_EOL;
        }
        
        echo PHP_EOL;
        echo "📊 Summary:" . PHP_EOL;
        echo "  - SOAP API parties: {$soapPartyCount}" . PHP_EOL;
        echo "  - Target in SOAP API: " . ($soapPartyFound ? "YES (quality: '{$soapPartyQuality}')" : "NO") . PHP_EOL;
        echo "  - Target in decision text: " . ($decisionPartyFound ? "YES" : "NO") . PHP_EOL;
        
    } else {
        echo "❌ No SOAP response data" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace:" . PHP_EOL;
    echo $e->getTraceAsString() . PHP_EOL;
}
