<?php
/**
 * Diagnostic script to check the status of expand/collapse buttons in index.php
 */

echo "<h1>Portal Judiciar România - Button Status Check</h1>";

// Read index.php file
$indexContent = file_get_contents('index.php');

if (!$indexContent) {
    echo "<div style='color: red;'>ERROR: Could not read index.php file</div>";
    exit;
}

echo "<h2>✅ Diagnostic Results:</h2>";

// Check for button IDs
$hasExpandBtn = strpos($indexContent, 'id="expandAllBtn"') !== false;
$hasCollapseBtn = strpos($indexContent, 'id="collapseAllBtn"') !== false;

echo "<h3>1. Button HTML Elements:</h3>";
echo "<ul>";
echo "<li>" . ($hasExpandBtn ? "✅" : "❌") . " expandAllBtn ID found</li>";
echo "<li>" . ($hasCollapseBtn ? "✅" : "❌") . " collapseAllBtn ID found</li>";
echo "</ul>";

// Check for JavaScript functions
$hasInitFunction = strpos($indexContent, 'function initExpandCollapseButtons()') !== false;
$hasEventListeners = strpos($indexContent, 'addEventListener(\'click\'') !== false;
$hasInitCall = strpos($indexContent, 'initExpandCollapseButtons();') !== false;

echo "<h3>2. JavaScript Implementation:</h3>";
echo "<ul>";
echo "<li>" . ($hasInitFunction ? "✅" : "❌") . " initExpandCollapseButtons() function found</li>";
echo "<li>" . ($hasEventListeners ? "✅" : "❌") . " Event listeners found</li>";
echo "<li>" . ($hasInitCall ? "✅" : "❌") . " Function initialization call found</li>";
echo "</ul>";

// Check for DOM elements that will be targeted
$hasTermContent = strpos($indexContent, 'id="termContent<?php echo $index; ?>"') !== false;
$hasToggleIcon = strpos($indexContent, 'id="toggleIcon<?php echo $index; ?>"') !== false;

echo "<h3>3. Target DOM Elements:</h3>";
echo "<ul>";
echo "<li>" . ($hasTermContent ? "✅" : "❌") . " termContent elements structure found</li>";
echo "<li>" . ($hasToggleIcon ? "✅" : "❌") . " toggleIcon elements structure found</li>";
echo "</ul>";

// Check for notification system
$hasNotificationContainer = strpos($indexContent, 'id="notificationContainer"') !== false;
$hasNotificationDiv = strpos($indexContent, 'id="notification"') !== false;

echo "<h3>4. Notification System:</h3>";
echo "<ul>";
echo "<li>" . ($hasNotificationContainer ? "✅" : "❌") . " notificationContainer found</li>";
echo "<li>" . ($hasNotificationDiv ? "✅" : "❌") . " notification div found</li>";
echo "</ul>";

// Extract and show the current button HTML
echo "<h3>5. Current Button HTML:</h3>";
preg_match('/id="expandAllBtn"[^>]*>.*?<\/button>/s', $indexContent, $expandBtnMatch);
preg_match('/id="collapseAllBtn"[^>]*>.*?<\/button>/s', $indexContent, $collapseBtnMatch);

if ($expandBtnMatch) {
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Expand Button:</strong><br>";
    echo "<code>" . htmlspecialchars($expandBtnMatch[0]) . "</code>";
    echo "</div>";
}

if ($collapseBtnMatch) {
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Collapse Button:</strong><br>";
    echo "<code>" . htmlspecialchars($collapseBtnMatch[0]) . "</code>";
    echo "</div>";
}

// Show current JavaScript implementation
echo "<h3>6. Current JavaScript Implementation:</h3>";
$jsStart = strpos($indexContent, 'function initExpandCollapseButtons()');
if ($jsStart !== false) {
    $jsEnd = strpos($indexContent, '}', strpos($indexContent, 'showSimpleNotification', $jsStart));
    if ($jsEnd !== false) {
        $jsCode = substr($indexContent, $jsStart, $jsEnd - $jsStart + 1);
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; max-height: 300px; overflow-y: auto;'>";
        echo "<pre><code>" . htmlspecialchars($jsCode) . "</code></pre>";
        echo "</div>";
    }
}

echo "<h2>🔧 Testing Instructions:</h2>";
echo "<ol>";
echo "<li><strong>Clear browser cache:</strong> Press Ctrl+Shift+R to hard refresh</li>";
echo "<li><strong>Open Developer Tools:</strong> Press F12 and go to Console tab</li>";
echo "<li><strong>Perform a search:</strong> Search for multiple terms like 'POPESCU IONESCU BUCURESTI'</li>";
echo "<li><strong>Check console:</strong> Look for 'Initializing expand/collapse buttons...' message</li>";
echo "<li><strong>Test buttons:</strong> Click 'Expandează toate' and 'Restrânge toate'</li>";
echo "<li><strong>Check for errors:</strong> Look for any JavaScript errors in console</li>";
echo "</ol>";

echo "<h2>🚀 Next Steps if Still Not Working:</h2>";
echo "<ul>";
echo "<li>Check if jQuery is loaded properly</li>";
echo "<li>Verify that search results actually generate termContent elements</li>";
echo "<li>Test with <a href='test_final_solution.html'>test_final_solution.html</a> first</li>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "</ul>";

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>✅ Implementation Status:</h3>";
echo "<p><strong>The robust vanilla JavaScript solution has been implemented!</strong></p>";
echo "<p>This solution uses event listeners instead of onclick handlers and should work reliably.</p>";
echo "</div>";
?>
