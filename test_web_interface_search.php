<?php
// Test the actual web interface search process
require_once 'bootstrap.php';
require_once 'includes/functions.php';

use App\Services\DosarService;

echo "<h1>Web Interface Search Simulation</h1>";

// Simulate the exact process that happens when a user searches via the web interface
$searchTerm = "14096/3/2024*";

echo "<h2>Simulating search for: '$searchTerm'</h2>";

// Step 1: Parse search terms (like the web interface does)
function parseBulkSearchTerms($input) {
    $input = str_replace(',', "\n", $input);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?[\*]?$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)[\*]?$/i', $cleanTerm)) {
        return 'numarDosar';
    }
    
    return 'numeParte';
}

try {
    // Step 1: Parse the search term
    echo "<h3>Step 1: Parse Search Terms</h3>";
    $searchTermsData = parseBulkSearchTerms($searchTerm);
    echo "<pre>" . print_r($searchTermsData, true) . "</pre>";
    
    // Step 2: Simulate performBulkSearchWithFilters function
    echo "<h3>Step 2: Perform Bulk Search</h3>";
    
    $dosarService = new DosarService();
    $results = [];
    
    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];
        
        echo "<h4>Processing term: '$term' (type: $searchType)</h4>";
        
        // Build search parameters
        $searchParams = [
            'numarDosar' => ($searchType === 'numarDosar') ? $term : '',
            'institutie' => null,
            'numeParte' => ($searchType === 'numeParte') ? $term : '',
            'obiectDosar' => '',
            'dataStart' => '',
            'dataStop' => '',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => '',
            'categorieInstanta' => '',
            'categorieCaz' => ''
        ];
        
        echo "<p>Search parameters:</p>";
        echo "<pre>" . print_r($searchParams, true) . "</pre>";
        
        // Perform the search
        $termResults = $dosarService->cautareAvansata($searchParams);
        
        echo "<p><strong>Raw results count: " . count($termResults) . "</strong></p>";
        
        // Display raw results
        foreach ($termResults as $index => $dosar) {
            echo "<div style='background: #e7f3ff; padding: 5px; margin: 2px 0; border: 1px solid #007bff;'>";
            echo "Raw #" . ($index + 1) . ": " . ($dosar->numar ?? 'N/A');
            echo "</div>";
        }
        
        // Store results in the same format as the web interface
        $results[] = [
            'term' => $term,
            'type' => $searchType,
            'results' => $termResults,
            'count' => count($termResults),
            'error' => null
        ];
    }
    
    // Step 3: Check the final results structure
    echo "<h3>Step 3: Final Results Structure</h3>";
    
    $totalResults = 0;
    foreach ($results as $termResult) {
        $totalResults += count($termResult['results']);
        
        echo "<div style='background: #f0fff0; padding: 10px; margin: 5px 0; border: 1px solid #90EE90;'>";
        echo "<strong>Term:</strong> " . $termResult['term'] . "<br>";
        echo "<strong>Type:</strong> " . $termResult['type'] . "<br>";
        echo "<strong>Count:</strong> " . $termResult['count'] . "<br>";
        
        foreach ($termResult['results'] as $index => $dosar) {
            echo "<div style='margin-left: 20px; background: #f8f9fa; padding: 3px; margin: 1px 0;'>";
            echo "Final #" . ($index + 1) . ": " . ($dosar->numar ?? 'N/A');
            echo "</div>";
        }
        echo "</div>";
    }
    
    echo "<h3>Step 4: Summary</h3>";
    echo "<p><strong>Total results across all terms: $totalResults</strong></p>";
    
    // Check for the specific case with asterisk
    $foundLiteralAsterisk = false;
    foreach ($results as $termResult) {
        foreach ($termResult['results'] as $dosar) {
            if (strpos($dosar->numar, '*') !== false) {
                $foundLiteralAsterisk = true;
                echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
                echo "<strong>✓ Found case with literal asterisk:</strong><br>";
                echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
                echo "Institution: " . ($dosar->instanta ?? 'N/A') . "<br>";
                echo "Object: " . substr($dosar->obiect ?? 'N/A', 0, 100) . "...<br>";
                echo "</div>";
                break 2;
            }
        }
    }
    
    if (!$foundLiteralAsterisk) {
        echo "<p style='color: red;'><strong>✗ Case with literal asterisk NOT found in final results!</strong></p>";
    }
    
    // Step 5: Test the message generation
    echo "<h3>Step 5: Result Message Generation</h3>";
    
    foreach ($results as $termResult) {
        $count = $termResult['count'];
        $term = $termResult['term'];
        
        if ($count == 0) {
            $message = "Nu au fost găsite rezultate pentru termenul '$term'";
        } elseif ($count == 1) {
            $message = "1 rezultat găsit pentru termenul '$term'";
        } else {
            $message = "$count rezultate găsite pentru termenul '$term'";
        }
        
        echo "<div style='background: #d1ecf1; padding: 8px; margin: 3px 0; border: 1px solid #bee5eb;'>";
        echo "<strong>Message:</strong> $message";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr><h2>Conclusion</h2>";
echo "<p>If this simulation shows 3 results but the web interface shows 2, then the issue is likely in:</p>";
echo "<ul>";
echo "<li>JavaScript filtering on the frontend</li>";
echo "<li>HTML rendering/display logic</li>";
echo "<li>CSS hiding elements</li>";
echo "<li>Client-side result processing</li>";
echo "</ul>";
?>
