<?php
/**
 * Check PHP Configuration Limits
 * Verify if PHP configuration might be limiting party processing
 */

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>PHP Configuration Limits Check</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.config-table { width: 100%; border-collapse: collapse; }
.config-table th, .config-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
.config-table th { background-color: #f8f9fa; }
.config-value { font-family: monospace; font-weight: bold; }
</style></head><body>";

echo "<h1>🔧 PHP Configuration Limits Check</h1>";
echo "<p><strong>Objective:</strong> Check if PHP configuration might be limiting party processing</p>";
echo "<hr>";

// Memory and execution limits
echo "<div class='section'>";
echo "<h2>📊 Memory and Execution Limits</h2>";
echo "<table class='config-table'>";
echo "<tr><th>Setting</th><th>Current Value</th><th>Status</th></tr>";

$memoryLimit = ini_get('memory_limit');
$maxExecutionTime = ini_get('max_execution_time');
$maxInputTime = ini_get('max_input_time');
$maxInputVars = ini_get('max_input_vars');
$postMaxSize = ini_get('post_max_size');
$uploadMaxFilesize = ini_get('upload_max_filesize');

echo "<tr><td>memory_limit</td><td class='config-value'>{$memoryLimit}</td>";
echo "<td>" . (intval($memoryLimit) >= 128 ? "<span class='text-success'>✅ OK</span>" : "<span class='text-warning'>⚠️ Low</span>") . "</td></tr>";

echo "<tr><td>max_execution_time</td><td class='config-value'>{$maxExecutionTime}</td>";
echo "<td>" . ($maxExecutionTime == 0 || $maxExecutionTime >= 30 ? "<span class='text-success'>✅ OK</span>" : "<span class='text-warning'>⚠️ Low</span>") . "</td></tr>";

echo "<tr><td>max_input_time</td><td class='config-value'>{$maxInputTime}</td>";
echo "<td>" . ($maxInputTime == -1 || $maxInputTime >= 60 ? "<span class='text-success'>✅ OK</span>" : "<span class='text-warning'>⚠️ Low</span>") . "</td></tr>";

echo "<tr><td>max_input_vars</td><td class='config-value'>{$maxInputVars}</td>";
echo "<td>" . ($maxInputVars >= 1000 ? "<span class='text-success'>✅ OK</span>" : "<span class='text-warning'>⚠️ Low</span>") . "</td></tr>";

echo "<tr><td>post_max_size</td><td class='config-value'>{$postMaxSize}</td>";
echo "<td><span class='text-info'>ℹ️ Info</span></td></tr>";

echo "<tr><td>upload_max_filesize</td><td class='config-value'>{$uploadMaxFilesize}</td>";
echo "<td><span class='text-info'>ℹ️ Info</span></td></tr>";

echo "</table>";
echo "</div>";

// Error reporting and logging
echo "<div class='section'>";
echo "<h2>🐛 Error Reporting and Logging</h2>";
echo "<table class='config-table'>";
echo "<tr><th>Setting</th><th>Current Value</th><th>Status</th></tr>";

$errorReporting = error_reporting();
$displayErrors = ini_get('display_errors');
$logErrors = ini_get('log_errors');
$errorLog = ini_get('error_log');

echo "<tr><td>error_reporting</td><td class='config-value'>{$errorReporting}</td>";
echo "<td>" . ($errorReporting > 0 ? "<span class='text-success'>✅ Enabled</span>" : "<span class='text-warning'>⚠️ Disabled</span>") . "</td></tr>";

echo "<tr><td>display_errors</td><td class='config-value'>{$displayErrors}</td>";
echo "<td>" . ($displayErrors ? "<span class='text-info'>ℹ️ On</span>" : "<span class='text-info'>ℹ️ Off</span>") . "</td></tr>";

echo "<tr><td>log_errors</td><td class='config-value'>{$logErrors}</td>";
echo "<td>" . ($logErrors ? "<span class='text-success'>✅ On</span>" : "<span class='text-warning'>⚠️ Off</span>") . "</td></tr>";

echo "<tr><td>error_log</td><td class='config-value'>" . ($errorLog ?: 'system default') . "</td>";
echo "<td><span class='text-info'>ℹ️ Info</span></td></tr>";

echo "</table>";
echo "</div>";

// SOAP and cURL extensions
echo "<div class='section'>";
echo "<h2>🔌 Required Extensions</h2>";
echo "<table class='config-table'>";
echo "<tr><th>Extension</th><th>Status</th><th>Notes</th></tr>";

$soapEnabled = extension_loaded('soap');
$curlEnabled = extension_loaded('curl');
$mbstringEnabled = extension_loaded('mbstring');
$xmlEnabled = extension_loaded('xml');

echo "<tr><td>SOAP</td>";
echo "<td>" . ($soapEnabled ? "<span class='text-success'>✅ Loaded</span>" : "<span class='text-danger'>❌ Missing</span>") . "</td>";
echo "<td>" . ($soapEnabled ? "Required for SOAP API calls" : "CRITICAL: Required for SOAP API") . "</td></tr>";

echo "<tr><td>cURL</td>";
echo "<td>" . ($curlEnabled ? "<span class='text-success'>✅ Loaded</span>" : "<span class='text-danger'>❌ Missing</span>") . "</td>";
echo "<td>" . ($curlEnabled ? "Required for HTTP requests" : "May affect API calls") . "</td></tr>";

echo "<tr><td>mbstring</td>";
echo "<td>" . ($mbstringEnabled ? "<span class='text-success'>✅ Loaded</span>" : "<span class='text-warning'>⚠️ Missing</span>") . "</td>";
echo "<td>" . ($mbstringEnabled ? "Required for Romanian diacritics" : "May affect text processing") . "</td></tr>";

echo "<tr><td>XML</td>";
echo "<td>" . ($xmlEnabled ? "<span class='text-success'>✅ Loaded</span>" : "<span class='text-danger'>❌ Missing</span>") . "</td>";
echo "<td>" . ($xmlEnabled ? "Required for SOAP responses" : "CRITICAL: Required for XML parsing") . "</td></tr>";

echo "</table>";
echo "</div>";

// Current memory usage
echo "<div class='section'>";
echo "<h2>💾 Current Memory Usage</h2>";
$currentMemory = memory_get_usage(true);
$peakMemory = memory_get_peak_usage(true);
$memoryLimitBytes = ini_get('memory_limit');

// Convert memory limit to bytes
if (preg_match('/^(\d+)(.)$/', $memoryLimitBytes, $matches)) {
    $value = $matches[1];
    $unit = strtoupper($matches[2]);
    switch ($unit) {
        case 'G': $memoryLimitBytes = $value * 1024 * 1024 * 1024; break;
        case 'M': $memoryLimitBytes = $value * 1024 * 1024; break;
        case 'K': $memoryLimitBytes = $value * 1024; break;
        default: $memoryLimitBytes = $value; break;
    }
} else {
    $memoryLimitBytes = intval($memoryLimitBytes);
}

function formatBytes($bytes) {
    if ($bytes >= 1024 * 1024 * 1024) {
        return round($bytes / (1024 * 1024 * 1024), 2) . ' GB';
    } elseif ($bytes >= 1024 * 1024) {
        return round($bytes / (1024 * 1024), 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return round($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

echo "<table class='config-table'>";
echo "<tr><th>Memory Metric</th><th>Value</th><th>Status</th></tr>";

echo "<tr><td>Current Usage</td><td class='config-value'>" . formatBytes($currentMemory) . "</td>";
$currentPercent = ($currentMemory / $memoryLimitBytes) * 100;
echo "<td>" . ($currentPercent < 50 ? "<span class='text-success'>✅ OK ({$currentPercent}%)</span>" : 
    ($currentPercent < 80 ? "<span class='text-warning'>⚠️ Moderate ({$currentPercent}%)</span>" : 
    "<span class='text-danger'>❌ High ({$currentPercent}%)</span>")) . "</td></tr>";

echo "<tr><td>Peak Usage</td><td class='config-value'>" . formatBytes($peakMemory) . "</td>";
$peakPercent = ($peakMemory / $memoryLimitBytes) * 100;
echo "<td>" . ($peakPercent < 50 ? "<span class='text-success'>✅ OK ({$peakPercent}%)</span>" : 
    ($peakPercent < 80 ? "<span class='text-warning'>⚠️ Moderate ({$peakPercent}%)</span>" : 
    "<span class='text-danger'>❌ High ({$peakPercent}%)</span>")) . "</td></tr>";

echo "<tr><td>Memory Limit</td><td class='config-value'>" . formatBytes($memoryLimitBytes) . "</td>";
echo "<td><span class='text-info'>ℹ️ Limit</span></td></tr>";

echo "</table>";
echo "</div>";

// Test array processing capability
echo "<div class='section'>";
echo "<h2>🧪 Array Processing Test</h2>";
echo "<p>Testing PHP's ability to handle large arrays (simulating 340+ parties):</p>";

$startTime = microtime(true);
$startMemory = memory_get_usage();

// Create a test array with 500 parties to simulate processing
$testParties = [];
for ($i = 1; $i <= 500; $i++) {
    $testParties[] = [
        'nume' => "Test Party {$i}",
        'calitate' => "Test Quality {$i}",
        'source' => ($i <= 100) ? 'soap_api' : 'decision_text'
    ];
}

$endTime = microtime(true);
$endMemory = memory_get_usage();

$processingTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
$memoryUsed = $endMemory - $startMemory;

echo "<table class='config-table'>";
echo "<tr><th>Test Metric</th><th>Value</th><th>Status</th></tr>";

echo "<tr><td>Array Size</td><td class='config-value'>" . count($testParties) . " elements</td>";
echo "<td><span class='text-success'>✅ Created</span></td></tr>";

echo "<tr><td>Processing Time</td><td class='config-value'>" . round($processingTime, 2) . " ms</td>";
echo "<td>" . ($processingTime < 100 ? "<span class='text-success'>✅ Fast</span>" : 
    ($processingTime < 500 ? "<span class='text-warning'>⚠️ Moderate</span>" : 
    "<span class='text-danger'>❌ Slow</span>")) . "</td></tr>";

echo "<tr><td>Memory Used</td><td class='config-value'>" . formatBytes($memoryUsed) . "</td>";
echo "<td>" . ($memoryUsed < 1024 * 1024 ? "<span class='text-success'>✅ Low</span>" : 
    ($memoryUsed < 5 * 1024 * 1024 ? "<span class='text-warning'>⚠️ Moderate</span>" : 
    "<span class='text-danger'>❌ High</span>")) . "</td></tr>";

echo "</table>";

// Test foreach loop performance
$loopStartTime = microtime(true);
$loopCount = 0;
foreach ($testParties as $index => $party) {
    $loopCount++;
    // Simulate the same processing as in detalii_dosar.php
    $name = htmlspecialchars($party['nume']);
    $quality = htmlspecialchars($party['calitate']);
    $source = htmlspecialchars($party['source']);
}
$loopEndTime = microtime(true);
$loopTime = ($loopEndTime - $loopStartTime) * 1000;

echo "<p><strong>Foreach Loop Test:</strong></p>";
echo "<ul>";
echo "<li>Processed {$loopCount} parties</li>";
echo "<li>Loop time: " . round($loopTime, 2) . " ms</li>";
echo "<li>Performance: " . ($loopTime < 50 ? "✅ Excellent" : ($loopTime < 200 ? "⚠️ Good" : "❌ Poor")) . "</li>";
echo "</ul>";

echo "</div>";

// Recommendations
echo "<div class='section'>";
echo "<h2>💡 Recommendations</h2>";

$issues = [];
$warnings = [];

if (intval($memoryLimit) < 128) {
    $issues[] = "Memory limit is too low ({$memoryLimit}). Recommend at least 128M.";
}

if ($maxExecutionTime > 0 && $maxExecutionTime < 30) {
    $issues[] = "Execution time limit is too low ({$maxExecutionTime}s). Recommend at least 30s.";
}

if (!$soapEnabled) {
    $issues[] = "SOAP extension is missing. This is critical for API functionality.";
}

if (!$xmlEnabled) {
    $issues[] = "XML extension is missing. This is critical for SOAP response parsing.";
}

if ($currentPercent > 80) {
    $warnings[] = "Current memory usage is high ({$currentPercent}%). Monitor for memory issues.";
}

if ($processingTime > 500) {
    $warnings[] = "Array processing is slow ({$processingTime}ms). May affect large party lists.";
}

if (!empty($issues)) {
    echo "<div class='error'>";
    echo "<h4>❌ Critical Issues:</h4>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>{$issue}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($warnings)) {
    echo "<div class='warning'>";
    echo "<h4>⚠️ Warnings:</h4>";
    echo "<ul>";
    foreach ($warnings as $warning) {
        echo "<li>{$warning}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (empty($issues) && empty($warnings)) {
    echo "<div class='success'>";
    echo "<h4>✅ All Good!</h4>";
    echo "<p>PHP configuration appears to be suitable for processing large party lists.</p>";
    echo "</div>";
}

echo "</div>";

echo "</body></html>";
?>
