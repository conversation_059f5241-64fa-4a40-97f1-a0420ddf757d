<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

// Define search functions locally for testing
function normalizeForSearch($text) {
    if (empty($text)) return '';

    $text = trim((string) $text);

    // Enhanced encoding detection for Romanian characters
    if (!mb_check_encoding($text, 'UTF-8')) {
        $encodingList = ['UTF-8', 'ISO-8859-2', 'Windows-1250', 'ISO-8859-1', 'ASCII'];
        $detectedEncoding = mb_detect_encoding($text, $encodingList, true);

        if ($detectedEncoding !== false) {
            $text = mb_convert_encoding($text, 'UTF-8', $detectedEncoding);
        } else {
            // Fallback to Windows-1250 with error suppression
            $originalErrorReporting = error_reporting();
            error_reporting($originalErrorReporting & ~E_WARNING);
            try {
                $text = mb_convert_encoding($text, 'UTF-8', 'Windows-1250');
            } catch (Exception $e) {
                error_log("Test Search: Encoding conversion failed for: " . substr($text, 0, 50));
            }
            error_reporting($originalErrorReporting);
        }
    }

    // Romanian diacritics mapping
    $diacritics = [
        'ă' => 'a', 'Ă' => 'A',
        'â' => 'a', 'Â' => 'A',
        'î' => 'i', 'Î' => 'I',
        'ș' => 's', 'Ș' => 'S',
        'ț' => 't', 'Ț' => 'T',
        'ş' => 's', 'Ş' => 'S',
        'ţ' => 't', 'Ţ' => 'T'
    ];

    return mb_strtolower(strtr($text, $diacritics), 'UTF-8');
}

function extractPartyName($party) {
    if (is_array($party)) {
        return $party['nume'] ?? '';
    } elseif (is_object($party)) {
        return $party->nume ?? '';
    }
    return '';
}

function findMatchingParty($parti, $searchTerm) {
    if (empty($parti) || empty($searchTerm)) {
        return null;
    }

    $normalizedSearchTerm = normalizeForSearch($searchTerm);
    $lowerSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');

    // Phase 1: Exact match
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) continue;

        $normalizedPartyName = normalizeForSearch($partyName);
        if (strcasecmp($normalizedPartyName, $normalizedSearchTerm) === 0) {
            return $party;
        }
    }

    // Phase 2: Partial match
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) continue;

        $normalizedPartyName = normalizeForSearch($partyName);
        if (mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false) {
            return $party;
        }
    }

    return null;
}

function getRelevantParty($parti, $searchTerm = '') {
    if (empty($parti)) {
        return null;
    }

    if (!empty($searchTerm)) {
        $matchingParty = findMatchingParty($parti, $searchTerm);
        if ($matchingParty) {
            return $matchingParty;
        }
    }

    return $parti[0];
}

echo "🔍 TESTING SEARCH FUNCTIONALITY\n";
echo "================================\n\n";

$dosarService = new DosarService();

// Test search scenarios
$searchTests = [
    [
        'searchType' => 'numeParte',
        'searchTerm' => 'BURDUŞELU',
        'description' => 'Party name search for BURDUŞELU'
    ],
    [
        'searchType' => 'numeParte', 
        'searchTerm' => 'NORDIS',
        'description' => 'Party name search for NORDIS'
    ],
    [
        'searchType' => 'numeParte',
        'searchTerm' => 'TUDORIŢA',
        'description' => 'Party name search for TUDORIŢA'
    ]
];

foreach ($searchTests as $i => $test) {
    echo "🔍 SEARCH TEST " . ($i + 1) . ": {$test['description']}\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    try {
        // Simulate search request
        $searchParams = [
            'searchType' => $test['searchType'],
            'numeParte' => $test['searchTerm']
        ];
        
        echo "Search parameters:\n";
        echo "- Type: {$test['searchType']}\n";
        echo "- Term: '{$test['searchTerm']}'\n\n";
        
        // Get search results using the service
        // Note: We'll simulate search results since cautaDosare method may not exist
        $results = [];

        // Test with known cases that contain the search term
        $testCases = [
            ['130/98/2022', 'CurteadeApelBUCURESTI'],
            ['130/98/2022', 'TribunalulIALOMITA']
        ];

        foreach ($testCases as $testCase) {
            try {
                $dosar = $dosarService->getDetaliiDosar($testCase[0], $testCase[1]);
                if ($dosar) {
                    // Check if this case contains the search term
                    $hasMatch = false;
                    foreach ($dosar->parti as $party) {
                        $partyName = extractPartyName($party);
                        if (stripos($partyName, $test['searchTerm']) !== false) {
                            $hasMatch = true;
                            break;
                        }
                    }

                    if ($hasMatch) {
                        $results[] = $dosar;
                    }
                }
            } catch (Exception $e) {
                // Skip this case if there's an error
                continue;
            }
        }
        
        echo "✅ Search executed\n";
        echo "Results found: " . count($results) . "\n\n";
        
        if (count($results) > 0) {
            echo "🔍 ANALYZING FIRST 3 RESULTS:\n";
            echo "------------------------------\n";
            
            $displayCount = min(3, count($results));
            for ($j = 0; $j < $displayCount; $j++) {
                $dosar = $results[$j];
                echo "Result " . ($j + 1) . ":\n";
                echo "- Case: {$dosar->numar} ({$dosar->institutie})\n";
                echo "- Total parties: " . count($dosar->parti ?? []) . "\n";
                
                // Test party matching
                if (function_exists('findMatchingParty')) {
                    $matchingParty = findMatchingParty($dosar->parti ?? [], $test['searchTerm']);
                    if ($matchingParty) {
                        $matchedName = is_array($matchingParty) ? $matchingParty['nume'] : $matchingParty->nume;
                        $matchedQuality = is_array($matchingParty) ? ($matchingParty['calitate'] ?? '') : ($matchingParty->calitate ?? '');
                        echo "- Matching party: '{$matchedName}' ({$matchedQuality})\n";
                    } else {
                        echo "- No matching party found by algorithm\n";
                    }
                } else {
                    echo "- findMatchingParty function not available\n";
                }
                
                // Manual search in parties
                $manualMatches = [];
                foreach ($dosar->parti ?? [] as $party) {
                    $partyName = is_array($party) ? $party['nume'] : $party->nume;
                    if (stripos($partyName, $test['searchTerm']) !== false) {
                        $manualMatches[] = $partyName;
                    }
                }
                
                echo "- Manual matches found: " . count($manualMatches) . "\n";
                if (count($manualMatches) > 0) {
                    echo "  First match: '{$manualMatches[0]}'\n";
                }
                
                echo "\n";
            }
        } else {
            echo "❌ No results found\n\n";
            
            // Test if the search term exists in our test cases
            echo "🔍 CHECKING IF TERM EXISTS IN TEST CASES:\n";
            echo "-----------------------------------------\n";
            
            $testCases = [
                ['130/98/2022', 'CurteadeApelBUCURESTI'],
                ['130/98/2022', 'TribunalulIALOMITA']
            ];
            
            foreach ($testCases as $testCase) {
                $dosar = $dosarService->getDetaliiDosar($testCase[0], $testCase[1]);
                if ($dosar) {
                    $matches = 0;
                    foreach ($dosar->parti as $party) {
                        $partyName = is_array($party) ? $party['nume'] : $party->nume;
                        if (stripos($partyName, $test['searchTerm']) !== false) {
                            $matches++;
                        }
                    }
                    echo "- {$testCase[0]} ({$testCase[1]}): {$matches} matches\n";
                }
            }
        }
        
        // Test search result display logic
        echo "🔍 TESTING SEARCH RESULT DISPLAY:\n";
        echo "----------------------------------\n";
        
        if (count($results) > 0 && function_exists('getRelevantParty')) {
            $firstResult = $results[0];
            $relevantParty = getRelevantParty($firstResult->parti ?? [], $test['searchTerm']);
            
            if ($relevantParty) {
                $relevantName = is_array($relevantParty) ? $relevantParty['nume'] : $relevantParty->nume;
                $relevantQuality = is_array($relevantParty) ? ($relevantParty['calitate'] ?? '') : ($relevantParty->calitate ?? '');
                echo "✅ Relevant party for display: '{$relevantName}' ({$relevantQuality})\n";
                
                // Check if this is the best match
                $totalParties = count($firstResult->parti ?? []);
                echo "- Total parties in case: {$totalParties}\n";
                echo "- Display shows: 1 party + ({$totalParties} - 1) others indicator\n";
            } else {
                echo "❌ No relevant party found for display\n";
            }
        } else {
            echo "⚠️ Cannot test display logic (no results or missing functions)\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("=", 70) . "\n\n";
}

echo "✅ Search functionality testing complete\n";
