{% extends "base.twig" %}

{% block title %}{{ page_title }}{% endblock %}

{% block meta_description %}Administrare securitate Portal Judiciar România - Configurare politici parole, monitorizare securitate, control acces și gestionare incidente.{% endblock %}

{% block additional_css %}
<style>
:root {
    --primary-color: #007bff;
    --secondary-color: #2c3e50;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.security-header {
    background: linear-gradient(135deg, var(--danger-color), var(--secondary-color));
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 8px;
}

.security-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
}

.security-card.danger {
    border-left-color: var(--danger-color);
}

.security-card.warning {
    border-left-color: var(--warning-color);
}

.security-card.success {
    border-left-color: var(--success-color);
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.form-section {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.incident-item {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: white;
}

.incident-item.high-severity {
    border-left: 4px solid var(--danger-color);
}

.incident-item.medium-severity {
    border-left: 4px solid var(--warning-color);
}

.incident-item.low-severity {
    border-left: 4px solid var(--info-color);
}

.incident-resolved {
    opacity: 0.7;
    background-color: #f8f9fa;
}

.ip-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    background: white;
}

.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
}

.loading-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.spinner {
    border: 4px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top: 4px solid white;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .security-header {
        padding: 1rem 0;
        margin-bottom: 1rem;
    }
    
    .security-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .stat-item {
        margin-bottom: 0.5rem;
    }
    
    .ip-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Security Header -->
    <div class="security-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-shield-alt"></i> Administrare Securitate</h1>
                    <p class="mb-0">Configurare politici securitate, monitorizare și control acces</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="user-info">
                        <i class="fas fa-user-shield"></i>
                        <span>{{ user_name }}</span>
                        <div class="mt-1">
                            <small class="text-light">Rol: {{ user_role|upper }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Security Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="security-card danger">
                    <div class="stat-item">
                        <span class="stat-number">{{ security_stats.incidents.total_incidents|default(0) }}</span>
                        <span class="stat-label">Incidente Totale</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="security-card warning">
                    <div class="stat-item">
                        <span class="stat-number">{{ security_stats.incidents.high_severity|default(0) }}</span>
                        <span class="stat-label">Severitate Înaltă</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="security-card">
                    <div class="stat-item">
                        <span class="stat-number">{{ security_stats.incidents.unresolved|default(0) }}</span>
                        <span class="stat-label">Nerezolvate</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="security-card success">
                    <div class="stat-item">
                        <span class="stat-number">{{ security_stats.login_attempts.failed_attempts|default(0) }}</span>
                        <span class="stat-label">Login-uri Eșuate</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Security Settings -->
            <div class="col-lg-8">
                <!-- Password Policy -->
                <div class="security-card">
                    <h3><i class="fas fa-key"></i> Politica de Parole</h3>
                    <form id="passwordPolicyForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_tokens.update_password_policy }}">
                        
                        <div class="form-section">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="minLength" class="form-label">Lungimea minimă:</label>
                                    <input type="number" class="form-control" id="minLength" name="min_length" 
                                           value="{{ security_settings.password_min_length.value|default(8) }}" min="6" max="20">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Cerințe caracter:</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireUppercase" name="require_uppercase"
                                               {{ security_settings.password_require_uppercase.value == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="requireUppercase">Litere mari</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireLowercase" name="require_lowercase"
                                               {{ security_settings.password_require_lowercase.value == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="requireLowercase">Litere mici</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireNumbers" name="require_numbers"
                                               {{ security_settings.password_require_numbers.value == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="requireNumbers">Cifre</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireSymbols" name="require_symbols"
                                               {{ security_settings.password_require_symbols.value == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="requireSymbols">Simboluri</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Salvează Politica de Parole
                        </button>
                    </form>
                </div>

                <!-- Session Settings -->
                <div class="security-card">
                    <h3><i class="fas fa-clock"></i> Setări Sesiune</h3>
                    <form id="sessionSettingsForm">
                        <input type="hidden" name="csrf_token" value="{{ csrf_tokens.update_session_settings }}">
                        
                        <div class="form-section">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="sessionTimeout" class="form-label">Timeout sesiune (secunde):</label>
                                    <input type="number" class="form-control" id="sessionTimeout" name="timeout" 
                                           value="{{ security_settings.session_timeout.value|default(3600) }}" min="300" max="86400">
                                </div>
                                <div class="col-md-4">
                                    <label for="maxAttempts" class="form-label">Încercări maxime login:</label>
                                    <input type="number" class="form-control" id="maxAttempts" name="max_attempts" 
                                           value="{{ security_settings.max_login_attempts.value|default(5) }}" min="3" max="10">
                                </div>
                                <div class="col-md-4">
                                    <label for="lockoutDuration" class="form-label">Durata blocare (secunde):</label>
                                    <input type="number" class="form-control" id="lockoutDuration" name="lockout_duration" 
                                           value="{{ security_settings.lockout_duration.value|default(900) }}" min="60" max="3600">
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Salvează Setări Sesiune
                        </button>
                    </form>
                </div>
            </div>

            <!-- Security Monitoring -->
            <div class="col-lg-4">
                <!-- Recent Incidents -->
                <div class="security-card">
                    <h4><i class="fas fa-exclamation-triangle"></i> Incidente Recente</h4>
                    <div id="recentIncidents">
                        {% if recent_incidents %}
                            {% for incident in recent_incidents %}
                                <div class="incident-item {{ incident.severity }}-severity {{ incident.resolved ? 'incident-resolved' : '' }}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong>{{ incident.incident_type }}</strong>
                                            <p class="mb-1 small">{{ incident.description }}</p>
                                            <small class="text-muted">{{ incident.created_at }}</small>
                                        </div>
                                        {% if not incident.resolved %}
                                            <button class="btn btn-sm btn-outline-success" 
                                                    onclick="resolveIncident({{ incident.id }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">Nu există incidente recente.</p>
                        {% endif %}
                    </div>
                </div>

                <!-- IP Whitelist -->
                <div class="security-card">
                    <h4><i class="fas fa-list"></i> IP Whitelist</h4>
                    <form id="ipWhitelistForm" class="mb-3">
                        <input type="hidden" name="csrf_token" value="{{ csrf_tokens.update_ip_whitelist }}">
                        <input type="hidden" name="whitelist_action" value="add">
                        
                        <div class="input-group mb-2">
                            <input type="text" class="form-control" name="ip_address" placeholder="***********" required>
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <input type="text" class="form-control" name="description" placeholder="Descriere (opțional)">
                    </form>
                    
                    <div id="ipWhitelistItems">
                        {% if ip_whitelist %}
                            {% for ip in ip_whitelist %}
                                <div class="ip-item">
                                    <div>
                                        <strong>{{ ip.ip_address }}</strong>
                                        {% if ip.description %}
                                            <br><small class="text-muted">{{ ip.description }}</small>
                                        {% endif %}
                                    </div>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="removeFromWhitelist('{{ ip.ip_address }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">Nu există adrese IP în whitelist.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Se procesează cererea...</p>
    </div>
</div>
{% endblock %}

{% block additional_js %}
<script>
// Security Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize forms
    initializeSecurityForms();

    // Auto-refresh security stats every 30 seconds
    setInterval(refreshSecurityStats, 30000);
});

function initializeSecurityForms() {
    // Password Policy Form
    const passwordForm = document.getElementById('passwordPolicyForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitSecurityForm(this, 'update_password_policy');
        });
    }

    // Session Settings Form
    const sessionForm = document.getElementById('sessionSettingsForm');
    if (sessionForm) {
        sessionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitSecurityForm(this, 'update_session_settings');
        });
    }

    // IP Whitelist Form
    const ipForm = document.getElementById('ipWhitelistForm');
    if (ipForm) {
        ipForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitSecurityForm(this, 'update_ip_whitelist');
        });
    }
}

function submitSecurityForm(form, action) {
    showLoading();

    const formData = new FormData(form);
    formData.append('action', action);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showNotification(data.message, 'success');

            // Refresh specific sections based on action
            if (action === 'update_ip_whitelist') {
                refreshIPWhitelist();
            }

            // Clear form if it's the IP whitelist form
            if (action === 'update_ip_whitelist') {
                form.reset();
            }
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showNotification('Eroare de comunicare cu serverul', 'danger');
    });
}

function resolveIncident(incidentId) {
    if (!confirm('Sunteți sigur că doriți să marcați acest incident ca rezolvat?')) {
        return;
    }

    showLoading();

    const formData = new FormData();
    formData.append('action', 'resolve_security_incident');
    formData.append('incident_id', incidentId);
    formData.append('csrf_token', '{{ csrf_tokens.resolve_security_incident }}');

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showNotification(data.message, 'success');
            refreshRecentIncidents();
            refreshSecurityStats();
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showNotification('Eroare de comunicare cu serverul', 'danger');
    });
}

function removeFromWhitelist(ipAddress) {
    if (!confirm(`Sunteți sigur că doriți să eliminați ${ipAddress} din whitelist?`)) {
        return;
    }

    showLoading();

    const formData = new FormData();
    formData.append('action', 'update_ip_whitelist');
    formData.append('whitelist_action', 'remove');
    formData.append('ip_address', ipAddress);
    formData.append('csrf_token', '{{ csrf_tokens.update_ip_whitelist }}');

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            showNotification(data.message, 'success');
            refreshIPWhitelist();
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showNotification('Eroare de comunicare cu serverul', 'danger');
    });
}

function refreshSecurityStats() {
    // This would typically reload the page or make an AJAX call to get updated stats
    // For now, we'll just update the timestamp
    console.log('Refreshing security stats...');
}

function refreshRecentIncidents() {
    // Reload the recent incidents section
    location.reload();
}

function refreshIPWhitelist() {
    // Reload the IP whitelist section
    location.reload();
}

function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'block';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// Notification function (assuming it exists globally)
function showNotification(message, type) {
    // Check if global notification function exists
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
    } else {
        // Fallback to alert
        alert(message);
    }
}

// Form validation
function validateIPAddress(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
}

// Add real-time validation to IP input
document.addEventListener('DOMContentLoaded', function() {
    const ipInput = document.querySelector('input[name="ip_address"]');
    if (ipInput) {
        ipInput.addEventListener('input', function() {
            const isValid = validateIPAddress(this.value);
            this.classList.toggle('is-invalid', this.value && !isValid);
            this.classList.toggle('is-valid', this.value && isValid);
        });
    }
});
</script>
{% endblock %}
