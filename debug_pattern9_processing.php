<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 DEBUGGING PATTERN 9 PROCESSING\n";
echo "==================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n\n";
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    // Test specific missing names with Pattern 9 logic
    $missingNames = ['Badic Angela', 'Câlţea Lică', 'Chiţu Gheorghe'];
    
    echo "🔍 TESTING PATTERN 9 LOGIC ON MISSING NAMES:\n";
    echo "============================================\n\n";
    
    $allCommaNames = explode(',', $solutieSumarText);
    
    foreach ($missingNames as $targetName) {
        echo "Testing: \"{$targetName}\"\n";
        
        $found = false;
        foreach ($allCommaNames as $index => $potentialName) {
            $originalName = $potentialName;
            $potentialName = trim($potentialName);
            
            // Check if this comma segment contains our target name
            if (stripos($potentialName, $targetName) !== false) {
                echo "  Found in segment {$index}: \"{$originalName}\"\n";
                
                // Apply Pattern 9 processing
                $processedName = $potentialName;
                
                // Remove common prefixes and suffixes that might interfere
                $processedName = preg_replace('/.*(?:apelanţii|apelan\?ii|creditorii|intervenienţii)\s+/', '', $processedName);
                echo "    After prefix removal: \"{$processedName}\"\n";
                
                $processedName = preg_replace('/.*(?:Anulează|Respinge|Admite)\s+apelurile\s+formulate\s+de\s+/', '', $processedName);
                echo "    After appeal removal: \"{$processedName}\"\n";
                
                $processedName = preg_replace('/\s*ca\s+(?:netimbrate|nefondate).*$/', '', $processedName);
                echo "    After ca removal: \"{$processedName}\"\n";
                
                $processedName = preg_replace('/\s*\(.*?\)/', '', $processedName);
                echo "    After parentheses removal: \"{$processedName}\"\n";
                
                $processedName = preg_replace('/\s*şi\s*$/', '', $processedName);
                echo "    After şi removal: \"{$processedName}\"\n";
                
                $processedName = preg_replace('/\..*$/', '', $processedName);
                echo "    After dot removal: \"{$processedName}\"\n";
                
                $processedName = preg_replace('/\s*în sumă.*$/', '', $processedName);
                $processedName = preg_replace('/\s*pentru.*$/', '', $processedName);
                $processedName = preg_replace('/\s*privind.*$/', '', $processedName);
                $processedName = trim($processedName);
                echo "    Final processed: \"{$processedName}\"\n";
                
                // Test validation
                $isValid = false;
                if (strlen($processedName) >= 3) {
                    if (preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $processedName)) {
                        $isValid = true;
                        echo "    Validation: PASS\n";
                    } else {
                        echo "    Validation: FAIL (regex)\n";
                    }
                } else {
                    echo "    Validation: FAIL (too short)\n";
                }
                
                // Test exclusion terms
                if ($isValid) {
                    $lowerName = strtolower($processedName);
                    $excludeTerms = [
                        'anulează', 'respinge', 'admite', 'apelurile', 'formulate', 'apelanţii', 'apelan?ii',
                        'creditorii', 'intervenienţii', 'netimbrate', 'nefondate', 'împotriva', 'pentru',
                        'privind', 'suma', 'reprezentând', 'prin', 'avocat', 'mandatar', 'domiciliul',
                        'calitate', 'fiind', 'având', 'conform', 'potrivit', 'astfel', 'încât'
                    ];
                    
                    $excluded = false;
                    foreach ($excludeTerms as $term) {
                        if (stripos($lowerName, $term) !== false) {
                            echo "    Excluded by term: \"{$term}\"\n";
                            $excluded = true;
                            break;
                        }
                    }
                    
                    if (!$excluded) {
                        echo "    Final result: SHOULD BE EXTRACTED\n";
                    }
                } else {
                    echo "    Final result: REJECTED\n";
                }
                
                $found = true;
                break;
            }
        }
        
        if (!$found) {
            echo "  NOT FOUND in any comma segment\n";
        }
        
        echo "\n";
    }
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
