<?php
// Debug the real issue - simulate exact web interface behavior
require_once 'bootstrap.php';

use App\Services\DosarService;

// Simulate the exact POST request
$_POST['bulkSearchTerms'] = "14096/3/2024*";

echo "<h1>🔍 Debug Real Issue - Exact Web Interface Simulation</h1>";

// Include the exact functions from index.php
function parseBulkSearchTerms($input) {
    $input = str_replace(',', "\n", $input);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        return 'numarDosar';
    }
    
    return 'numeParte';
}

function performBulkSearchWithFilters($searchTermsData, $filters) {
    $dosarService = new DosarService();
    $results = [];

    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];

        try {
            $searchParams = [
                'numarDosar' => ($searchType === 'numarDosar') ? $term : '',
                'institutie' => $filters['institutie'] ?? null,
                'numeParte' => ($searchType === 'numeParte') ? $term : '',
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => '',
                'categorieCaz' => ''
            ];

            $termResults = $dosarService->cautareAvansata($searchParams);

            // Add search metadata to each result
            foreach ($termResults as $dosar) {
                $dosar->searchTerm = $term;
                $dosar->searchType = $searchType;
            }

            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => $termResults,
                'count' => count($termResults),
                'error' => null
            ];

        } catch (Exception $e) {
            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => [],
                'count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    return $results;
}

try {
    $bulkSearchTerms = $_POST['bulkSearchTerms'] ?? '';
    
    if (!empty($bulkSearchTerms)) {
        echo "<h2>Step 1: Parse Search Terms</h2>";
        $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
        
        echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
        echo "<strong>Input:</strong> '$bulkSearchTerms'<br>";
        echo "<strong>Parsed terms:</strong><br>";
        foreach ($searchTermsData as $termData) {
            echo "- Term: '{$termData['term']}', Type: '{$termData['type']}'<br>";
        }
        echo "</div>";
        
        echo "<h2>Step 2: Perform Bulk Search</h2>";
        $searchResults = performBulkSearchWithFilters($searchTermsData, []);
        
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
        echo "<strong>Search Results Summary:</strong><br>";
        
        $totalResults = 0;
        $literalAsteriskFound = false;
        
        foreach ($searchResults as $termResult) {
            $count = count($termResult['results']);
            $totalResults += $count;
            
            echo "- Term: '{$termResult['term']}' → {$count} results<br>";
            
            // Check each result
            foreach ($termResult['results'] as $dosar) {
                if (strpos($dosar->numar, '*') !== false) {
                    $literalAsteriskFound = true;
                    echo "  → Found literal asterisk case: {$dosar->numar}<br>";
                }
            }
        }
        
        echo "<strong>Total results: $totalResults</strong><br>";
        echo "<strong>Literal asterisk found: " . ($literalAsteriskFound ? "YES" : "NO") . "</strong><br>";
        echo "</div>";
        
        echo "<h2>Step 3: Simulate HTML Generation</h2>";
        
        // This is the critical part - simulate exactly how the HTML is generated
        foreach ($searchResults as $termIndex => $termResult) {
            echo "<h3>Term: {$termResult['term']} ({$termResult['count']} results)</h3>";
            
            if ($termResult['count'] > 0) {
                echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
                echo "<strong>Message that should appear:</strong> \"{$termResult['count']} rezultate găsite pentru termenul '{$termResult['term']}'\"<br>";
                echo "</div>";
                
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr style='background: #e9ecef;'>";
                echo "<th>Index</th><th>Case Number</th><th>Institution</th><th>Search Type</th><th>Data Attributes</th>";
                echo "</tr>";
                
                foreach ($termResult['results'] as $index => $dosar) {
                    $caseNumber = htmlspecialchars($dosar->numar ?? '');
                    $institution = htmlspecialchars($dosar->instanta ?? '');
                    $searchType = htmlspecialchars($dosar->searchType ?? '');
                    
                    $hasAsterisk = strpos($dosar->numar, '*') !== false;
                    $rowStyle = $hasAsterisk ? "background: #fff3cd;" : "background: #f8f9fa;";
                    
                    echo "<tr style='$rowStyle' data-numar='$caseNumber' data-search-type='$searchType'>";
                    echo "<td>" . ($index + 1) . "</td>";
                    echo "<td>$caseNumber" . ($hasAsterisk ? " <strong>(ASTERISK!)</strong>" : "") . "</td>";
                    echo "<td>$institution</td>";
                    echo "<td>$searchType</td>";
                    echo "<td>data-numar=\"$caseNumber\" data-search-type=\"$searchType\"</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            }
        }
        
        echo "<h2>Step 4: Check for Filtering Issues</h2>";
        
        // Check if there are any issues with the results
        $issues = [];
        
        if ($totalResults != 3) {
            $issues[] = "Expected 3 total results, got $totalResults";
        }
        
        if (!$literalAsteriskFound) {
            $issues[] = "Literal asterisk case '14096/3/2024*' not found in results";
        }
        
        // Check for duplicate results
        $allCaseNumbers = [];
        foreach ($searchResults as $termResult) {
            foreach ($termResult['results'] as $dosar) {
                $allCaseNumbers[] = $dosar->numar;
            }
        }
        
        $uniqueCaseNumbers = array_unique($allCaseNumbers);
        if (count($allCaseNumbers) != count($uniqueCaseNumbers)) {
            $duplicates = array_diff_assoc($allCaseNumbers, $uniqueCaseNumbers);
            $issues[] = "Duplicate results found: " . implode(', ', $duplicates);
        }
        
        if (empty($issues)) {
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
            echo "<strong>✅ No backend issues found!</strong><br>";
            echo "The problem must be in the frontend JavaScript or HTML rendering.";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
            echo "<strong>❌ Backend issues found:</strong><br>";
            foreach ($issues as $issue) {
                echo "- $issue<br>";
            }
            echo "</div>";
        }
        
        echo "<h2>Step 5: Direct Database Check</h2>";
        
        // Let's check the database directly
        $dosarService = new DosarService();
        
        echo "<h3>Direct search for exact case '14096/3/2024*':</h3>";
        $exactResults = $dosarService->cautareAvansata(['numarDosar' => '14096/3/2024*']);
        echo "<p>Results for exact search: " . count($exactResults) . "</p>";
        
        foreach ($exactResults as $dosar) {
            echo "<div style='background: #fff3cd; padding: 8px; margin: 3px 0; border: 1px solid #ffc107;'>";
            echo "<strong>Found:</strong> {$dosar->numar} at {$dosar->instanta}<br>";
            echo "<strong>Object:</strong> " . substr($dosar->obiect, 0, 100) . "...<br>";
            echo "</div>";
        }
        
        echo "<h3>Direct search for pattern '14096/3/2024':</h3>";
        $patternResults = $dosarService->cautareAvansata(['numarDosar' => '14096/3/2024']);
        echo "<p>Results for pattern search: " . count($patternResults) . "</p>";
        
        foreach ($patternResults as $dosar) {
            echo "<div style='background: #e7f3ff; padding: 8px; margin: 3px 0; border: 1px solid #007bff;'>";
            echo "<strong>Found:</strong> {$dosar->numar} at {$dosar->instanta}<br>";
            echo "</div>";
        }
        
    } else {
        echo "<p>No search terms provided.</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🔍 Next Investigation Steps</h2>";
echo "<p>If the backend shows 3 results correctly, we need to check:</p>";
echo "<ol>";
echo "<li>JavaScript console errors in the browser</li>";
echo "<li>Network tab to see the actual AJAX requests</li>";
echo "<li>HTML source to see if all 3 rows are actually generated</li>";
echo "<li>CSS rules that might be hiding elements</li>";
echo "<li>JavaScript filtering functions that run after page load</li>";
echo "</ol>";
?>
