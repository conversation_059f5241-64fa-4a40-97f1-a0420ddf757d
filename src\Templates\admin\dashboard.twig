<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #2c3e50;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card.success {
            border-left-color: var(--success-color);
        }
        
        .stat-card.warning {
            border-left-color: var(--warning-color);
        }
        
        .stat-card.danger {
            border-left-color: var(--danger-color);
        }
        
        .stat-card.info {
            border-left-color: var(--info-color);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }
        
        .activity-item:hover {
            background-color: #f8f9fa;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-time {
            color: #6c757d;
            font-size: 0.85rem;
        }
        
        .admin-nav {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-nav .nav-link {
            color: var(--secondary-color);
            font-weight: 500;
            padding: 0.75rem 1rem;
            border-radius: 5px;
            transition: all 0.2s;
        }
        
        .admin-nav .nav-link:hover,
        .admin-nav .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .permission-badge {
            background-color: var(--info-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
            margin: 0.1rem;
            display: inline-block;
        }
        
        .role-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.8rem;
        }
        
        .role-super_admin {
            background-color: var(--danger-color);
            color: white;
        }
        
        .role-admin {
            background-color: var(--warning-color);
            color: black;
        }
        
        .role-moderator {
            background-color: var(--info-color);
            color: white;
        }
        
        .role-viewer {
            background-color: var(--success-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Panou Administrativ
                    </h1>
                    <p class="mb-0 opacity-75">Portal Judiciar România</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <strong>{{ user_name }}</strong>
                            <span class="role-badge role-{{ user_role }}">{{ user_role }}</span>
                        </div>
                        <div class="text-end">
                            <small class="d-block opacity-75">{{ current_time }}</small>
                            <a href="../monitor.php" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>
                                Înapoi la Portal
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Admin Navigation -->
        <div class="admin-nav">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link active" href="#dashboard" data-section="dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                {% if 'user_management' in user_permissions %}
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Utilizatori
                    </a>
                </li>
                {% endif %}
                {% if 'security_logs' in user_permissions %}
                <li class="nav-item">
                    <a class="nav-link" href="security.php">
                        <i class="fas fa-shield-alt me-2"></i>Securitate
                    </a>
                </li>
                {% endif %}
                {% if 'gdpr_management' in user_permissions %}
                <li class="nav-item">
                    <a class="nav-link" href="gdpr.php">
                        <i class="fas fa-user-shield me-2"></i>GDPR
                    </a>
                </li>
                {% endif %}
                {% if 'system_monitoring' in user_permissions %}
                <li class="nav-item">
                    <a class="nav-link" href="monitoring.php">
                        <i class="fas fa-chart-line me-2"></i>Monitorizare
                    </a>
                </li>
                {% endif %}
                <li class="nav-item ms-auto">
                    <a class="nav-link text-danger" href="logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Deconectare
                    </a>
                </li>
            </ul>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="admin-section">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card success">
                        <div class="stat-number text-success">{{ stats.users.total_users ?? 0 }}</div>
                        <div class="stat-label">Total Utilizatori</div>
                        <small class="text-muted">
                            {{ stats.users.verified_users ?? 0 }} verificați
                        </small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card info">
                        <div class="stat-number text-info">{{ stats.cases.total_monitored_cases ?? 0 }}</div>
                        <div class="stat-label">Dosare Monitorizate</div>
                        <small class="text-muted">
                            {{ stats.cases.active_cases ?? 0 }} active
                        </small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card warning">
                        <div class="stat-number text-warning">{{ stats.notifications.notifications_24h ?? 0 }}</div>
                        <div class="stat-label">Notificări 24h</div>
                        <small class="text-muted">
                            {{ stats.notifications.failed_notifications ?? 0 }} eșuate
                        </small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card danger">
                        <div class="stat-number text-danger">{{ stats.security.unresolved_incidents ?? 0 }}</div>
                        <div class="stat-label">Incidente Securitate</div>
                        <small class="text-muted">
                            {{ stats.security.incidents_24h ?? 0 }} în ultimele 24h
                        </small>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-bar me-2"></i>
                            Activitate Sistem
                        </h5>
                        <canvas id="activityChart" height="100"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <h5 class="mb-3">
                            <i class="fas fa-history me-2"></i>
                            Activitate Recentă
                        </h5>
                        <div class="activity-list" style="max-height: 400px; overflow-y: auto;">
                            {% for activity in recent_activity %}
                            <div class="activity-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>{{ activity.action|replace({'_': ' '})|title }}</strong>
                                        {% if activity.first_name %}
                                        <br><small class="text-muted">{{ activity.first_name }} {{ activity.last_name }}</small>
                                        {% endif %}
                                    </div>
                                    <small class="activity-time">{{ activity.created_at|date('H:i') }}</small>
                                </div>
                                {% if activity.ip_address %}
                                <small class="text-muted d-block mt-1">IP: {{ activity.ip_address }}</small>
                                {% endif %}
                            </div>
                            {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-info-circle me-2"></i>
                                Nu există activitate recentă
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Permissions Display -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-key me-2"></i>
                        Permisiuni Curente
                    </h5>
                    <div>
                        {% for permission in user_permissions %}
                        <span class="permission-badge">{{ permission|replace({'_': ' '})|title }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // CSRF tokens for AJAX requests
        const csrfTokens = {{ csrf_tokens|json_encode|raw }};
        const rateLimits = {{ rate_limits|json_encode|raw }};
        
        // Initialize activity chart
        const ctx = document.getElementById('activityChart').getContext('2d');
        const activityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Lun', 'Mar', 'Mie', 'Joi', 'Vin', 'Sâm', 'Dum'],
                datasets: [{
                    label: 'Utilizatori Activi',
                    data: [12, 19, 3, 5, 2, 3, 9],
                    borderColor: 'rgb(0, 123, 255)',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Notificări Trimise',
                    data: [2, 3, 20, 5, 1, 4, 8],
                    borderColor: 'rgb(40, 167, 69)',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Navigation handling
        document.querySelectorAll('.nav-link[data-section]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Update active nav
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                // Show/hide sections (for future implementation)
                const section = this.dataset.section;
                console.log('Switching to section:', section);
            });
        });
        
        // Auto-refresh dashboard every 30 seconds
        setInterval(() => {
            // Refresh statistics without full page reload
            console.log('Auto-refreshing dashboard...');
        }, 30000);
    </script>
</body>
</html>
