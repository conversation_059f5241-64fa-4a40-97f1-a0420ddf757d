<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 DEBUGGING MISSING NAMES CONTEXT\n";
echo "===================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n\n";
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    // Test specific missing names
    $missingNames = ['Badic Angela', 'Câlţea Lică', 'Chiţu Gheorghe'];
    
    echo "🔍 SEARCHING FOR MISSING NAMES IN TEXT:\n";
    echo "======================================\n\n";
    
    foreach ($missingNames as $name) {
        echo "Searching for: \"{$name}\"\n";
        
        // Search for exact match
        $pos = stripos($solutieSumarText, $name);
        if ($pos !== false) {
            echo "  Found at position: {$pos}\n";
            
            // Show context (50 chars before and after)
            $start = max(0, $pos - 50);
            $length = min(strlen($solutieSumarText) - $start, 150);
            $context = substr($solutieSumarText, $start, $length);
            echo "  Context: \"" . $context . "\"\n";
        } else {
            echo "  NOT FOUND in text\n";
            
            // Try searching for parts of the name
            $nameParts = explode(' ', $name);
            foreach ($nameParts as $part) {
                if (strlen($part) >= 3) {
                    $partPos = stripos($solutieSumarText, $part);
                    if ($partPos !== false) {
                        echo "  Found part \"{$part}\" at position: {$partPos}\n";
                        $start = max(0, $partPos - 30);
                        $length = min(strlen($solutieSumarText) - $start, 100);
                        $context = substr($solutieSumarText, $start, $length);
                        echo "    Context: \"" . $context . "\"\n";
                    }
                }
            }
        }
        echo "\n";
    }
    
    // Also check if these names might be in different formats
    echo "🔍 CHECKING ALTERNATIVE FORMATS:\n";
    echo "================================\n\n";
    
    foreach ($missingNames as $name) {
        echo "Checking alternatives for: \"{$name}\"\n";
        
        // Try uppercase
        $upperName = strtoupper($name);
        if (stripos($solutieSumarText, $upperName) !== false) {
            echo "  Found uppercase version: \"{$upperName}\"\n";
        }
        
        // Try with different separators
        $nameWithComma = $name . ',';
        if (stripos($solutieSumarText, $nameWithComma) !== false) {
            echo "  Found with comma: \"{$nameWithComma}\"\n";
        }
        
        $nameWithSemicolon = $name . ';';
        if (stripos($solutieSumarText, $nameWithSemicolon) !== false) {
            echo "  Found with semicolon: \"{$nameWithSemicolon}\"\n";
        }
        
        echo "\n";
    }
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
