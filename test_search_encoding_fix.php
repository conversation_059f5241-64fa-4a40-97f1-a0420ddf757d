<?php
/**
 * Comprehensive test for Romanian character encoding fix in search functionality
 * Tests actual search operations to verify no encoding warnings occur
 */

// Capture all errors and warnings
$errors = [];
set_error_handler(function($severity, $message, $file, $line) use (&$errors) {
    $errors[] = [
        'severity' => $severity,
        'message' => $message,
        'file' => basename($file),
        'line' => $line,
        'type' => ($severity & E_WARNING) ? 'WARNING' : (($severity & E_ERROR) ? 'ERROR' : 'NOTICE')
    ];
});

echo "<h1>🔍 Romanian Search Encoding Test</h1>\n";
echo "<p><strong>Testing:</strong> Search functionality with Romanian diacritics - No encoding warnings</p>\n";

// Test Romanian names with diacritics
$testSearches = [
    'Saragea Tudorița',
    'SARAGEA TUDORIȚA', 
    'saragea tudorița',
    '<PERSON><PERSON><PERSON> Mărțișor',
    '<PERSON><PERSON> Ștefan',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',  // Mixed old/new diacritics
    'SC ROMÂNIA SRL',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>'
];

echo "<h2>📊 Search Test Results</h2>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>\n";
echo "<thead style='background: #e9ecef;'>\n";
echo "<tr><th style='padding: 12px;'>Search Term</th><th style='padding: 12px;'>Function Test</th><th style='padding: 12px;'>Warnings</th><th style='padding: 12px;'>Status</th></tr>\n";
echo "</thead>\n";
echo "<tbody>\n";

$totalTests = 0;
$successfulTests = 0;

foreach ($testSearches as $searchTerm) {
    $totalTests++;
    $testErrors = [];
    
    echo "<tr>\n";
    echo "<td style='padding: 10px; font-family: monospace;'>" . htmlspecialchars($searchTerm) . "</td>\n";
    
    // Clear previous errors
    $errors = [];
    
    try {
        // Test the normalizeForSearch function from index.php
        ob_start();
        
        // Include the function (suppress any output)
        include_once 'index.php';
        
        // Test the function
        $normalized = normalizeForSearch($searchTerm);
        
        ob_end_clean();
        
        // Check for encoding-related warnings
        $encodingWarnings = array_filter($errors, function($error) {
            return stripos($error['message'], 'encoding') !== false || 
                   stripos($error['message'], 'mb_convert_encoding') !== false ||
                   stripos($error['message'], 'detect') !== false;
        });
        
        if (empty($encodingWarnings)) {
            echo "<td style='padding: 10px; color: #28a745;'>✅ Success</td>\n";
            echo "<td style='padding: 10px; color: #28a745;'>None</td>\n";
            echo "<td style='padding: 10px; color: #28a745;'>✅ PASS</td>\n";
            $successfulTests++;
        } else {
            echo "<td style='padding: 10px; color: #dc3545;'>❌ Function executed</td>\n";
            echo "<td style='padding: 10px; color: #dc3545;'>" . count($encodingWarnings) . " warnings</td>\n";
            echo "<td style='padding: 10px; color: #dc3545;'>❌ FAIL</td>\n";
            
            // Log the warnings for debugging
            foreach ($encodingWarnings as $warning) {
                error_log("ENCODING WARNING: {$warning['message']} in {$warning['file']}:{$warning['line']}");
            }
        }
        
    } catch (Exception $e) {
        echo "<td style='padding: 10px; color: #dc3545;'>❌ Exception</td>\n";
        echo "<td style='padding: 10px; color: #dc3545;'>Exception: " . htmlspecialchars($e->getMessage()) . "</td>\n";
        echo "<td style='padding: 10px; color: #dc3545;'>❌ FAIL</td>\n";
    }
    
    echo "</tr>\n";
}

echo "</tbody>\n";
echo "</table>\n";

// Summary
$successPercentage = round(($successfulTests / $totalTests) * 100, 1);

echo "<h2>📈 Test Summary</h2>\n";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; margin: 15px 0; border-radius: 8px;'>\n";
echo "<p><strong>Total Tests:</strong> {$totalTests}</p>\n";
echo "<p><strong>Successful (No Warnings):</strong> {$successfulTests}</p>\n";
echo "<p><strong>Success Rate:</strong> <span style='font-size: 1.5em; color: " . ($successPercentage >= 95 ? '#28a745' : ($successPercentage >= 80 ? '#ffc107' : '#dc3545')) . ";'>{$successPercentage}%</span></p>\n";
echo "</div>\n";

// Test actual search operations
echo "<h2>🌐 Live Search Test</h2>\n";
echo "<p>Testing actual search operations with Romanian names:</p>\n";

$liveSearchTests = [
    [
        'term' => 'Saragea Tudorița',
        'url' => 'index.php',
        'method' => 'POST',
        'data' => ['searchTerms' => 'Saragea Tudorița', 'searchType' => 'auto']
    ],
    [
        'term' => 'ȘTEFAN',
        'url' => 'search.php',
        'method' => 'GET',
        'data' => ['numeParte' => 'ȘTEFAN']
    ]
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>\n";
echo "<thead style='background: #e9ecef;'>\n";
echo "<tr><th style='padding: 12px;'>Search Term</th><th style='padding: 12px;'>Page</th><th style='padding: 12px;'>Test URL</th><th style='padding: 12px;'>Status</th></tr>\n";
echo "</thead>\n";
echo "<tbody>\n";

foreach ($liveSearchTests as $test) {
    echo "<tr>\n";
    echo "<td style='padding: 10px; font-family: monospace;'>" . htmlspecialchars($test['term']) . "</td>\n";
    echo "<td style='padding: 10px;'>" . htmlspecialchars($test['url']) . "</td>\n";
    
    // Build test URL
    if ($test['method'] === 'GET') {
        $queryString = http_build_query($test['data']);
        $testUrl = $test['url'] . '?' . $queryString;
    } else {
        $testUrl = $test['url'];
    }
    
    echo "<td style='padding: 10px;'><a href='{$testUrl}' target='_blank'>Test Search</a></td>\n";
    echo "<td style='padding: 10px; color: #007bff;'>🔗 Manual Test</td>\n";
    echo "</tr>\n";
}

echo "</tbody>\n";
echo "</table>\n";

// Overall assessment
echo "<h2>🎯 Overall Assessment</h2>\n";

if ($successPercentage >= 95) {
    echo "<div style='background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ SUCCESS: Encoding Fix Working</h4>\n";
    echo "<p style='color: #155724; margin: 0;'>The Romanian character encoding fix is working correctly. No encoding warnings detected during search operations.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
    echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ ISSUES DETECTED</h4>\n";
    echo "<p style='color: #721c24; margin: 0;'>Some encoding warnings are still occurring. Further investigation needed.</p>\n";
    echo "</div>\n";
}

// Show any remaining errors
if (!empty($errors)) {
    echo "<h2>⚠️ Detected Issues</h2>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>\n";
    echo "<thead style='background: #f8d7da;'>\n";
    echo "<tr><th style='padding: 12px;'>Type</th><th style='padding: 12px;'>Message</th><th style='padding: 12px;'>File</th><th style='padding: 12px;'>Line</th></tr>\n";
    echo "</thead>\n";
    echo "<tbody>\n";
    
    foreach ($errors as $error) {
        $color = ($error['type'] === 'ERROR') ? '#dc3545' : (($error['type'] === 'WARNING') ? '#ffc107' : '#6c757d');
        echo "<tr style='color: {$color};'>\n";
        echo "<td style='padding: 10px;'>{$error['type']}</td>\n";
        echo "<td style='padding: 10px;'>" . htmlspecialchars($error['message']) . "</td>\n";
        echo "<td style='padding: 10px;'>{$error['file']}</td>\n";
        echo "<td style='padding: 10px;'>{$error['line']}</td>\n";
        echo "</tr>\n";
    }
    
    echo "</tbody>\n";
    echo "</table>\n";
}

echo "<h2>🧪 Next Steps</h2>\n";
echo "<ul>\n";
echo "<li>🔍 Click the test search links above to verify no warnings appear in browser console</li>\n";
echo "<li>📝 Perform manual searches with Romanian names like 'Saragea Tudorița'</li>\n";
echo "<li>🌐 Test both main search page and advanced search</li>\n";
echo "<li>📊 Monitor server error logs for any remaining encoding issues</li>\n";
echo "</ul>\n";

echo "<h2>🔧 Technical Details</h2>\n";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 8px;'>\n";
echo "<h4>Encoding Detection Strategy:</h4>\n";
echo "<ol>\n";
echo "<li><strong>Primary:</strong> UTF-8 validation with mb_check_encoding()</li>\n";
echo "<li><strong>Detection:</strong> mb_detect_encoding() with Romanian-specific encoding list</li>\n";
echo "<li><strong>Priority Order:</strong> UTF-8 → ISO-8859-2 → Windows-1250 → ISO-8859-1 → ASCII</li>\n";
echo "<li><strong>Fallback:</strong> Windows-1250 (most common for Romanian legacy systems)</li>\n";
echo "<li><strong>Error Handling:</strong> Warning suppression with proper error logging</li>\n";
echo "</ol>\n";
echo "</div>\n";

// Restore error handler
restore_error_handler();
?>
