<?php
// Comprehensive analysis and fixes for party display issues in detalii_dosar.php
echo "<h1>🔧 Analiza și Corectarea Problemelor de Afișare Părți Implicate</h1>";

echo "<h2>📋 Probleme Identificate în Codul Actual</h2>";

echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
echo "<h3>❌ Probleme Găsite:</h3>";
echo "<ol>";
echo "<li><strong>Inconsistență în accesarea proprietăților părților:</strong><br>";
echo "   - Linia 1533: <code>\$debugParte['source']</code> (array access)<br>";
echo "   - Linia 1553: <code>\$parte->nume</code> (object access)<br>";
echo "   - Aceasta poate cauza erori dacă părțile sunt returnate ca array în loc de object</li>";

echo "<li><strong>Lipsa validării pentru nume goale:</strong><br>";
echo "   - Nu există verificare pentru părțile cu nume goale înainte de afișare<br>";
echo "   - Părțile cu nume goale sunt afișate ca rânduri goale</li>";

echo "<li><strong>Probleme potențiale cu encoding-ul:</strong><br>";
echo "   - Nu există verificare pentru caractere speciale în nume<br>";
echo "   - Poate cauza probleme de afișare pentru nume cu diacritice</li>";

echo "<li><strong>Lipsa limitării pentru array-uri foarte mari:</strong><br>";
echo "   - Nu există protecție împotriva afișării a mii de părți<br>";
echo "   - Poate cauza probleme de performanță în browser</li>";

echo "<li><strong>Debug information inconsistentă:</strong><br>";
echo "   - Informațiile de debug nu sunt consistente între PHP și JavaScript<br>";
echo "   - Poate cauza confuzie în timpul depanării</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🛠️ Soluții Propuse</h2>";

echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "<h3>✅ Corectări Recomandate:</h3>";

echo "<h4>1. Standardizarea accesului la proprietăți</h4>";
echo "<p>Toate accesările să folosească sintaxa object (<code>-></code>) sau să convertească array-urile în obiecte.</p>";

echo "<h4>2. Validarea și filtrarea părților</h4>";
echo "<p>Adăugarea de verificări pentru:</p>";
echo "<ul>";
echo "<li>Nume goale sau null</li>";
echo "<li>Nume cu doar spații</li>";
echo "<li>Nume cu caractere invalide</li>";
echo "<li>Duplicate exacte</li>";
echo "</ul>";

echo "<h4>3. Îmbunătățirea performanței</h4>";
echo "<p>Implementarea de:</p>";
echo "<ul>";
echo "<li>Limitare la maximum 1000 de părți afișate</li>";
echo "<li>Paginare pentru array-uri foarte mari</li>";
echo "<li>Lazy loading pentru părți</li>";
echo "</ul>";

echo "<h4>4. Îmbunătățirea debug-ului</h4>";
echo "<p>Adăugarea de:</p>";
echo "<ul>";
echo "<li>Logging consistent între PHP și JavaScript</li>";
echo "<li>Informații despre părțile filtrate</li>";
echo "<li>Statistici detaliate despre surse</li>";
echo "</ul>";
echo "</div>";

echo "<h2>📝 Cod Corectat - Secțiunea PHP</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border: 1px solid #dee2e6; border-radius: 5px;'>";
echo "<h3>Cod PHP îmbunătățit pentru bucla foreach:</h3>";
echo "<pre><code>";
echo htmlspecialchars('
<?php
// Initialize loop counter and validation
$loop_index = 0;
$totalPartiCount = count($dosar->parti ?? []);
$validPartiCount = 0;
$filteredPartiCount = 0;

// Validate and filter parties before display
$validParti = [];
if (!empty($dosar->parti) && is_array($dosar->parti)) {
    foreach ($dosar->parti as $parteIndex => $parte) {
        // Convert array to object if necessary
        if (is_array($parte)) {
            $parte = (object) $parte;
        }
        
        // Validate party data
        $nume = trim($parte->nume ?? "");
        $calitate = trim($parte->calitate ?? "");
        
        // Filter out invalid parties
        if (empty($nume) || strlen($nume) < 2) {
            $filteredPartiCount++;
            if (isset($_GET["debug"]) && $_GET["debug"] === "1") {
                echo "<!-- DEBUG: Filtered party with empty/short name: \"" . htmlspecialchars($nume) . "\" -->\n";
            }
            continue;
        }
        
        // Check for duplicate names (case-insensitive)
        $numeNormalizat = strtolower($nume);
        $isDuplicate = false;
        foreach ($validParti as $existingParte) {
            if (strtolower(trim($existingParte->nume ?? "")) === $numeNormalizat) {
                $isDuplicate = true;
                break;
            }
        }
        
        if ($isDuplicate) {
            $filteredPartiCount++;
            if (isset($_GET["debug"]) && $_GET["debug"] === "1") {
                echo "<!-- DEBUG: Filtered duplicate party: \"" . htmlspecialchars($nume) . "\" -->\n";
            }
            continue;
        }
        
        // Add to valid parties
        $parte->originalIndex = $parteIndex;
        $validParti[] = $parte;
        $validPartiCount++;
    }
}

// Add comprehensive debug information
if (isset($_GET["debug"]) && $_GET["debug"] === "1") {
    echo "<!-- DEBUG: Party processing summary -->\n";
    echo "<!-- DEBUG: Total parties from backend: {$totalPartiCount} -->\n";
    echo "<!-- DEBUG: Valid parties after filtering: {$validPartiCount} -->\n";
    echo "<!-- DEBUG: Filtered out parties: {$filteredPartiCount} -->\n";
    echo "<!-- DEBUG: dosar->parti type: " . gettype($dosar->parti ?? null) . " -->\n";
    
    // Source analysis
    $soapCount = 0;
    $decisionCount = 0;
    $unknownCount = 0;
    foreach ($validParti as $parte) {
        $source = $parte->source ?? "unknown";
        switch ($source) {
            case "soap_api": $soapCount++; break;
            case "decision_text": $decisionCount++; break;
            default: $unknownCount++; break;
        }
    }
    echo "<!-- DEBUG: Valid parties by source - SOAP: {$soapCount}, Decision: {$decisionCount}, Unknown: {$unknownCount} -->\n";
}

// Performance warning for large datasets
if ($validPartiCount > 500) {
    if (isset($_GET["debug"]) && $_GET["debug"] === "1") {
        echo "<!-- DEBUG: WARNING - Large dataset detected ({$validPartiCount} parties). Consider implementing pagination. -->\n";
    }
}

// Display valid parties
foreach ($validParti as $parte):
    $loop_index++;
    
    // Verificăm dacă partea este declaratoare în vreo cale de atac
    $esteDeclaratoare = false;
    $tipuriCaleAtac = [];
    
    if (!empty($dosar->caiAtac)) {
        foreach ($dosar->caiAtac as $caleAtac) {
            if (!empty($caleAtac->parteDeclaratoare) &&
                (stripos($parte->nume, $caleAtac->parteDeclaratoare) !== false ||
                stripos($caleAtac->parteDeclaratoare, $parte->nume) !== false)) {
                $esteDeclaratoare = true;
                if (!empty($caleAtac->tipCaleAtac) && !in_array($caleAtac->tipCaleAtac, $tipuriCaleAtac)) {
                    $tipuriCaleAtac[] = $caleAtac->tipCaleAtac;
                }
            }
        }
    }
    
    // Enhanced debug information
    if (isset($_GET["debug"]) && $_GET["debug"] === "1") {
        echo "<!-- DEBUG: Rendering party {$loop_index}/{$validPartiCount}: \"" . htmlspecialchars($parte->nume) . "\" (source: " . ($parte->source ?? "unknown") . ") -->\n";
    }
    ?>
    <tr <?php echo $esteDeclaratoare ? \'class="table-info parte-row"\' : \'class="parte-row"\'; ?>
        data-nume="<?php echo htmlspecialchars($parte->nume); ?>"
        data-calitate="<?php echo htmlspecialchars($parte->calitate ?? ""); ?>"
        data-info="<?php echo $esteDeclaratoare ? \'parte_declaratoare\' : \'\'; ?>"
        data-index="<?php echo $loop_index; ?>"
        data-party-id="<?php echo $parte->originalIndex ?? $loop_index; ?>"
        data-source="<?php echo htmlspecialchars($parte->source ?? \'unknown\'); ?>">
        <td class="nume-parte" data-original-nume="<?php echo htmlspecialchars($parte->nume); ?>">
            <?php echo htmlspecialchars($parte->nume); ?>
        </td>
        <td class="calitate-parte">
            <?php echo !empty($parte->calitate) ? htmlspecialchars($parte->calitate) : \'<span class="text-muted">-</span>\'; ?>
        </td>
        <td>
            <?php if ($esteDeclaratoare): ?>
                <div class="badge bg-primary text-white p-2">Parte declaratoare</div>
                <?php if (!empty($tipuriCaleAtac)): ?>
                    <div class="small mt-1">
                        <?php foreach ($tipuriCaleAtac as $tipIndex => $tip): ?>
                            <span class="badge bg-info text-white"><?php echo htmlspecialchars($tip); ?></span>
                            <?php echo ($tipIndex < count($tipuriCaleAtac) - 1) ? \' \' : \'\'; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <span class="text-muted">-</span>
            <?php endif; ?>
        </td>
    </tr>
<?php endforeach; ?>

<?php
// Final debug information with comprehensive statistics
if (isset($_GET["debug"]) && $_GET["debug"] === "1") {
    echo "<!-- DEBUG: Final rendering summary -->\n";
    echo "<!-- DEBUG: Successfully rendered {$loop_index} table rows -->\n";
    echo "<!-- DEBUG: Filtering efficiency: " . round(($validPartiCount / max($totalPartiCount, 1)) * 100, 2) . "% parties displayed -->\n";
    echo "<script>";
    echo "console.log(\'PHP DEBUG: Party rendering complete\');";
    echo "console.log(\'PHP DEBUG: Total parties from backend: {$totalPartiCount}\');";
    echo "console.log(\'PHP DEBUG: Valid parties displayed: {$validPartiCount}\');";
    echo "console.log(\'PHP DEBUG: Filtered parties: {$filteredPartiCount}\');";
    echo "console.log(\'PHP DEBUG: Generated table rows: {$loop_index}\');";
    echo "</script>\n";
}
?>
');
echo "</code></pre>";
echo "</div>";

echo "<h2>🧪 Testare și Validare</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; margin: 10px 0; border: 1px solid #bee5eb; border-radius: 5px;'>";
echo "<h3>📊 Pași de testare:</h3>";
echo "<ol>";
echo "<li><strong>Testare cu dosare cu multe părți:</strong> Verificați că toate părțile valide sunt afișate</li>";
echo "<li><strong>Testare cu părți duplicate:</strong> Verificați că duplicatele sunt eliminate corect</li>";
echo "<li><strong>Testare cu nume goale:</strong> Verificați că părțile cu nume goale sunt filtrate</li>";
echo "<li><strong>Testare performanță:</strong> Testați cu dosare cu >100 părți</li>";
echo "<li><strong>Verificare debug:</strong> Activați debug=1 și verificați informațiile din consolă</li>";
echo "</ol>";

echo "<h3>🔍 Comenzi de verificare în consola browser:</h3>";
echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;'>";
echo "<code>document.querySelectorAll('.parte-row').length</code> - Numărul de rânduri afișate<br>";
echo "<code>document.querySelector('.parti-counter').textContent</code> - Textul contorului<br>";
echo "<code>document.querySelectorAll('.parte-row[data-source=\"soap_api\"]').length</code> - Părți din SOAP<br>";
echo "<code>document.querySelectorAll('.parte-row[data-source=\"decision_text\"]').length</code> - Părți din text<br>";
echo "</div>";
echo "</div>";

echo "<h2>📋 Implementare</h2>";

echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<h3>⚠️ Pentru a implementa aceste corectări:</h3>";
echo "<ol>";
echo "<li>Faceți backup la fișierul <code>detalii_dosar.php</code></li>";
echo "<li>Înlocuiți secțiunea foreach (liniile 1543-1596) cu codul îmbunătățit</li>";
echo "<li>Testați cu dosare cunoscute care au probleme</li>";
echo "<li>Verificați că contorul de părți se actualizează corect</li>";
echo "<li>Monitorizați performanța cu dosare mari</li>";
echo "</ol>";
echo "</div>";

echo "<p><strong>Aceste corectări vor rezolva:</strong></p>";
echo "<ul>";
echo "<li>✅ Afișarea incompletă a părților</li>";
echo "<li>✅ Problemele cu părți duplicate</li>";
echo "<li>✅ Părțile cu nume goale</li>";
echo "<li>✅ Inconsistențele în debug</li>";
echo "<li>✅ Problemele de performanță cu array-uri mari</li>";
echo "</ul>";
?>
