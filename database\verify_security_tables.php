<?php

/**
 * Verify Security Tables - Portal Judiciar România
 */

require_once dirname(__DIR__) . '/bootstrap.php';
use App\Config\Database;

echo "Verifying security tables...\n";

try {
    // Check system_settings
    $settings = Database::fetchAll("SELECT setting_key, setting_value FROM system_settings WHERE category = 'security' LIMIT 5");
    echo "Security settings (" . count($settings) . " found):\n";
    foreach ($settings as $setting) {
        echo "  - " . $setting['setting_key'] . ": " . $setting['setting_value'] . "\n";
    }

    // Check login_attempts table structure
    $loginAttempts = Database::fetchOne("SELECT COUNT(*) as count FROM login_attempts");
    echo "Login attempts table: " . $loginAttempts['count'] . " records\n";

    // Check ip_whitelist
    $whitelist = Database::fetchAll("SELECT ip_address, description FROM ip_whitelist WHERE is_active = 1");
    echo "IP whitelist (" . count($whitelist) . " entries):\n";
    foreach ($whitelist as $entry) {
        echo "  - " . $entry['ip_address'] . ": " . $entry['description'] . "\n";
    }

    // Check security_incidents table
    $incidents = Database::fetchOne("SELECT COUNT(*) as count FROM security_incidents");
    echo "Security incidents table: " . $incidents['count'] . " records\n";

    // Check data_processing_logs table
    $logs = Database::fetchOne("SELECT COUNT(*) as count FROM data_processing_logs");
    echo "Data processing logs table: " . $logs['count'] . " records\n";

    echo "\n✅ All tables verified successfully!\n";
    echo "The admin security page should now be fully functional.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
