<?php
/**
 * Debug the quality extraction process step by step
 */

require_once 'bootstrap.php';

use App\Services\DosarService;

echo "=== DEBUGGING QUALITY EXTRACTION PROCESS ===" . PHP_EOL;
echo "Case: 130/98/2022 from TribunalulIALOMITA" . PHP_EOL;
echo "Target: SARAGEA TUDORIŢA" . PHP_EOL;
echo PHP_EOL;

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$targetParty = 'SARAGEA TUDORIŢA';

try {
    $dosarService = new DosarService();
    
    // Get the case details using the SOAP API
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    // Use reflection to access the private methods
    $reflection = new ReflectionClass($dosarService);
    
    $soapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $soapMethod->setAccessible(true);
    
    $extractMethod = $reflection->getMethod('extractPartiesWithQuality');
    $extractMethod->setAccessible(true);
    
    $response = $soapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Test error");
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $dosar = $dosare[0];
        
        echo "=== TESTING QUALITY EXTRACTION BY PATTERN ===" . PHP_EOL;
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            $allExtractedParties = [];
            
            foreach ($sedinte as $sedintaIndex => $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;
                    
                    echo "--- Session " . ($sedintaIndex + 1) . " ---" . PHP_EOL;
                    
                    // Test the extraction method directly
                    $extractedParties = $extractMethod->invoke($dosarService, $solutieText);
                    
                    if (!empty($extractedParties)) {
                        echo "✅ Extracted " . count($extractedParties) . " parties from this session" . PHP_EOL;
                        
                        foreach ($extractedParties as $party) {
                            if (stripos($party['nume'], $targetParty) !== false) {
                                echo "🎯 TARGET PARTY FOUND: '{$party['nume']}' with quality '{$party['calitate']}'" . PHP_EOL;
                            }
                            $allExtractedParties[] = $party;
                        }
                        
                        // Show first few parties for context
                        echo "Sample parties from this session:" . PHP_EOL;
                        for ($i = 0; $i < min(5, count($extractedParties)); $i++) {
                            echo "  - {$extractedParties[$i]['nume']} ({$extractedParties[$i]['calitate']})" . PHP_EOL;
                        }
                    } else {
                        echo "❌ No parties extracted from this session" . PHP_EOL;
                    }
                    
                    echo PHP_EOL;
                }
            }
            
            echo "=== SUMMARY ===" . PHP_EOL;
            echo "Total parties extracted from decision text: " . count($allExtractedParties) . PHP_EOL;
            
            // Check if target party was found and with what quality
            $targetFound = false;
            foreach ($allExtractedParties as $party) {
                if (stripos($party['nume'], $targetParty) !== false) {
                    echo "🎯 Target party found: '{$party['nume']}' with quality '{$party['calitate']}'" . PHP_EOL;
                    $targetFound = true;
                }
            }
            
            if (!$targetFound) {
                echo "❌ Target party NOT found in extracted parties" . PHP_EOL;
            }
            
            // Show quality distribution
            $qualityCount = [];
            foreach ($allExtractedParties as $party) {
                $quality = $party['calitate'];
                if (!isset($qualityCount[$quality])) {
                    $qualityCount[$quality] = 0;
                }
                $qualityCount[$quality]++;
            }
            
            echo PHP_EOL . "Quality distribution:" . PHP_EOL;
            foreach ($qualityCount as $quality => $count) {
                echo "  - {$quality}: {$count} parties" . PHP_EOL;
            }
            
        }
        
    } else {
        echo "❌ No SOAP response data" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace: " . $e->getTraceAsString() . PHP_EOL;
}
