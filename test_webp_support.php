<?php
echo "=== WebP Support Test ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "GD Extension: " . (extension_loaded('gd') ? 'Loaded' : 'Not loaded') . "\n";

if (extension_loaded('gd')) {
    echo "WebP Support: " . (function_exists('imagewebp') ? 'Available' : 'Not available') . "\n";
    echo "WebP Read Support: " . (function_exists('imagecreatefromwebp') ? 'Available' : 'Not available') . "\n";
    $gdInfo = gd_info();
    echo "WebP Support (gd_info): " . (isset($gdInfo['WebP Support']) && $gdInfo['WebP Support'] ? 'Yes' : 'No') . "\n";
}

echo "ImageMagick Extension: " . (extension_loaded('imagick') ? 'Loaded' : 'Not loaded') . "\n";
if (extension_loaded('imagick')) {
    try {
        $imagick = new Imagick();
        $formats = $imagick->queryFormats('WEBP');
        echo "ImageMagick WebP Support: " . (!empty($formats) ? 'Available' : 'Not available') . "\n";
    } catch (Exception $e) {
        echo "ImageMagick WebP Test Error: " . $e->getMessage() . "\n";
    }
}

// Test if logo.jpg exists
$logoPath = 'images/logo.jpg';
echo "\n=== Image Files Test ===\n";
echo "Logo file exists: " . (file_exists($logoPath) ? 'Yes' : 'No') . "\n";
if (file_exists($logoPath)) {
    echo "Logo file size: " . filesize($logoPath) . " bytes\n";
    echo "Logo file type: " . mime_content_type($logoPath) . "\n";
}

// Test WebP conversion capability
if (extension_loaded('gd') && function_exists('imagewebp') && file_exists($logoPath)) {
    echo "\n=== WebP Conversion Test ===\n";
    try {
        $image = imagecreatefromjpeg($logoPath);
        if ($image) {
            $webpPath = 'images/logo_test.webp';
            $result = imagewebp($image, $webpPath, 80);
            echo "WebP conversion test: " . ($result ? 'Success' : 'Failed') . "\n";
            if ($result && file_exists($webpPath)) {
                echo "WebP file created: " . $webpPath . "\n";
                echo "WebP file size: " . filesize($webpPath) . " bytes\n";
                // Clean up test file
                unlink($webpPath);
                echo "Test WebP file cleaned up\n";
            }
            imagedestroy($image);
        } else {
            echo "Failed to load JPEG image\n";
        }
    } catch (Exception $e) {
        echo "WebP conversion error: " . $e->getMessage() . "\n";
    }
}
?>
