<?php
require_once 'src/Helpers/MinificationHelper.php';
use App\Helpers\MinificationHelper;

echo "=== CSS/JS Minification Test ===\n";

// Minifică toate fișierele CSS
echo "\n--- CSS Minification ---\n";
$cssResults = MinificationHelper::minifyAllCSS('assets/css');

if (empty($cssResults)) {
    echo "No CSS files found or minification failed\n";
} else {
    foreach ($cssResults as $result) {
        echo "✓ " . basename($result['original']) . "\n";
        echo "  Original: " . MinificationHelper::formatFileSize($result['original_size']) . "\n";
        echo "  Minified: " . MinificationHelper::formatFileSize($result['minified_size']) . "\n";
        echo "  Reduction: " . $result['reduction'] . "%\n";
        echo "  Output: " . $result['minified'] . "\n\n";
    }
}

// Minifică toate fișierele JavaScript
echo "\n--- JavaScript Minification ---\n";
$jsResults = MinificationHelper::minifyAllJS('assets/js');

if (empty($jsResults)) {
    echo "No JS files found or minification failed\n";
} else {
    foreach ($jsResults as $result) {
        echo "✓ " . basename($result['original']) . "\n";
        echo "  Original: " . MinificationHelper::formatFileSize($result['original_size']) . "\n";
        echo "  Minified: " . MinificationHelper::formatFileSize($result['minified_size']) . "\n";
        echo "  Reduction: " . $result['reduction'] . "%\n";
        echo "  Output: " . $result['minified'] . "\n\n";
    }
}

// Calculează statistici totale
$totalOriginalSize = 0;
$totalMinifiedSize = 0;
$totalFiles = 0;

foreach (array_merge($cssResults, $jsResults) as $result) {
    $totalOriginalSize += $result['original_size'];
    $totalMinifiedSize += $result['minified_size'];
    $totalFiles++;
}

echo "\n=== Summary ===\n";
echo "Total files processed: $totalFiles\n";
echo "Total original size: " . MinificationHelper::formatFileSize($totalOriginalSize) . "\n";
echo "Total minified size: " . MinificationHelper::formatFileSize($totalMinifiedSize) . "\n";

if ($totalOriginalSize > 0) {
    $totalReduction = round((1 - $totalMinifiedSize / $totalOriginalSize) * 100, 2);
    echo "Total size reduction: {$totalReduction}%\n";
    echo "Space saved: " . MinificationHelper::formatFileSize($totalOriginalSize - $totalMinifiedSize) . "\n";
}

// Verifică fișierele minificate create
echo "\n=== Minified Files Created ===\n";
$minifiedFiles = array_merge(
    glob('assets/css/*.min.css'),
    glob('assets/js/*.min.js')
);

foreach ($minifiedFiles as $file) {
    echo "✓ $file (" . MinificationHelper::formatFileSize(filesize($file)) . ")\n";
}
?>
