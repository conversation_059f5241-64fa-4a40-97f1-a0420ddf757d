<?php

/**
 * Create Security Tables - Simple Script
 * Portal Judiciar România
 */

require_once dirname(__DIR__) . '/bootstrap.php';
use App\Config\Database;

echo "Creating security tables...\n";

try {
    // Create system_settings table
    echo "Creating system_settings table...\n";
    Database::execute("
        CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            category VARCHAR(50) NOT NULL DEFAULT 'general',
            description TEXT NULL,
            data_type ENUM('string', 'integer', 'boolean', 'json') NOT NULL DEFAULT 'string',
            is_public TINYINT(1) NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_setting_key (setting_key),
            INDEX idx_is_public (is_public)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ system_settings table created\n";

    // Create login_attempts table
    echo "Creating login_attempts table...\n";
    Database::execute("
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            success TINYINT(1) NOT NULL DEFAULT 0,
            failure_reason VARCHAR(100) NULL,
            user_id INT UNSIGNED NULL,
            session_id VARCHAR(128) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_email (email),
            INDEX idx_ip_address (ip_address),
            INDEX idx_success (success),
            INDEX idx_created_at (created_at),
            INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ login_attempts table created\n";

    // Create ip_whitelist table
    echo "Creating ip_whitelist table...\n";
    Database::execute("
        CREATE TABLE IF NOT EXISTS ip_whitelist (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL,
            description TEXT NULL,
            created_by INT UNSIGNED NOT NULL,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
            INDEX idx_ip_address (ip_address),
            INDEX idx_is_active (is_active),
            INDEX idx_created_by (created_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ ip_whitelist table created\n";

    // Insert default security settings
    echo "Inserting default security settings...\n";
    $settings = [
        ['password_min_length', '8', 'security', 'Lungimea minimă a parolei', 'integer'],
        ['password_require_uppercase', '1', 'security', 'Necesită litere mari în parolă', 'boolean'],
        ['password_require_lowercase', '1', 'security', 'Necesită litere mici în parolă', 'boolean'],
        ['password_require_numbers', '1', 'security', 'Necesită cifre în parolă', 'boolean'],
        ['password_require_symbols', '1', 'security', 'Necesită simboluri în parolă', 'boolean'],
        ['session_timeout', '3600', 'security', 'Timeout sesiune în secunde', 'integer'],
        ['max_login_attempts', '5', 'security', 'Numărul maxim de încercări de login', 'integer'],
        ['lockout_duration', '900', 'security', 'Durata blocării contului în secunde', 'integer'],
        ['csrf_token_lifetime', '3600', 'security', 'Durata de viață a token-urilor CSRF în secunde', 'integer'],
        ['csrf_strict_mode', '1', 'security', 'Mod strict pentru validarea CSRF', 'boolean'],
        ['rate_limit_login', '5', 'security', 'Limite rate pentru login per oră', 'integer'],
        ['rate_limit_api', '100', 'security', 'Limite rate pentru API per oră', 'integer'],
        ['rate_limit_contact', '5', 'security', 'Limite rate pentru formulare contact per oră', 'integer'],
        ['enable_2fa', '0', 'security', 'Activează autentificarea cu doi factori', 'boolean'],
        ['require_email_verification', '1', 'security', 'Necesită verificarea email-ului', 'boolean'],
        ['enable_ip_whitelist', '0', 'security', 'Activează whitelist-ul de IP-uri', 'boolean'],
        ['enable_geo_blocking', '0', 'security', 'Activează blocarea geografică', 'boolean']
    ];

    foreach ($settings as $setting) {
        Database::execute("
            INSERT IGNORE INTO system_settings (setting_key, setting_value, category, description, data_type) 
            VALUES (?, ?, ?, ?, ?)
        ", $setting);
    }
    echo "✅ Default security settings inserted\n";

    // Insert sample IP whitelist entries
    echo "Inserting sample IP whitelist entries...\n";
    Database::execute("
        INSERT IGNORE INTO ip_whitelist (ip_address, description, created_by, is_active) VALUES
        ('127.0.0.1', 'Localhost - Development', 1, 1),
        ('::1', 'IPv6 Localhost', 1, 1)
    ");
    echo "✅ Sample IP whitelist entries inserted\n";

    // Add resolution column to security_incidents if it doesn't exist
    echo "Adding resolution column to security_incidents...\n";
    try {
        Database::execute("ALTER TABLE security_incidents ADD COLUMN resolution TEXT NULL AFTER notes");
        echo "✅ Resolution column added to security_incidents\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️  Resolution column already exists in security_incidents\n";
        } else {
            throw $e;
        }
    }

    // Update migration tracking
    echo "Updating migration tracking...\n";
    Database::execute("
        INSERT INTO schema_migrations (version, applied_at) 
        VALUES ('005', NOW()) 
        ON DUPLICATE KEY UPDATE applied_at = NOW()
    ");
    echo "✅ Migration tracking updated\n";

    echo "\n🎉 All security tables created successfully!\n";
    echo "You can now access the admin security page.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
