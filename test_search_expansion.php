<?php
/**
 * Test Search Results Expansion Functionality
 * Simulates the search results structure to test JavaScript expansion
 */

// Include necessary files for search functionality
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Simulate search results for testing
$searchResults = [
    [
        'term' => '130/98/2022',
        'type' => 'numarDosar',
        'results' => [
            [
                'numar' => '130/98/2022',
                'institutie' => 'TribunalulIALOMITA',
                'institutie_nume' => 'Tribunalul Ialomița',
                'parti' => ['Test Party 1', 'Test Party 2'],
                'obiect' => 'Test case object'
            ],
            [
                'numar' => '130/98/2022',
                'institutie' => 'CurteadeApelBUCURESTI',
                'institutie_nume' => 'Curtea de Apel București',
                'parti' => ['Test Party 3', 'Test Party 4'],
                'obiect' => 'Another test case'
            ]
        ]
    ],
    [
        'term' => 'Test Party',
        'type' => 'numeParte',
        'results' => [
            [
                'numar' => '100/2023',
                'institutie' => 'TribunalulBUCURESTI',
                'institutie_nume' => 'Tribunalul București',
                'parti' => ['Test Party Name', 'Another Party'],
                'obiect' => 'Civil case test'
            ]
        ]
    ]
];
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Expansion Test - Romanian Judicial Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { background: #f8f9fa; font-family: Arial, sans-serif; }
        .container { max-width: 1200px; margin: 20px auto; }
        .header { background: linear-gradient(135deg, #007bff, #0056b3); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
        .section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        
        /* Search results styles (copied from index.php) */
        .term-results { margin-bottom: 1.5rem; border: 1px solid #dee2e6; border-radius: 8px; overflow: hidden; }
        .term-header { background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 1rem; cursor: pointer; transition: all 0.3s ease; border-bottom: 1px solid #dee2e6; }
        .term-header:hover { background: linear-gradient(135deg, #e9ecef, #dee2e6); }
        .term-content { padding: 0; background: white; }
        .toggle-icon { transition: transform 0.3s ease; color: #6c757d; }
        .table-container { margin: 0; }
        .table { margin-bottom: 0; }
        .table th { background-color: #f8f9fa; border-top: none; font-weight: 600; color: #495057; }
        
        /* Test controls */
        .test-controls { background: #e9ecef; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 5px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .debug-info { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; border-radius: 4px; }
        .status-indicator { padding: 5px 10px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h1>🔧 Search Results Expansion Test</h1>
        <p>Testing the "Expandează rezultatele" functionality</p>
        <p><strong>Purpose:</strong> Verify JavaScript expansion functions work correctly</p>
    </div>

    <div class="section">
        <h2>🎮 Test Controls</h2>
        <div class="test-controls">
            <button type="button" class="test-button" onclick="testFunctions()">Test Functions Exist</button>
            <button type="button" class="test-button" onclick="testElements()">Test Elements Exist</button>
            <button type="button" class="test-button" onclick="expandAllResults()">Expandează toate</button>
            <button type="button" class="test-button" onclick="collapseAllResults()">Restrânge toate</button>
            <button type="button" class="test-button" onclick="testIndividualToggle()">Test Individual Toggle</button>
            <button type="button" class="test-button" onclick="showDebugInfo()">Show Debug Info</button>
        </div>
        
        <div id="debugOutput" class="debug-info" style="display: none;">
            <strong>Debug Output:</strong><br>
            <div id="debugContent"></div>
        </div>
    </div>

    <div class="section">
        <h2>📊 Simulated Search Results</h2>
        
        <!-- Results Section (copied structure from index.php) -->
        <div class="results-section">
            <?php if (!empty($searchResults)): ?>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Rezultate detaliate
                    </h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="expandAllResults()">
                            <i class="fas fa-expand-alt me-1"></i>
                            Expandează toate
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllResults()">
                            <i class="fas fa-compress-alt me-1"></i>
                            Restrânge toate
                        </button>
                    </div>
                </div>
            <?php endif; ?>

            <?php foreach ($searchResults as $index => $result): ?>
                <div class="term-results">
                    <div class="term-header" onclick="toggleTermResults(<?php echo $index; ?>)">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-search me-2"></i>
                                    <?php echo htmlspecialchars($result['term']); ?>
                                    <span class="badge bg-primary ms-2">
                                        <?php echo $result['type'] === 'numarDosar' ? 'Număr dosar' : 'Nume parte'; ?>
                                    </span>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    <?php echo count($result['results']); ?> rezultate găsite
                                </small>
                            </div>
                            <div>
                                <i class="fas fa-chevron-down toggle-icon" id="toggleIcon<?php echo $index; ?>"></i>
                            </div>
                        </div>
                    </div>

                    <div class="term-content" id="termContent<?php echo $index; ?>" style="display: none;">
                        <?php if (!empty($result['results'])): ?>
                            <!-- Desktop/Tablet Table View -->
                            <div class="table-container table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Număr Dosar</th>
                                            <th>Instanță</th>
                                            <th>Părți</th>
                                            <th>Obiect</th>
                                            <th>Acțiuni</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($result['results'] as $case): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($case['numar']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($case['institutie_nume']); ?></td>
                                                <td><?php echo htmlspecialchars(implode(', ', array_slice($case['parti'], 0, 2))); ?></td>
                                                <td><?php echo htmlspecialchars(substr($case['obiect'], 0, 50)) . '...'; ?></td>
                                                <td>
                                                    <a href="detalii_dosar.php?numar=<?php echo urlencode($case['numar']); ?>&institutie=<?php echo urlencode($case['institutie']); ?>" 
                                                       class="btn btn-sm btn-primary" target="_blank">
                                                        <i class="fas fa-eye me-1"></i>
                                                        Detalii
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="section">
        <h2>📋 Test Results</h2>
        <div id="testResults">
            <p>Click the test buttons above to verify functionality.</p>
        </div>
    </div>
</div>

<!-- Notification container -->
<div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1050; display: none;">
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <span id="notificationMessage"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Copy the exact functions from index.php
function showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const messageElement = document.getElementById('notificationMessage');
    
    if (!container || !messageElement) {
        console.log('Notification: ' + message);
        return;
    }
    
    // Set message
    messageElement.textContent = message;
    
    // Set alert type
    const alertDiv = container.querySelector('.alert');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    
    // Show notification
    container.style.display = 'block';
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        container.style.display = 'none';
    }, 5000);
}

// Expand all results function (copied from index.php)
function expandAllResults() {
    const termContents = document.querySelectorAll('[id^="termContent"]');
    const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

    termContents.forEach(content => {
        content.style.display = 'block';
    });

    toggleIcons.forEach(icon => {
        icon.className = 'fas fa-chevron-up toggle-icon';
    });

    showNotification('Toate secțiunile au fost expandate.', 'info');
}

// Collapse all results function (copied from index.php)
function collapseAllResults() {
    const termContents = document.querySelectorAll('[id^="termContent"]');
    const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

    termContents.forEach(content => {
        content.style.display = 'none';
    });

    toggleIcons.forEach(icon => {
        icon.className = 'fas fa-chevron-down toggle-icon';
    });

    showNotification('Toate secțiunile au fost restrânse.', 'info');
}

// Toggle term results visibility (copied from index.php)
function toggleTermResults(index) {
    const content = document.getElementById('termContent' + index);
    const icon = document.getElementById('toggleIcon' + index);

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.className = 'fas fa-chevron-up toggle-icon';
    } else {
        content.style.display = 'none';
        icon.className = 'fas fa-chevron-down toggle-icon';
    }
}

// Test functions
function testFunctions() {
    const results = [];
    
    results.push('expandAllResults: ' + (typeof expandAllResults === 'function' ? '✅ EXISTS' : '❌ MISSING'));
    results.push('collapseAllResults: ' + (typeof collapseAllResults === 'function' ? '✅ EXISTS' : '❌ MISSING'));
    results.push('toggleTermResults: ' + (typeof toggleTermResults === 'function' ? '✅ EXISTS' : '❌ MISSING'));
    results.push('showNotification: ' + (typeof showNotification === 'function' ? '✅ EXISTS' : '❌ MISSING'));
    
    updateTestResults('Function Existence Test', results);
}

function testElements() {
    const results = [];
    
    const termContents = document.querySelectorAll('[id^="termContent"]');
    const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
    
    results.push('termContent elements: ' + termContents.length + ' found');
    results.push('toggleIcon elements: ' + toggleIcons.length + ' found');
    
    termContents.forEach((element, index) => {
        results.push('  - ' + element.id + ': ' + (element ? '✅ EXISTS' : '❌ MISSING'));
    });
    
    toggleIcons.forEach((element, index) => {
        results.push('  - ' + element.id + ': ' + (element ? '✅ EXISTS' : '❌ MISSING'));
    });
    
    updateTestResults('Element Existence Test', results);
}

function testIndividualToggle() {
    const results = [];
    
    // Test toggle for first result
    try {
        toggleTermResults(0);
        results.push('toggleTermResults(0): ✅ SUCCESS');
        
        setTimeout(() => {
            toggleTermResults(0);
            results.push('toggleTermResults(0) again: ✅ SUCCESS');
            updateTestResults('Individual Toggle Test', results);
        }, 1000);
    } catch (error) {
        results.push('toggleTermResults(0): ❌ ERROR - ' + error.message);
        updateTestResults('Individual Toggle Test', results);
    }
}

function showDebugInfo() {
    const debugOutput = document.getElementById('debugOutput');
    const debugContent = document.getElementById('debugContent');
    
    const info = [];
    info.push('DOM Ready State: ' + document.readyState);
    info.push('Bootstrap loaded: ' + (typeof bootstrap !== 'undefined' ? 'YES' : 'NO'));
    info.push('Font Awesome loaded: ' + (document.querySelector('link[href*="font-awesome"]') ? 'YES' : 'NO'));
    info.push('jQuery loaded: ' + (typeof $ !== 'undefined' ? 'YES' : 'NO'));
    
    debugContent.innerHTML = info.join('<br>');
    debugOutput.style.display = 'block';
}

function updateTestResults(testName, results) {
    const testResultsDiv = document.getElementById('testResults');
    
    let html = '<div class="mb-3">';
    html += '<h5>' + testName + '</h5>';
    html += '<ul>';
    
    results.forEach(result => {
        const status = result.includes('✅') ? 'success' : (result.includes('❌') ? 'error' : 'warning');
        html += '<li><span class="status-indicator status-' + status + '">' + result + '</span></li>';
    });
    
    html += '</ul></div>';
    
    testResultsDiv.innerHTML = html + testResultsDiv.innerHTML;
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Search expansion test page loaded');
    showNotification('Test page loaded successfully. Use the test buttons to verify functionality.', 'success');
});
</script>

</body>
</html>
