<?php
/**
 * Comprehensive PDF functionality debugging page
 * This page helps identify and fix PDF save button issues
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

$testResults = [];
$error = null;

// Test case data
$testCaseNumber = $_GET['numar'] ?? '123/2024';
$testInstitution = $_GET['institutie'] ?? 'TribunalulBUCURESTI';

// Perform comprehensive tests
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['test'])) {
    try {
        // Test 1: Check if case exists
        $dosarService = new DosarService();
        $dosarDetails = $dosarService->getDetaliiDosar($testCaseNumber, $testInstitution);
        
        $testResults['case_exists'] = !empty($dosarDetails);
        $testResults['case_data'] = $dosarDetails;
        
        // Test 2: Check PDF generation URLs
        $testResults['pdf_urls'] = [
            'inline' => "generate_pdf.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution) . "&disposition=inline",
            'download' => "generate_pdf.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution) . "&disposition=attachment",
            'case_details' => "detalii_dosar.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution)
        ];
        
        // Test 3: Check server-side PDF generation
        $testResults['server_tests'] = [];
        
        // Test if generate_pdf.php exists
        $testResults['server_tests']['generate_pdf_exists'] = file_exists('generate_pdf.php');
        
        // Test if PdfService exists
        $testResults['server_tests']['pdf_service_exists'] = class_exists('PdfService');
        
    } catch (Exception $e) {
        $error = 'Eroare la testarea funcționalității: ' . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug PDF Functionality - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
        }
        .debug-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .debug-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .debug-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px 8px 0 0;
        }
        .debug-body {
            padding: 1.5rem;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .test-result {
            background: #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .debug-button {
            margin: 0.5rem 0.5rem 0.5rem 0;
            min-width: 150px;
        }
        .console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
            margin: 1rem 0;
        }
        .test-pdf-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-pdf-button:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-card">
            <div class="debug-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-bug mr-2"></i>
                    Debug PDF Functionality - Portal Judiciar
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    Diagnostic complet pentru funcționalitatea "Salvează PDF"
                </p>
            </div>
            <div class="debug-body">
                
                <div class="test-section">
                    <h4><i class="fas fa-play-circle mr-2"></i>Test Rapid PDF</h4>
                    <p>Testați funcționalitatea PDF cu datele curente:</p>
                    
                    <div class="mb-3">
                        <strong>Parametri test:</strong><br>
                        Număr dosar: <code><?php echo htmlspecialchars($testCaseNumber); ?></code><br>
                        Instituție: <code><?php echo htmlspecialchars($testInstitution); ?></code>
                    </div>
                    
                    <div class="mb-3">
                        <button onclick="testPrintFunction()" class="test-pdf-button">
                            <i class="fas fa-file-pdf mr-2"></i>Test Funcție printDosar()
                        </button>
                        <button onclick="testDirectPDF()" class="btn btn-warning debug-button">
                            <i class="fas fa-download mr-2"></i>Test PDF Direct
                        </button>
                        <button onclick="openCaseDetails()" class="btn btn-primary debug-button">
                            <i class="fas fa-external-link-alt mr-2"></i>Deschide Detalii Dosar
                        </button>
                        <button onclick="runFullDiagnostic()" class="btn btn-info debug-button">
                            <i class="fas fa-stethoscope mr-2"></i>Diagnostic Complet
                        </button>
                    </div>
                    
                    <div id="consoleOutput" class="console-output" style="display: none;">
                        <strong>Console Output:</strong><br>
                        <div id="consoleLog"></div>
                    </div>
                </div>

                <?php if (!empty($testResults)): ?>
                <div class="test-section">
                    <h4><i class="fas fa-check-circle mr-2"></i>Rezultate Teste Automate</h4>
                    
                    <div class="test-result <?php echo $testResults['case_exists'] ? 'success' : 'error'; ?>">
                        <strong>Test 1 - Existența dosarului:</strong><br>
                        <?php if ($testResults['case_exists']): ?>
                            ✓ Dosarul a fost găsit cu succes<br>
                            Număr: <?php echo htmlspecialchars($testResults['case_data']->numar ?? 'N/A'); ?><br>
                            Obiect: <?php echo htmlspecialchars($testResults['case_data']->obiect ?? 'N/A'); ?>
                        <?php else: ?>
                            ✗ Dosarul nu a fost găsit sau nu există
                        <?php endif; ?>
                    </div>
                    
                    <div class="test-result info">
                        <strong>Test 2 - URL-uri PDF:</strong><br>
                        PDF Inline: <a href="<?php echo $testResults['pdf_urls']['inline']; ?>" target="_blank"><?php echo $testResults['pdf_urls']['inline']; ?></a><br>
                        PDF Download: <a href="<?php echo $testResults['pdf_urls']['download']; ?>" target="_blank"><?php echo $testResults['pdf_urls']['download']; ?></a><br>
                        Detalii Dosar: <a href="<?php echo $testResults['pdf_urls']['case_details']; ?>" target="_blank"><?php echo $testResults['pdf_urls']['case_details']; ?></a>
                    </div>
                    
                    <div class="test-result <?php echo $testResults['server_tests']['generate_pdf_exists'] ? 'success' : 'error'; ?>">
                        <strong>Test 3 - Server-side PDF:</strong><br>
                        generate_pdf.php: <?php echo $testResults['server_tests']['generate_pdf_exists'] ? '✓ Există' : '✗ Nu există'; ?><br>
                        PdfService class: <?php echo $testResults['server_tests']['pdf_service_exists'] ? '✓ Există' : '✗ Nu există'; ?>
                    </div>
                </div>
                <?php endif; ?>

                <div class="test-section">
                    <h4><i class="fas fa-list-check mr-2"></i>Checklist Manual</h4>
                    <p><strong>Pași pentru testarea manuală:</strong></p>
                    <ol>
                        <li>Deschideți Developer Tools (F12) și mergeți la tab-ul Console</li>
                        <li>Faceți click pe "Test Funcție printDosar()" de mai sus</li>
                        <li>Verificați mesajele din consolă pentru erori</li>
                        <li>Testați butonul "Salvează PDF" din pagina de detalii dosar</li>
                        <li>Verificați că se deschide dialogul de printare</li>
                        <li>Testați "Save as PDF" din dialogul de printare</li>
                    </ol>
                </div>

                <div class="test-section">
                    <h4><i class="fas fa-tools mr-2"></i>Soluții pentru Probleme Comune</h4>
                    
                    <div class="test-result warning">
                        <strong>Problemă: Butonul nu răspunde</strong><br>
                        Soluții:<br>
                        • Verificați că initExportFunctions() se execută<br>
                        • Verificați că elementul #printBtnHeader există<br>
                        • Verificați erorile JavaScript în consolă
                    </div>
                    
                    <div class="test-result warning">
                        <strong>Problemă: Eroare "printContent nu a fost găsit"</strong><br>
                        Soluții:<br>
                        • Verificați că elementele #printContent și #printVersion există<br>
                        • Verificați că HTML-ul este valid și complet încărcat
                    </div>
                    
                    <div class="test-result warning">
                        <strong>Problemă: PDF gol sau incomplet</strong><br>
                        Soluții:<br>
                        • Verificați că getDosarInfo() returnează date valide<br>
                        • Verificați că selectoarele CSS din getDosarInfo() sunt corecte<br>
                        • Verificați că datele dosarului sunt afișate în pagină
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <a href="detalii_dosar.php?numar=<?php echo urlencode($testCaseNumber); ?>&institutie=<?php echo urlencode($testInstitution); ?>" 
                           class="btn btn-primary btn-block" target="_blank">
                            <i class="fas fa-folder-open mr-2"></i>Detalii Dosar
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="generate_pdf.php?numar=<?php echo urlencode($testCaseNumber); ?>&institutie=<?php echo urlencode($testInstitution); ?>&disposition=inline" 
                           class="btn btn-danger btn-block" target="_blank">
                            <i class="fas fa-file-pdf mr-2"></i>PDF Direct
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test_button_functionality.php" class="btn btn-info btn-block">
                            <i class="fas fa-vial mr-2"></i>Test Butoane
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="?test=1&numar=<?php echo urlencode($testCaseNumber); ?>&institutie=<?php echo urlencode($testInstitution); ?>" 
                           class="btn btn-success btn-block">
                            <i class="fas fa-redo mr-2"></i>Rerun Tests
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden elements for PDF testing -->
    <div id="printVersion" class="print-only" style="display: none;">
        <div class="print-header">
            <h1>Test PDF Document</h1>
            <p>Generated at: <?php echo date('d.m.Y H:i'); ?></p>
        </div>
        <div id="printContent"></div>
    </div>

    <script>
        // Console logging override for debugging
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        let consoleMessages = [];
        
        function logToConsole(type, message) {
            consoleMessages.push(`[${type.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}`);
            updateConsoleOutput();
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToConsole('warn', args.join(' '));
        };
        
        function updateConsoleOutput() {
            const output = document.getElementById('consoleOutput');
            const log = document.getElementById('consoleLog');
            
            if (consoleMessages.length > 0) {
                output.style.display = 'block';
                log.innerHTML = consoleMessages.slice(-20).join('<br>'); // Show last 20 messages
                log.scrollTop = log.scrollHeight;
            }
        }
        
        // Test functions
        function testPrintFunction() {
            console.log('=== ÎNCEPE TEST FUNCȚIE printDosar() ===');
            
            // Test if function exists
            if (typeof printDosar === 'function') {
                console.log('✓ Funcția printDosar() există');
                
                // Test if required elements exist
                const printContent = document.getElementById('printContent');
                const printVersion = document.getElementById('printVersion');
                
                console.log('Element #printContent:', printContent ? '✓ Există' : '✗ Nu există');
                console.log('Element #printVersion:', printVersion ? '✓ Există' : '✗ Nu există');
                
                if (printContent && printVersion) {
                    console.log('Toate elementele necesare există, se execută printDosar()...');
                    
                    try {
                        // Create a mock getDosarInfo function for testing
                        if (typeof getDosarInfo !== 'function') {
                            window.getDosarInfo = function() {
                                return {
                                    numar: '<?php echo htmlspecialchars($testCaseNumber); ?>',
                                    instanta: '<?php echo htmlspecialchars($testInstitution); ?>',
                                    data: '<?php echo date('d.m.Y'); ?>',
                                    obiect: 'Test obiect pentru debugging',
                                    stadiuProcesual: 'Test stadiu',
                                    parti: [
                                        { nume: 'Test Parte 1', calitate: 'Reclamant' },
                                        { nume: 'Test Parte 2', calitate: 'Pârât' }
                                    ],
                                    sedinte: [
                                        { data: '<?php echo date('d.m.Y'); ?>', ora: '10:00', complet: 'Test Complet', solutie: 'Test soluție' }
                                    ],
                                    caiAtac: []
                                };
                            };
                            console.log('✓ Funcție getDosarInfo() mock creată');
                        }
                        
                        // Create a mock showNotification function for testing
                        if (typeof showNotification !== 'function') {
                            window.showNotification = function(message, type) {
                                console.log(`NOTIFICARE [${type}]: ${message}`);
                            };
                            console.log('✓ Funcție showNotification() mock creată');
                        }
                        
                        // Initialize isPrinting variable
                        if (typeof isPrinting === 'undefined') {
                            window.isPrinting = false;
                            console.log('✓ Variabilă isPrinting inițializată');
                        }
                        
                        // Execute printDosar function
                        printDosar();
                        
                    } catch (error) {
                        console.error('✗ Eroare la executarea printDosar():', error);
                    }
                } else {
                    console.error('✗ Elementele necesare pentru PDF nu există');
                }
            } else {
                console.error('✗ Funcția printDosar() nu există');
                
                // Try to load it from the case details page
                console.log('Încercare de încărcare a funcției din pagina de detalii...');
                loadPrintFunctionFromCasePage();
            }
            
            console.log('=== SFÂRȘIT TEST FUNCȚIE printDosar() ===');
        }
        
        function testDirectPDF() {
            const url = 'generate_pdf.php?numar=<?php echo urlencode($testCaseNumber); ?>&institutie=<?php echo urlencode($testInstitution); ?>&disposition=attachment';
            console.log('Test PDF direct:', url);
            window.open(url, '_blank');
        }
        
        function openCaseDetails() {
            const url = 'detalii_dosar.php?numar=<?php echo urlencode($testCaseNumber); ?>&institutie=<?php echo urlencode($testInstitution); ?>';
            console.log('Deschidere detalii dosar:', url);
            window.open(url, '_blank');
        }
        
        function runFullDiagnostic() {
            console.log('=== DIAGNOSTIC COMPLET ===');
            
            // Browser info
            console.log('Browser:', navigator.userAgent);
            console.log('jQuery disponibil:', typeof $ !== 'undefined' ? '✓' : '✗');
            console.log('window.print disponibil:', typeof window.print === 'function' ? '✓' : '✗');
            
            // Test elements
            const elements = ['printBtnHeader', 'printContent', 'printVersion', 'notificationContainer'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                console.log(`Element #${id}:`, element ? '✓ Există' : '✗ Nu există');
            });
            
            // Test functions
            const functions = ['printDosar', 'getDosarInfo', 'showNotification', 'initExportFunctions'];
            functions.forEach(funcName => {
                console.log(`Funcția ${funcName}():`, typeof window[funcName] === 'function' ? '✓ Există' : '✗ Nu există');
            });
            
            console.log('=== SFÂRȘIT DIAGNOSTIC ===');
        }
        
        function loadPrintFunctionFromCasePage() {
            // This would load the actual printDosar function from the case details page
            console.log('Încărcare funcții din pagina de detalii dosar...');
            
            fetch('detalii_dosar.php?numar=<?php echo urlencode($testCaseNumber); ?>&institutie=<?php echo urlencode($testInstitution); ?>')
                .then(response => response.text())
                .then(html => {
                    // Extract and execute the JavaScript from the case details page
                    const scriptRegex = /<script[^>]*>([\s\S]*?)<\/script>/gi;
                    let match;
                    
                    while ((match = scriptRegex.exec(html)) !== null) {
                        const scriptContent = match[1];
                        if (scriptContent.includes('function printDosar')) {
                            console.log('✓ Funcția printDosar() găsită în pagina de detalii');
                            try {
                                eval(scriptContent);
                                console.log('✓ Funcția printDosar() încărcată cu succes');
                            } catch (error) {
                                console.error('✗ Eroare la încărcarea funcției:', error);
                            }
                            break;
                        }
                    }
                })
                .catch(error => {
                    console.error('✗ Eroare la încărcarea paginii de detalii:', error);
                });
        }
        
        // Initialize debugging
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded successfully');
            console.log('Test parameters:', {
                caseNumber: '<?php echo htmlspecialchars($testCaseNumber); ?>',
                institution: '<?php echo htmlspecialchars($testInstitution); ?>'
            });
        });
    </script>
</body>
</html>
