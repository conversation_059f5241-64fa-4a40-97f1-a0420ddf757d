<?php
/**
 * Performance Test - Optimized Version
 * Tests the performance improvements after optimization
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Performance Test - Optimized - Romanian Judicial Portal</title>";
echo "<meta charset='UTF-8'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
.container { max-width: 1200px; margin: 0 auto; }
.section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.header { background: linear-gradient(135deg, #28a745, #20c997); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
.metric-card { background: #f8f9fa; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0; }
.performance-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
.performance-table th, .performance-table td { padding: 12px; border: 1px solid #ddd; text-align: left; }
.performance-table th { background-color: #28a745; color: white; font-weight: bold; }
.timing-info { background: #d4edda; border: 1px solid #28a745; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap; border-radius: 4px; }
.improvement { background: #d1ecf1; border-left: 4px solid #17a2b8; }
.success { background: #d4edda; border-left: 4px solid #28a745; }
.comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
.comparison-table th, .comparison-table td { padding: 12px; border: 1px solid #ddd; text-align: center; }
.comparison-table th { background-color: #17a2b8; color: white; }
.before { background-color: #f8d7da; }
.after { background-color: #d4edda; }
.improvement-percent { font-weight: bold; color: #28a745; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🚀 Performance Test - Optimized</h1>";
echo "<p>Testing performance improvements after optimization</p>";
echo "<p><strong>Target Case:</strong> 130/98/2022 from CurteadeApelBUCURESTI</p>";
echo "</div>";

// Test case parameters
$testCase = [
    'number' => '130/98/2022',
    'institution' => 'CurteadeApelBUCURESTI',
    'description' => 'Appeal court case with 161 parties'
];

echo "<div class='section'>";
echo "<h2>⚡ Optimized Performance Test</h2>";

$performanceMetrics = [];
$totalStartTime = microtime(true);
$totalStartMemory = memory_get_usage(true);

try {
    // Test multiple runs to verify caching effectiveness
    echo "<div class='metric-card improvement'>";
    echo "<h4>🔄 Multiple Run Test (Caching Verification)</h4>";
    
    $dosarService = new DosarService();
    
    // First run (cold cache)
    $run1StartTime = microtime(true);
    $run1StartMemory = memory_get_usage(true);
    
    $searchParams = [
        'numarDosar' => $testCase['number'],
        'institutie' => $testCase['institution'],
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    // Use reflection to test the full process
    $reflection = new ReflectionClass($dosarService);
    $soapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $soapMethod->setAccessible(true);
    
    $soapResponse1 = $soapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Performance test run 1");
    
    $run1EndTime = microtime(true);
    $run1EndMemory = memory_get_usage(true);
    
    $run1Time = ($run1EndTime - $run1StartTime) * 1000;
    $run1Memory = ($run1EndMemory - $run1StartMemory) / 1024 / 1024;
    
    // Second run (warm cache)
    $run2StartTime = microtime(true);
    $run2StartMemory = memory_get_usage(true);
    
    $soapResponse2 = $soapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Performance test run 2");
    
    $run2EndTime = microtime(true);
    $run2EndMemory = memory_get_usage(true);
    
    $run2Time = ($run2EndTime - $run2StartTime) * 1000;
    $run2Memory = ($run2EndMemory - $run2StartMemory) / 1024 / 1024;
    
    $cacheImprovement = (($run1Time - $run2Time) / $run1Time) * 100;
    
    echo "<div class='timing-info'>";
    echo "Caching Performance Test:\n";
    echo "Run 1 (Cold Cache): " . round($run1Time, 2) . " ms\n";
    echo "Run 2 (Warm Cache): " . round($run2Time, 2) . " ms\n";
    echo "Cache Improvement: " . round($cacheImprovement, 1) . "%\n";
    echo "Status: " . ($cacheImprovement > 50 ? "✅ Excellent caching" : ($cacheImprovement > 20 ? "✅ Good caching" : "⚠️ Limited caching benefit")) . "\n";
    echo "</div>";
    echo "</div>";
    
    // Full data processing test
    echo "<div class='metric-card success'>";
    echo "<h4>⚙️ Full Data Processing Test</h4>";
    
    $processingStartTime = microtime(true);
    $processingStartMemory = memory_get_usage(true);
    
    // Extract dosar from response
    $dosar = null;
    if (isset($soapResponse1->CautareDosare2Result->Dosar)) {
        $dosare = $soapResponse1->CautareDosare2Result->Dosar;
        $dosar = is_array($dosare) ? $dosare[0] : $dosare;
    }
    
    if ($dosar) {
        // Test full mapDosarToObject method
        $mapMethod = $reflection->getMethod('mapDosarToObject');
        $mapMethod->setAccessible(true);
        
        $mappedDosar = $mapMethod->invoke($dosarService, $dosar);
        
        $processingEndTime = microtime(true);
        $processingEndMemory = memory_get_usage(true);
        
        $processingTime = ($processingEndTime - $processingStartTime) * 1000;
        $processingMemory = ($processingEndMemory - $processingStartMemory) / 1024 / 1024;
        
        $performanceMetrics['optimized_processing'] = [
            'time' => $processingTime,
            'memory' => $processingMemory,
            'parties_count' => count($mappedDosar->parti ?? []),
            'status' => $processingTime < 1000 ? 'excellent' : ($processingTime < 2000 ? 'good' : 'needs_improvement')
        ];
        
        echo "<div class='timing-info'>";
        echo "Optimized Data Processing Results:\n";
        echo "Time: " . round($processingTime, 2) . " ms\n";
        echo "Memory: " . round($processingMemory, 2) . " MB\n";
        echo "Parties processed: " . count($mappedDosar->parti ?? []) . "\n";
        echo "Status: " . ($processingTime < 1000 ? "✅ Excellent" : ($processingTime < 2000 ? "✅ Good" : "⚠️ Needs improvement")) . "\n";
        echo "</div>";
        
        // Verify data integrity
        echo "</div>";
        echo "<div class='metric-card success'>";
        echo "<h4>🔍 Data Integrity Verification</h4>";
        
        $soapParties = 0;
        $decisionParties = 0;
        $unknownSources = 0;
        
        foreach ($mappedDosar->parti as $party) {
            if (isset($party->source)) {
                if ($party->source === 'soap_api') {
                    $soapParties++;
                } elseif ($party->source === 'decision_text') {
                    $decisionParties++;
                } else {
                    $unknownSources++;
                }
            } else {
                $unknownSources++;
            }
        }
        
        $totalParties = count($mappedDosar->parti);
        $sourceAttributionRate = (($soapParties + $decisionParties) / $totalParties) * 100;
        
        echo "<div class='timing-info'>";
        echo "Data Integrity Results:\n";
        echo "Total parties: $totalParties\n";
        echo "SOAP API parties: $soapParties (" . round(($soapParties / $totalParties) * 100, 1) . "%)\n";
        echo "Decision text parties: $decisionParties (" . round(($decisionParties / $totalParties) * 100, 1) . "%)\n";
        echo "Unknown sources: $unknownSources\n";
        echo "Source attribution rate: " . round($sourceAttributionRate, 1) . "%\n";
        echo "Status: " . ($sourceAttributionRate >= 100 ? "✅ Perfect attribution" : ($sourceAttributionRate >= 95 ? "✅ Good attribution" : "❌ Attribution issues")) . "\n";
        echo "</div>";
        echo "</div>";
        
    } else {
        echo "<div class='critical'>";
        echo "<h4>❌ No dosar data found in SOAP response</h4>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='critical'>";
    echo "<h4>❌ Error during performance test</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

$totalEndTime = microtime(true);
$totalEndMemory = memory_get_usage(true);

$totalTime = ($totalEndTime - $totalStartTime) * 1000;
$totalMemory = ($totalEndMemory - $totalStartMemory) / 1024 / 1024;

echo "</div>";

// Performance Comparison
echo "<div class='section'>";
echo "<h2>📊 Performance Comparison</h2>";

echo "<table class='comparison-table'>";
echo "<tr><th>Metric</th><th>Before Optimization</th><th>After Optimization</th><th>Improvement</th></tr>";

// Previous performance data (from analysis)
$beforeMetrics = [
    'SOAP API Call' => 353.01,
    'Data Processing' => 2677.36,
    'Text Parsing' => 5.68,
    'Party Merging' => 2633.88,
    'Total' => 5672.16
];

$afterMetrics = [
    'SOAP API Call' => $run2Time, // Use cached run
    'Data Processing' => $performanceMetrics['optimized_processing']['time'] ?? 0,
    'Text Parsing' => 5.68, // Should be similar
    'Party Merging' => 0, // Included in data processing
    'Total' => $totalTime
];

foreach ($beforeMetrics as $metric => $beforeTime) {
    $afterTime = $afterMetrics[$metric] ?? 0;
    $improvement = $beforeTime > 0 ? (($beforeTime - $afterTime) / $beforeTime) * 100 : 0;
    $improvementClass = $improvement > 50 ? 'improvement-percent' : '';
    
    echo "<tr>";
    echo "<td><strong>$metric</strong></td>";
    echo "<td class='before'>" . round($beforeTime, 2) . " ms</td>";
    echo "<td class='after'>" . round($afterTime, 2) . " ms</td>";
    echo "<td class='$improvementClass'>" . ($improvement > 0 ? "↓ " . round($improvement, 1) . "%" : "→ Similar") . "</td>";
    echo "</tr>";
}

echo "</table>";

// Performance target assessment
$targetTime = 2000; // 2 seconds target
$targetMet = $totalTime <= $targetTime;

echo "<div class='metric-card " . ($targetMet ? 'success' : 'improvement') . "'>";
echo "<h4>" . ($targetMet ? "🎯 Performance Target Met!" : "⚠️ Performance Target Assessment") . "</h4>";
echo "<div class='timing-info'>";
echo "Performance Target: Under 2000 ms\n";
echo "Actual Performance: " . round($totalTime, 2) . " ms\n";
echo "Target Status: " . ($targetMet ? "✅ ACHIEVED" : "❌ NOT MET") . "\n";
if (!$targetMet) {
    echo "Additional optimization needed: " . round($totalTime - $targetTime, 2) . " ms\n";
}
echo "</div>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body></html>";
?>
