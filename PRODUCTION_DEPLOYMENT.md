# Portal Judiciar România - Production Deployment Guide

## 404 Error Troubleshooting for just.gabrielanghel.ro

### Possible Causes and Solutions

#### 1. File Structure Issues
- **Problem**: Files might not be uploaded to the correct directory
- **Solution**: Ensure all files are in the web root directory (public_html or www)
- **Check**: Verify index.php exists in the root directory

#### 2. .htaccess Configuration
- **Problem**: .htaccess rules might not be compatible with production server
- **Solution**: The main .htaccess has been updated with production-friendly rules
- **Check**: Ensure mod_rewrite is enabled on the server

#### 3. PHP Version Compatibility
- **Problem**: Production server might have different PHP version
- **Solution**: Ensure PHP 7.4+ is available
- **Check**: Verify all PHP extensions are installed (SOAP, cURL, etc.)

#### 4. File Permissions
- **Problem**: Incorrect file permissions
- **Solution**: Set proper permissions:
  - Files: 644
  - Directories: 755
  - index.php: 644

#### 5. Domain Configuration
- **Problem**: Domain not pointing to correct directory
- **Solution**: Verify DNS and hosting configuration
- **Check**: Ensure just.gabrielanghel.ro points to the correct web root

### Deployment Checklist

1. **Upload Files**
   - [ ] Upload all PHP files to web root
   - [ ] Upload assets/ directory with minified CSS/JS
   - [ ] Upload src/ directory with all classes
   - [ ] Upload images/ directory
   - [ ] Upload .htaccess file

2. **Verify Configuration**
   - [ ] Check PHP version (7.4+)
   - [ ] Verify SOAP extension is enabled
   - [ ] Test mod_rewrite functionality
   - [ ] Check file permissions

3. **Test Functionality**
   - [ ] Access homepage (just.gabrielanghel.ro)
   - [ ] Test search functionality
   - [ ] Verify CSS/JS loading
   - [ ] Check SOAP API connectivity

### Quick Fix Commands

```bash
# Set correct permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;

# Test .htaccess
curl -I http://just.gabrielanghel.ro/

# Check PHP info
echo "<?php phpinfo(); ?>" > info.php
```

### Contact Information
If issues persist, check:
- Hosting provider documentation
- Server error logs
- PHP error logs
