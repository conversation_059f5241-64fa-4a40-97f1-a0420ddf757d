<?php

/**
 * <PERSON>șier de configurare pentru constante globale
 */

// URL-ul WSDL pentru API-ul SOAP
if (!defined('SOAP_WSDL')) {
    define('SOAP_WSDL', 'http://portalquery.just.ro/query.asmx?WSDL');
}

// Configurare pentru căutare
define('MAX_SEARCH_RESULTS', 1000);

// Configurare pentru loguri
define('LOG_DIR', dirname(__DIR__, 2) . '/logs');

// Configurare pentru cache
define('CACHE_DIR', dirname(__DIR__, 2) . '/cache');
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 oră

// Configurare pentru export
define('EXPORT_DIR', dirname(__DIR__, 2) . '/temp');

// Configurare pentru baza de date
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
if (!defined('DB_NAME')) {
    define('DB_NAME', 'portal_judiciar');
}
if (!defined('DB_USER')) {
    define('DB_USER', 'root');
}
if (!defined('DB_PASS')) {
    define('DB_PASS', '');
}
if (!defined('DB_CHARSET')) {
    define('DB_CHARSET', 'utf8mb4');
}

// Configurare pentru email
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // Înlocuiți cu email-ul real
define('SMTP_PASSWORD', 'Eusuntacum22'); // Înlocuiți cu parola de aplicație
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_NAME', 'Portal Judiciar');

// Configurare pentru monitorizarea dosarelor
define('MAX_MONITORED_CASES_PER_USER', 50);
define('CASE_CHECK_INTERVAL_MINUTES', 30);
define('NOTIFICATION_BATCH_SIZE', 100);
define('CASE_SNAPSHOT_RETENTION_DAYS', 90);
define('MAX_NOTIFICATION_ATTEMPTS', 3);
