<?php
/**
 * Comprehensive debug script for hybrid party extraction
 * Analyzes the specific Bucharest case to identify extraction issues
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🔍 Hybrid Party Extraction Debug Analysis</h1>\n";
echo "<p><strong>Target Case:</strong> 130/98/2022 from Curtea de Apel BUCURESTI</p>\n";
echo "<p><strong>Expected:</strong> 500+ parties with hybrid extraction</p>\n";

try {
    // Initialize service
    $dosarService = new DosarService();
    
    // Test parameters
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    echo "<h2>📊 Step 1: Current Party Count Analysis</h2>\n";
    
    // Get case details using the same method as detalii_dosar.php
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if ($dosar && !empty($dosar->numar)) {
        echo "<h3>✅ Case Found</h3>\n";
        echo "<p><strong>Case Number:</strong> {$dosar->numar}</p>\n";
        echo "<p><strong>Institution:</strong> {$dosar->institutie}</p>\n";
        echo "<p><strong>Object:</strong> " . htmlspecialchars(substr($dosar->obiect ?? '', 0, 100)) . "...</p>\n";
        
        // Analyze current party count
        $totalParties = count($dosar->parti ?? []);
        echo "<p><strong>🎯 CURRENT TOTAL PARTIES:</strong> <span style='font-size: 1.5em; color: " . ($totalParties >= 500 ? 'green' : 'red') . ";'>{$totalParties}</span></p>\n";
        
        if ($totalParties >= 500) {
            echo "<p style='color: green;'><strong>✅ SUCCESS: Case shows 500+ parties - hybrid extraction is working!</strong></p>\n";
        } else {
            echo "<p style='color: red;'><strong>❌ ISSUE: Case shows only {$totalParties} parties - hybrid extraction may not be working</strong></p>\n";
        }
        
        // Analyze party sources
        $soapCount = 0;
        $decisionCount = 0;
        $unknownCount = 0;
        
        foreach ($dosar->parti as $parte) {
            $source = $parte->source ?? 'unknown';
            switch ($source) {
                case 'soap_api': $soapCount++; break;
                case 'decision_text': $decisionCount++; break;
                default: $unknownCount++; break;
            }
        }
        
        echo "<h3>📈 Party Source Breakdown</h3>\n";
        echo "<p><strong>SOAP API Parties:</strong> {$soapCount}</p>\n";
        echo "<p><strong>Decision Text Parties:</strong> {$decisionCount}</p>\n";
        echo "<p><strong>Unknown Source:</strong> {$unknownCount}</p>\n";
        
        if ($soapCount >= 100 && $decisionCount == 0) {
            echo "<p style='color: orange;'><strong>⚠️ POTENTIAL ISSUE: SOAP API has 100+ parties but no decision text parties found</strong></p>\n";
        } elseif ($soapCount >= 100 && $decisionCount > 0) {
            echo "<p style='color: green;'><strong>✅ GOOD: Hybrid extraction is working - both SOAP and decision text parties found</strong></p>\n";
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ Case not found</strong></p>\n";
        exit;
    }
    
    echo "<hr>\n";
    echo "<h2>🔬 Step 2: Raw SOAP API Analysis</h2>\n";
    
    // Access private method to get raw SOAP response
    $reflection = new ReflectionClass($dosarService);
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $response = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Debug analysis");
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $rawDosar = is_array($response->CautareDosare2Result->Dosar) ? $response->CautareDosare2Result->Dosar[0] : $response->CautareDosare2Result->Dosar;
        
        // Count raw SOAP parties
        $rawSoapPartyCount = 0;
        if (isset($rawDosar->parti) && isset($rawDosar->parti->DosarParte)) {
            $rawParti = $rawDosar->parti->DosarParte;
            $rawSoapPartyCount = is_array($rawParti) ? count($rawParti) : 1;
        }
        
        echo "<p><strong>Raw SOAP API Party Count:</strong> {$rawSoapPartyCount}</p>\n";
        
        if ($rawSoapPartyCount >= 100) {
            echo "<p style='color: orange;'><strong>🎯 TRIGGER CONDITION MET: SOAP API returned {$rawSoapPartyCount} parties (≥100) - Decision text parsing should be triggered</strong></p>\n";
        } else {
            echo "<p style='color: blue;'><strong>ℹ️ SOAP API returned {$rawSoapPartyCount} parties (<100) - Decision text parsing may not be needed</strong></p>\n";
        }
        
        // Analyze decision text availability
        $hasDecisionText = false;
        $decisionTextLength = 0;
        
        if (isset($rawDosar->sedinte) && isset($rawDosar->sedinte->DosarSedinta)) {
            $sedinte = $rawDosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            foreach ($sedinte as $sedinta) {
                if (isset($sedinta->solutie) && !empty($sedinta->solutie)) {
                    $hasDecisionText = true;
                    $decisionTextLength += strlen($sedinta->solutie);
                }
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $hasDecisionText = true;
                    $decisionTextLength += strlen($sedinta->solutieSumar);
                }
            }
        }
        
        echo "<p><strong>Decision Text Available:</strong> " . ($hasDecisionText ? "✅ Yes ({$decisionTextLength} chars)" : "❌ No") . "</p>\n";
        
        if ($rawSoapPartyCount >= 100 && !$hasDecisionText) {
            echo "<p style='color: red;'><strong>❌ PROBLEM: SOAP limit reached but no decision text available for extraction</strong></p>\n";
        } elseif ($rawSoapPartyCount >= 100 && $hasDecisionText) {
            echo "<p style='color: green;'><strong>✅ GOOD: SOAP limit reached and decision text available - hybrid extraction should work</strong></p>\n";
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ No raw SOAP response data</strong></p>\n";
    }
    
    echo "<hr>\n";
    echo "<h2>🧪 Step 3: Manual Decision Text Extraction Test</h2>\n";
    
    if (isset($rawDosar)) {
        // Test decision text extraction manually
        $extractMethod = $reflection->getMethod('extractPartiesFromDecisionText');
        $extractMethod->setAccessible(true);
        
        $extractedParties = $extractMethod->invoke($dosarService, $rawDosar);
        
        echo "<p><strong>Manual Decision Text Extraction Result:</strong> " . count($extractedParties) . " parties</p>\n";
        
        if (count($extractedParties) > 0) {
            echo "<p style='color: green;'><strong>✅ Decision text extraction is working - found " . count($extractedParties) . " additional parties</strong></p>\n";
            
            // Show first few extracted parties
            echo "<h4>Sample Extracted Parties:</h4>\n";
            echo "<ul>\n";
            for ($i = 0; $i < min(10, count($extractedParties)); $i++) {
                $party = $extractedParties[$i];
                echo "<li>" . htmlspecialchars($party['nume']) . " (" . $party['calitate'] . ")</li>\n";
            }
            echo "</ul>\n";
            
        } else {
            echo "<p style='color: red;'><strong>❌ Decision text extraction found 0 parties - extraction patterns may need improvement</strong></p>\n";
        }
        
        // Calculate expected total
        $expectedTotal = $rawSoapPartyCount + count($extractedParties);
        echo "<p><strong>Expected Total After Hybrid Extraction:</strong> {$expectedTotal} parties</p>\n";
        
        if ($expectedTotal >= 500) {
            echo "<p style='color: green;'><strong>✅ Expected total meets requirement (500+ parties)</strong></p>\n";
        } else {
            echo "<p style='color: orange;'><strong>⚠️ Expected total ({$expectedTotal}) is less than 500 - may need pattern enhancement</strong></p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p><strong>Stack trace:</strong></p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<hr>\n";
echo "<h2>📋 Step 4: Diagnostic Summary</h2>\n";
echo "<p>This analysis helps identify:</p>\n";
echo "<ul>\n";
echo "<li><strong>Current party count vs expected 500+</strong></li>\n";
echo "<li><strong>Whether SOAP API limit (100) is being reached</strong></li>\n";
echo "<li><strong>Whether decision text is available for extraction</strong></li>\n";
echo "<li><strong>Whether extraction patterns are finding parties</strong></li>\n";
echo "<li><strong>Whether hybrid extraction logic is working correctly</strong></li>\n";
echo "</ul>\n";
echo "<p><strong>Next steps will be determined based on these findings.</strong></p>\n";
