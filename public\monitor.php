<?php

/**
 * Case Monitoring Dashboard
 * 
 * Main interface for users to manage their monitored cases,
 * view changes, and configure notification preferences.
 */

require_once dirname(__DIR__) . '/bootstrap.php';

use App\Helpers\TemplateEngine;
use App\Helpers\SecurityHelper;
use App\Services\CaseMonitoringService;
use App\Services\DosarService;
use App\Config\Database;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use App\Security\GDPRCompliance;

// Session is already started in bootstrap.php

// Initialize services
$templateEngine = new TemplateEngine();
$securityHelper = new SecurityHelper();
$monitoringService = new CaseMonitoringService();
$dosarService = new DosarService();

// Check if user is logged in (for now, we'll use a simple session check)
// In a full implementation, this would integrate with a proper authentication system
if (!isset($_SESSION['user_id'])) {
    // For development, create a demo user session
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Demo User';
    $_SESSION['user_email'] = '<EMAIL>';
}

$userId = $_SESSION['user_id'];
$userName = $_SESSION['user_name'] ?? 'Utilizator';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        $action = $_POST['action'];

        // Check rate limiting first
        $rateLimitCheck = RateLimiter::checkLimit($action, "user_{$userId}");
        if (!$rateLimitCheck['allowed']) {
            echo json_encode([
                'success' => false,
                'error' => 'Prea multe cereri. Vă rugăm să încercați din nou mai târziu.',
                'rate_limit' => $rateLimitCheck
            ]);
            exit;
        }

        // Verify CSRF token
        if (!CSRFProtection::validateRequest($_POST, $action)) {
            RateLimiter::recordAttempt($action, "user_{$userId}", false, ['reason' => 'csrf_invalid']);
            throw new Exception('Token CSRF invalid');
        }

        // Execute action with rate limiting enforcement
        $result = RateLimiter::enforce($action, function() use ($action, $monitoringService, $dosarService, $userId) {
            switch ($action) {
                case 'add_case':
                $result = handleAddCase($monitoringService, $userId, $_POST);
                echo json_encode(['success' => true, 'data' => $result, 'message' => 'Dosarul a fost adăugat cu succes în monitorizare']);
                break;
                
            case 'remove_case':
                $caseId = (int) ($_POST['case_id'] ?? 0);
                $result = $monitoringService->removeCaseFromMonitoring($userId, $caseId);
                echo json_encode(['success' => true, 'message' => 'Dosarul a fost eliminat din monitorizare']);
                break;
                
            case 'update_frequency':
                $result = handleUpdateFrequency($userId, $_POST);
                echo json_encode(['success' => true, 'message' => 'Frecvența notificărilor a fost actualizată']);
                break;
                
            case 'check_case':
                $result = handleCheckCase($dosarService, $_POST);
                return ['success' => true, 'data' => $result];

            case 'record_gdpr_consent':
                $consents = json_decode($_POST['consents'] ?? '{}', true);
                $result = handleGDPRConsent($userId, $consents);
                return $result;

            default:
                throw new Exception('Acțiune necunoscută');
        }
        }, "user_{$userId}");

        // Return the result from rate limiter enforcement
        echo json_encode($result);

    } catch (Exception $e) {
        // Record failed attempt
        RateLimiter::recordAttempt($_POST['action'] ?? 'unknown', "user_{$userId}", false, [
            'error' => $e->getMessage()
        ]);

        http_response_code(400);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get user's monitored cases
$monitoredCases = $monitoringService->getUserMonitoredCases($userId);
$casesCount = count($monitoredCases);
$maxCases = MAX_MONITORED_CASES_PER_USER;

// Get recent changes for dashboard
$recentChanges = getRecentChanges($userId, 10);

// Get notification statistics
$notificationStats = getNotificationStats($userId);

// Get available institutions for the add case form
$institutions = [
    'JudecatoriaBUCURESTI' => 'Judecătoria București',
    'TribunalulBUCURESTI' => 'Tribunalul București',
    'CurteadeApelBUCURESTI' => 'Curtea de Apel București',
    'JudecatoriaCLUJ' => 'Judecătoria Cluj',
    'TribunalulCLUJ' => 'Tribunalul Cluj',
    'CurteadeApelCLUJ' => 'Curtea de Apel Cluj',
    'JudecatoriaCONSTANTA' => 'Judecătoria Constanța',
    'TribunalulCONSTANTA' => 'Tribunalul Constanța',
    'CurteadeApelCONSTANTA' => 'Curtea de Apel Constanța',
    'JudecatoriaTIMISOARA' => 'Judecătoria Timișoara',
    'TribunalulTIMIS' => 'Tribunalul Timiș',
    'CurteadeApelTIMISOARA' => 'Curtea de Apel Timișoara',
    'JudecatoriaIASI' => 'Judecătoria Iași',
    'TribunalulIASI' => 'Tribunalul Iași',
    'CurteadeApelIASI' => 'Curtea de Apel Iași',
    'JudecatoriaBRASOV' => 'Judecătoria Brașov',
    'TribunalulBRASOV' => 'Tribunalul Brașov',
    'CurteadeApelBRASOV' => 'Curtea de Apel Brașov',
    'InaltaCurtedeCasatiesiJustitie' => 'Înalta Curte de Casație și Justiție'
];

// Prepare data for template
$data = [
    'user_name' => $userName,
    'monitored_cases' => $monitoredCases,
    'cases_count' => $casesCount,
    'max_cases' => $maxCases,
    'recent_changes' => $recentChanges,
    'notification_stats' => $notificationStats,
    'institutions' => $institutions,
    'csrf_token' => CSRFProtection::generateToken('monitor_dashboard'),
    'csrf_tokens' => [
        'add_case' => CSRFProtection::generateToken('add_case'),
        'remove_case' => CSRFProtection::generateToken('remove_case'),
        'update_frequency' => CSRFProtection::generateToken('update_frequency'),
        'check_case' => CSRFProtection::generateToken('check_case')
    ],
    'rate_limits' => [
        'add_case' => RateLimiter::checkLimit('add_case', "user_{$userId}"),
        'remove_case' => RateLimiter::checkLimit('remove_case', "user_{$userId}"),
        'update_frequency' => RateLimiter::checkLimit('update_frequency', "user_{$userId}")
    ],
    'gdpr_consents' => GDPRCompliance::getUserConsents($userId)
];

// Render template
echo $templateEngine->render('monitor/dashboard.twig', $data);

/**
 * Handle adding a case to monitoring
 */
function handleAddCase($monitoringService, $userId, $postData)
{
    $caseNumber = trim($postData['case_number'] ?? '');
    $institutionCode = trim($postData['institution_code'] ?? '');
    $institutionName = trim($postData['institution_name'] ?? '');
    $monitoringReason = trim($postData['monitoring_reason'] ?? '');
    $notificationFrequency = trim($postData['notification_frequency'] ?? 'daily');

    if (empty($caseNumber) || empty($institutionCode)) {
        throw new Exception('Numărul dosarului și instituția sunt obligatorii');
    }

    return $monitoringService->addCaseToMonitoring(
        $userId,
        $caseNumber,
        $institutionCode,
        $institutionName,
        '',
        $monitoringReason,
        $notificationFrequency
    );
}

/**
 * Handle updating notification frequency
 */
function handleUpdateFrequency($userId, $postData)
{
    $caseId = (int) ($postData['case_id'] ?? 0);
    $frequency = trim($postData['frequency'] ?? '');

    if (!$caseId || !in_array($frequency, ['immediate', 'daily', 'weekly'])) {
        throw new Exception('Parametri invalizi');
    }

    return Database::update('monitored_cases',
        ['notification_frequency' => $frequency],
        ['id' => $caseId, 'user_id' => $userId]
    );
}

/**
 * Handle checking case details
 */
function handleCheckCase($dosarService, $postData)
{
    $caseNumber = trim($postData['case_number'] ?? '');
    $institutionCode = trim($postData['institution_code'] ?? '');

    if (empty($caseNumber) || empty($institutionCode)) {
        throw new Exception('Numărul dosarului și instituția sunt obligatorii');
    }

    $caseData = $dosarService->getDetaliiDosar($caseNumber, $institutionCode);
    if (!$caseData) {
        throw new Exception('Dosarul nu a fost găsit în sistemul judiciar');
    }

    return $caseData;
}

/**
 * Get recent changes for user
 */
function getRecentChanges($userId, $limit = 10)
{
    try {
        return Database::fetchAll(
            "SELECT cc.*, mc.case_number, mc.institution_name
             FROM case_changes cc
             JOIN monitored_cases mc ON mc.id = cc.monitored_case_id
             WHERE mc.user_id = ? AND mc.is_active = 1
             ORDER BY cc.detected_at DESC
             LIMIT ?",
            [$userId, $limit]
        );
    } catch (Exception $e) {
        // Log error and return empty array if monitoring tables don't exist
        error_log("Failed to get recent changes: " . $e->getMessage());
        return [];
    }
}

/**
 * Get notification statistics for user
 */
function getNotificationStats($userId)
{
    try {
        $stats = Database::fetchOne(
            "SELECT
                COUNT(CASE WHEN nq.status = 'sent' THEN 1 END) as sent_count,
                COUNT(CASE WHEN nq.status = 'pending' THEN 1 END) as pending_count,
                COUNT(CASE WHEN nq.status = 'failed' THEN 1 END) as failed_count,
                COUNT(CASE WHEN cc.detected_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as changes_this_week
             FROM notification_queue nq
             LEFT JOIN case_changes cc ON cc.id = nq.case_change_id
             LEFT JOIN monitored_cases mc ON mc.id = nq.monitored_case_id
             WHERE nq.user_id = ?",
            [$userId]
        );
    } catch (Exception $e) {
        // Log error and return default stats if monitoring tables don't exist
        error_log("Failed to get notification stats: " . $e->getMessage());
        $stats = [
            'sent_count' => 0,
            'pending_count' => 0,
            'failed_count' => 0,
            'changes_this_week' => 0
        ];
    }

    return [
        'sent_notifications' => (int) ($stats['sent_count'] ?? 0),
        'pending_notifications' => (int) ($stats['pending_count'] ?? 0),
        'failed_notifications' => (int) ($stats['failed_count'] ?? 0),
        'changes_this_week' => (int) ($stats['changes_this_week'] ?? 0)
    ];
}

/**
 * Handle GDPR consent recording
 */
function handleGDPRConsent($userId, $consents)
{
    try {
        $success = true;
        $recordedConsents = [];

        foreach ($consents as $consentType => $granted) {
            if (GDPRCompliance::recordConsent($userId, $consentType, $granted)) {
                $recordedConsents[] = $consentType;
            } else {
                $success = false;
            }
        }

        if ($success) {
            return [
                'success' => true,
                'message' => 'Consimțământul a fost înregistrat cu succes',
                'recorded_consents' => $recordedConsents
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Eroare la înregistrarea unor consimțăminte'
            ];
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Eroare la înregistrarea consimțământului: ' . $e->getMessage()
        ];
    }
}
