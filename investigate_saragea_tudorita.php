<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 INVESTIGATING MISSING PARTY: SARAGEA TUDORIŢA\n";
echo "================================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n\n";
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    echo "📄 SOLUTIE SUMAR TEXT LENGTH: " . strlen($solutieSumarText) . " characters\n\n";
    
    // 1. TEXT VERIFICATION - Search for "SARAGEA TUDORIŢA"
    echo "🔍 STEP 1: TEXT VERIFICATION\n";
    echo "============================\n\n";
    
    $targetName = "SARAGEA TUDORIŢA";
    $targetNameLower = strtolower($targetName);
    
    // Exact match search
    $exactPos = strpos($solutieSumarText, $targetName);
    echo "Exact match search for '{$targetName}': ";
    if ($exactPos !== false) {
        echo "✅ FOUND at position {$exactPos}\n";
    } else {
        echo "❌ NOT FOUND\n";
    }
    
    // Case-insensitive search
    $caseInsensitivePos = stripos($solutieSumarText, $targetName);
    echo "Case-insensitive search for '{$targetName}': ";
    if ($caseInsensitivePos !== false) {
        echo "✅ FOUND at position {$caseInsensitivePos}\n";
    } else {
        echo "❌ NOT FOUND\n";
    }
    
    // Search for variations
    $variations = [
        'Saragea Tudoriţa',
        'SARAGEA TUDORITA',
        'Saragea Tudorita',
        'saragea tudoriţa',
        'saragea tudorita'
    ];
    
    echo "\n🔍 SEARCHING FOR VARIATIONS:\n";
    foreach ($variations as $variation) {
        $pos = stripos($solutieSumarText, $variation);
        echo "'{$variation}': ";
        if ($pos !== false) {
            echo "✅ FOUND at position {$pos}\n";
        } else {
            echo "❌ NOT FOUND\n";
        }
    }
    
    // 2. CONTEXT ANALYSIS - Find context around any found instances
    echo "\n🔍 STEP 2: CONTEXT ANALYSIS\n";
    echo "===========================\n\n";
    
    $foundPositions = [];
    foreach ([$targetName, 'Saragea Tudoriţa', 'SARAGEA TUDORITA', 'Saragea Tudorita'] as $searchTerm) {
        $pos = stripos($solutieSumarText, $searchTerm);
        if ($pos !== false) {
            $foundPositions[] = ['term' => $searchTerm, 'position' => $pos];
        }
    }
    
    if (!empty($foundPositions)) {
        foreach ($foundPositions as $found) {
            echo "Found '{$found['term']}' at position {$found['position']}\n";
            
            // Extract context (200 characters before and after)
            $start = max(0, $found['position'] - 200);
            $length = min(400, strlen($solutieSumarText) - $start);
            $context = substr($solutieSumarText, $start, $length);
            
            echo "Context:\n";
            echo "--------\n";
            echo $context . "\n\n";
        }
    } else {
        echo "❌ No instances found in any variation\n\n";
        
        // Search for partial matches
        echo "🔍 SEARCHING FOR PARTIAL MATCHES:\n";
        $partials = ['SARAGEA', 'TUDORIŢA', 'Saragea', 'Tudoriţa', 'TUDORITA', 'Tudorita'];
        
        foreach ($partials as $partial) {
            $pos = stripos($solutieSumarText, $partial);
            echo "'{$partial}': ";
            if ($pos !== false) {
                echo "✅ FOUND at position {$pos}\n";
                
                // Show context for partial matches
                $start = max(0, $pos - 100);
                $length = min(200, strlen($solutieSumarText) - $start);
                $context = substr($solutieSumarText, $start, $length);
                echo "  Context: " . trim($context) . "\n";
            } else {
                echo "❌ NOT FOUND\n";
            }
        }
    }
    
    // 3. DETAILED CONTEXT ANALYSIS FOR FOUND PARTIAL
    echo "\n🔍 STEP 3: DETAILED CONTEXT ANALYSIS\n";
    echo "====================================\n\n";

    // Since we found "Tudoriţa" at position 2142, let's get more context
    $tudoritaPos = stripos($solutieSumarText, 'Tudoriţa');
    if ($tudoritaPos !== false) {
        echo "Found 'Tudoriţa' at position {$tudoritaPos}\n";

        // Get larger context (500 characters before and after)
        $start = max(0, $tudoritaPos - 500);
        $length = min(1000, strlen($solutieSumarText) - $start);
        $largeContext = substr($solutieSumarText, $start, $length);

        echo "\nLarge Context (500 chars before/after):\n";
        echo "=======================================\n";
        echo $largeContext . "\n\n";

        // Look for all names in this context that contain "Tudoriţa"
        echo "🔍 NAMES CONTAINING 'Tudoriţa' IN CONTEXT:\n";
        $lines = explode(',', $largeContext);
        foreach ($lines as $line) {
            $line = trim($line);
            if (stripos($line, 'Tudoriţa') !== false) {
                echo "Found: '{$line}'\n";
            }
        }
    }

    // 4. SEARCH FOR SIMILAR SOUNDING NAMES
    echo "\n🔍 STEP 4: SEARCH FOR SIMILAR NAMES\n";
    echo "===================================\n\n";

    // Maybe the name is slightly different
    $similarNames = [
        'SARAGEA',
        'SARACEA',
        'SARAGIA',
        'SARAGA',
        'TUDORIŢA',
        'TUDORITA',
        'TUDORIȚA',
        'TUDORIŢĂ'
    ];

    foreach ($similarNames as $similar) {
        $pos = stripos($solutieSumarText, $similar);
        if ($pos !== false) {
            echo "Found '{$similar}' at position {$pos}\n";

            // Get context around this name
            $start = max(0, $pos - 100);
            $length = min(200, strlen($solutieSumarText) - $start);
            $context = substr($solutieSumarText, $start, $length);
            echo "  Context: " . trim($context) . "\n\n";
        }
    }
    
    echo "\n✅ Investigation complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
