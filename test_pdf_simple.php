<?php
/**
 * Simple PDF functionality test page
 * Tests the PDF save functionality with minimal setup
 */

// Test case data
$testCaseNumber = $_GET['numar'] ?? '123/2024';
$testInstitution = $_GET['institutie'] ?? 'TribunalulBUCURESTI';

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Simple - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
            padding: 2rem;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .test-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 6px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        .console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            margin: 1rem 0;
            white-space: pre-wrap;
        }
        .dosar-header {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        .dosar-header h3 {
            margin: 0;
            color: #2c3e50;
        }
        .dosar-header p {
            margin: 0.5rem 0;
        }
        @media print {
            body * {
                visibility: hidden;
            }
            #printVersion, #printVersion * {
                visibility: visible;
            }
            #printVersion {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-file-pdf mr-2"></i>Test PDF Simple</h1>
        <p>Test rapid pentru funcționalitatea "Salvează PDF"</p>
        
        <div class="dosar-header">
            <h3><?php echo htmlspecialchars($testCaseNumber); ?></h3>
            <p><strong>Instanță:</strong> <?php echo htmlspecialchars($testInstitution); ?></p>
            <p><strong>Data:</strong> <?php echo date('d.m.Y'); ?></p>
            <p><strong>Obiect:</strong> Test obiect pentru debugging PDF</p>
            <p><strong>Stadiu procesual:</strong> Test stadiu procesual</p>
        </div>
        
        <div class="mb-3">
            <button id="printBtnHeader" class="test-button">
                <i class="fas fa-file-pdf mr-2"></i>Salvează PDF (Test)
            </button>
            <button onclick="testConsole()" class="btn btn-info ml-2">
                <i class="fas fa-bug mr-2"></i>Test Console
            </button>
            <button onclick="clearConsole()" class="btn btn-secondary ml-2">
                <i class="fas fa-eraser mr-2"></i>Clear Console
            </button>
        </div>
        
        <div id="consoleOutput" class="console-output"></div>
        
        <div class="mt-4">
            <h5>Instrucțiuni de testare:</h5>
            <ol>
                <li>Deschideți Developer Tools (F12) și mergeți la tab-ul Console</li>
                <li>Faceți click pe butonul "Salvează PDF (Test)"</li>
                <li>Verificați mesajele din consolă (atât în Developer Tools cât și în zona de mai sus)</li>
                <li>Verificați că se deschide dialogul de printare al browserului</li>
                <li>În dialogul de printare, selectați "Save as PDF" ca destinație</li>
                <li>Verificați că PDF-ul se generează și se descarcă corect</li>
            </ol>
        </div>
        
        <div class="mt-4">
            <h5>Link-uri de test:</h5>
            <div class="mb-2">
                <a href="detalii_dosar.php?numar=<?php echo urlencode($testCaseNumber); ?>&institutie=<?php echo urlencode($testInstitution); ?>" 
                   target="_blank" class="btn btn-primary">
                    <i class="fas fa-external-link-alt mr-2"></i>Detalii Dosar Real
                </a>
                <a href="debug_pdf_functionality.php?numar=<?php echo urlencode($testCaseNumber); ?>&institutie=<?php echo urlencode($testInstitution); ?>" 
                   target="_blank" class="btn btn-warning ml-2">
                    <i class="fas fa-bug mr-2"></i>Debug Complet
                </a>
            </div>
        </div>
    </div>

    <!-- Container pentru notificări -->
    <div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999; display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>

    <!-- Versiune pentru printare (inițial ascunsă) -->
    <div id="printVersion" class="print-only" style="display: none;">
        <div class="print-header">
            <h1>Test PDF Document: <?php echo htmlspecialchars($testCaseNumber); ?></h1>
            <p>Generat la data: <?php echo date('d.m.Y H:i'); ?></p>
        </div>
        <div id="printContent"></div>
    </div>

    <script>
        // Console logging override for debugging
        let consoleMessages = [];
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function logToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            consoleMessages.push(logMessage);
            updateConsoleOutput();
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToConsole('warn', args.join(' '));
        };
        
        function updateConsoleOutput() {
            const output = document.getElementById('consoleOutput');
            if (consoleMessages.length > 0) {
                output.textContent = consoleMessages.slice(-50).join('\n'); // Show last 50 messages
                output.scrollTop = output.scrollHeight;
            }
        }
        
        function clearConsole() {
            consoleMessages = [];
            updateConsoleOutput();
        }
        
        function testConsole() {
            console.log('Test console message');
            console.warn('Test warning message');
            console.error('Test error message');
        }

        // Variables for PDF functionality
        let isPrinting = false;
        let originalPageTitle = '';

        // Notification function
        function showNotification(message, type) {
            console.log(`NOTIFICARE [${type}]: ${message}`);
            
            const notificationContainer = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');

            if (notificationContainer && notification) {
                // Determină iconița și clasa CSS pe baza tipului
                let icon, alertClass;
                switch(type) {
                    case 'success':
                        icon = 'check-circle';
                        alertClass = 'alert-success';
                        break;
                    case 'danger':
                        icon = 'exclamation-triangle';
                        alertClass = 'alert-danger';
                        break;
                    case 'warning':
                        icon = 'exclamation-circle';
                        alertClass = 'alert-warning';
                        break;
                    case 'info':
                    default:
                        icon = 'info-circle';
                        alertClass = 'alert-info';
                        break;
                }

                // Setează conținutul și stilul notificării
                notification.className = 'alert ' + alertClass;
                notification.innerHTML = '<i class="fas fa-' + icon + ' mr-2"></i>' + message;

                // Afișează containerul de notificări
                notificationContainer.style.display = 'block';

                // Ascunde notificarea după 5 secunde
                setTimeout(function() {
                    notificationContainer.style.display = 'none';
                }, 5000);
            }
        }

        // Mock getDosarInfo function for testing
        function getDosarInfo() {
            console.log('Executare getDosarInfo() mock...');
            return {
                numar: '<?php echo htmlspecialchars($testCaseNumber); ?>',
                instanta: '<?php echo htmlspecialchars($testInstitution); ?>',
                data: '<?php echo date('d.m.Y'); ?>',
                obiect: 'Test obiect pentru debugging PDF',
                stadiuProcesual: 'Test stadiu procesual',
                categorieCaz: 'Test categorie',
                dataModificare: '<?php echo date('d.m.Y H:i'); ?>',
                parti: [
                    { nume: 'Test Parte 1', calitate: 'Reclamant' },
                    { nume: 'Test Parte 2', calitate: 'Pârât' }
                ],
                sedinte: [
                    { 
                        data: '<?php echo date('d.m.Y'); ?>', 
                        ora: '10:00', 
                        complet: 'Test Complet', 
                        solutie: 'Test soluție pentru ședința de judecată',
                        dataPronuntare: '<?php echo date('d.m.Y'); ?>',
                        document: 'Test document'
                    }
                ],
                caiAtac: [
                    {
                        dataDeclarare: '<?php echo date('d.m.Y'); ?>',
                        parteDeclaratoare: 'Test Parte Declaratoare',
                        tipCaleAtac: 'Apel',
                        numarDosarInstantaSuperior: 'TEST/2024'
                    }
                ]
            };
        }

        // PDF filename generation function
        function generatePdfFilename(caseNumber) {
            if (!caseNumber || caseNumber.trim() === '') {
                return 'Dosar_nedisponibil.pdf';
            }
            
            // Curățăm numărul dosarului pentru numele de fișier
            const cleanCaseNumber = caseNumber.replace(/[\/\\:*?"<>|]/g, '_');
            return `Dosar nr${cleanCaseNumber}.pdf`;
        }

        // Main printDosar function (simplified for testing)
        function printDosar() {
            console.log('=== ÎNCEPE FUNCȚIA printDosar() ===');
            
            // Verificăm dacă o operație de printare este deja în curs
            if (isPrinting) {
                console.log('Operație de printare deja în curs');
                showNotification('O operație de printare este deja în curs. Vă rugăm să așteptați.', 'warning');
                return;
            }

            // Marcăm că o operație de printare este în curs
            isPrinting = true;
            console.log('Operație de printare marcată ca în curs');

            // Afișăm notificare de informare
            console.log('Afișare notificare de informare...');
            showNotification('Se pregătește documentul pentru printare...', 'info');

            // Pregătim conținutul pentru printare
            console.log('Căutare elemente HTML necesare...');
            const printContent = document.getElementById('printContent');
            const printVersion = document.getElementById('printVersion');
            
            console.log('Element #printContent:', printContent ? '✓ Găsit' : '✗ Nu a fost găsit');
            console.log('Element #printVersion:', printVersion ? '✓ Găsit' : '✗ Nu a fost găsit');

            // Verificăm dacă elementele necesare există
            if (!printContent) {
                console.error('✗ Elementul #printContent nu a fost găsit!');
                showNotification('Eroare: Nu s-a putut pregăti documentul pentru printare. Elementul printContent lipsește.', 'danger');
                isPrinting = false;
                return;
            }

            if (!printVersion) {
                console.error('✗ Elementul #printVersion nu a fost găsit!');
                showNotification('Eroare: Nu s-a putut pregăti documentul pentru printare. Elementul printVersion lipsește.', 'danger');
                isPrinting = false;
                return;
            }
            
            console.log('✓ Toate elementele HTML necesare au fost găsite');

            // Curățăm conținutul anterior
            printContent.innerHTML = '';
            console.log('✓ Conținut anterior curățat');

            // Obținem informațiile dosarului
            console.log('Extragere informații dosar...');
            let dosarInfo;
            try {
                dosarInfo = getDosarInfo();
                console.log('✓ Funcția getDosarInfo() executată cu succes');
                console.log('Date dosar extrase:', {
                    numar: dosarInfo?.numar,
                    parti: dosarInfo?.parti?.length || 0,
                    sedinte: dosarInfo?.sedinte?.length || 0,
                    caiAtac: dosarInfo?.caiAtac?.length || 0
                });
            } catch (error) {
                console.error('✗ Eroare la executarea getDosarInfo():', error);
                showNotification('Eroare: Nu s-au putut extrage informațiile dosarului pentru printare.', 'danger');
                isPrinting = false;
                return;
            }

            // Verificăm dacă avem date valide
            if (!dosarInfo || !dosarInfo.numar) {
                console.error('✗ Nu s-au putut extrage informațiile dosarului sau numărul dosarului lipsește!');
                showNotification('Eroare: Nu s-au putut extrage informațiile dosarului pentru printare.', 'danger');
                isPrinting = false;
                return;
            }
            
            console.log('✓ Informații dosar valide găsite');

            // Construim conținutul HTML pentru printare
            console.log('Construire conținut HTML pentru PDF...');
            let html = `
                <div class="dosar-info">
                    <h2>Informații generale</h2>
                    <p><strong>Număr dosar:</strong> ${dosarInfo.numar}</p>
                    <p><strong>Instanță:</strong> ${dosarInfo.instanta}</p>
                    <p><strong>Data:</strong> ${dosarInfo.data || 'Nedisponibilă'}</p>
                    <p><strong>Obiect:</strong> ${dosarInfo.obiect || 'Nedisponibil'}</p>
                    <p><strong>Stadiu procesual:</strong> ${dosarInfo.stadiuProcesual || 'Nedisponibil'}</p>
                </div>
            `;

            // Adăugăm părțile implicate
            if (dosarInfo.parti && dosarInfo.parti.length > 0) {
                html += `
                    <div class="parti-implicate">
                        <h2>Părți implicate</h2>
                        <table border="1" cellpadding="3" cellspacing="0" width="100%">
                            <thead>
                                <tr>
                                    <th>Nume</th>
                                    <th>Calitate</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                dosarInfo.parti.forEach(parte => {
                    html += `
                        <tr>
                            <td>${parte.nume || '-'}</td>
                            <td>${parte.calitate || '-'}</td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            // Adăugăm ședințele
            if (dosarInfo.sedinte && dosarInfo.sedinte.length > 0) {
                html += `
                    <div class="sedinte">
                        <h2>Ședințe de judecată</h2>
                        <table border="1" cellpadding="3" cellspacing="0" width="100%">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Ora</th>
                                    <th>Complet</th>
                                    <th>Soluție</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                dosarInfo.sedinte.forEach(sedinta => {
                    html += `
                        <tr>
                            <td>${sedinta.data || '-'}</td>
                            <td>${sedinta.ora || '-'}</td>
                            <td>${sedinta.complet || '-'}</td>
                            <td>${sedinta.solutie || '-'}</td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            // Actualizăm conținutul pentru printare
            printContent.innerHTML = html;
            console.log('✓ Conținut HTML generat și adăugat');

            // Setăm titlul documentului pentru numele de fișier PDF
            try {
                originalPageTitle = document.title;
                const pdfFilename = generatePdfFilename(dosarInfo.numar);
                document.title = pdfFilename;
                console.log(`✓ Titlul documentului setat la: "${pdfFilename}"`);
            } catch (error) {
                console.error('✗ Eroare la setarea titlului documentului:', error);
            }

            // Executăm printarea
            console.log('Pregătire pentru deschiderea dialogului de printare...');
            setTimeout(function() {
                console.log('Executare window.print()...');
                try {
                    window.print();
                    console.log('✓ window.print() executat cu succes');
                } catch (error) {
                    console.error('✗ Eroare la executarea window.print():', error);
                    showNotification('Eroare: Nu s-a putut deschide dialogul de printare.', 'danger');
                    isPrinting = false;
                    return;
                }

                // Finalizare
                setTimeout(function() {
                    console.log('Finalizare operație printare...');
                    
                    // Restaurăm titlul original
                    if (originalPageTitle) {
                        document.title = originalPageTitle;
                        console.log('✓ Titlul original restaurat');
                    }

                    showNotification('Documentul PDF este gata pentru salvare.', 'success');
                    isPrinting = false;
                    console.log('✓ Operație printare finalizată cu succes');
                    console.log('=== SFÂRȘIT FUNCȚIA printDosar() ===');
                }, 500);
            }, 200);
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded successfully');
            
            // Add event listener to PDF button
            const pdfButton = document.getElementById('printBtnHeader');
            if (pdfButton) {
                console.log('✓ PDF button found, adding event listener...');
                pdfButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('PDF button clicked');
                    printDosar();
                });
            } else {
                console.error('✗ PDF button not found!');
            }
        });
    </script>
</body>
</html>
