<?php
/**
 * Test page for session search to case details navigation
 * This page tests the complete navigation flow from session search to case details
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigare Ședințe → Detalii Dosar - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px 8px 0 0;
        }
        .test-body {
            padding: 1.5rem;
        }
        .test-step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .test-step-header {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        .test-step-content {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            text-decoration: none;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info-message {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning-message {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-route mr-2"></i>
                    Test Navigare: Ședințe → Detalii Dosar
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    Testare completă a fluxului de navigare de la căutarea ședințelor la detaliile dosarelor
                </p>
            </div>
            <div class="test-body">
                <div class="info-message">
                    <strong>Scopul testului:</strong><br>
                    Acest test verifică dacă navigarea de la rezultatele căutării ședințelor către pagina de detalii a dosarelor funcționează corect.
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-play-circle mr-2"></i>
                        Pasul 1: Accesează pagina de căutare ședințe
                    </div>
                    <div class="test-step-content">
                        Începe testul accesând pagina de căutare a ședințelor și efectuează o căutare.
                        <br><br>
                        <a href="sedinte.php" class="test-link">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Accesează Căutare Ședințe
                        </a>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-search mr-2"></i>
                        Pasul 2: Efectuează o căutare de ședințe
                    </div>
                    <div class="test-step-content">
                        <strong>Instrucțiuni pentru căutare:</strong>
                        <ul>
                            <li>Introdu data de azi în format ZZ.LL.AAAA (ex: <?php echo date('d.m.Y'); ?>)</li>
                            <li>Opțional: selectează o instituție specifică</li>
                            <li>Apasă butonul "Caută Ședințe"</li>
                        </ul>
                        <div class="warning-message">
                            <strong>Notă:</strong> Dacă nu găsești ședințe pentru data de azi, încearcă cu alte date recente sau cu "Toate instituțiile".
                        </div>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-mouse-pointer mr-2"></i>
                        Pasul 3: Testează link-urile către dosare
                    </div>
                    <div class="test-step-content">
                        <strong>Ce să testezi:</strong>
                        <ul>
                            <li>Identifică ședințele care au dosare programate</li>
                            <li>Fă click pe numerele dosarelor (link-urile albastre)</li>
                            <li>Verifică că te redirecționează către pagina de detalii</li>
                            <li>Confirmă că detaliile dosarului se încarcă corect</li>
                        </ul>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-check-circle mr-2"></i>
                        Pasul 4: Verifică funcționalitatea completă
                    </div>
                    <div class="test-step-content">
                        <strong>Verificări necesare:</strong>
                        <ul>
                            <li>Link-urile se deschid în aceeași fereastră (nu în tab nou)</li>
                            <li>Parametrii URL sunt corecți (numar și institutie)</li>
                            <li>Pagina de detalii afișează informațiile dosarului</li>
                            <li>Nu apar erori JavaScript în consolă</li>
                            <li>Navigarea funcționează pe mobile și desktop</li>
                        </ul>
                    </div>
                </div>

                <div class="success-message">
                    <strong>✓ Testare completă!</strong><br>
                    Dacă toate pașii de mai sus funcționează corect, navigarea de la ședințe la detalii dosar este operațională.
                </div>

                <h4 class="mt-4 mb-3">
                    <i class="fas fa-tools mr-2"></i>
                    Instrumente de Debugging
                </h4>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-bug mr-2"></i>
                        Verificare URL-uri
                    </div>
                    <div class="test-step-content">
                        Exemplu de URL corect pentru detalii dosar:
                        <div class="code-block">
                            http://localhost/just/detalii_dosar.php?numar=123/2024&institutie=TribunalulBUCURESTI
                        </div>
                        <strong>Parametrii obligatorii:</strong>
                        <ul>
                            <li><code>numar</code> - Numărul dosarului (URL encoded)</li>
                            <li><code>institutie</code> - Codul instituției (poate fi gol pentru căutare în toate instituțiile)</li>
                        </ul>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-code mr-2"></i>
                        Verificare JavaScript
                    </div>
                    <div class="test-step-content">
                        Pentru a verifica erorile JavaScript:
                        <ol>
                            <li>Deschide Developer Tools (F12)</li>
                            <li>Mergi la tab-ul "Console"</li>
                            <li>Efectuează căutarea și fă click pe link-urile dosarelor</li>
                            <li>Verifică că nu apar erori în consolă</li>
                        </ol>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-mobile-alt mr-2"></i>
                        Test Responsivitate
                    </div>
                    <div class="test-step-content">
                        Pentru a testa pe mobile:
                        <ol>
                            <li>Deschide Developer Tools (F12)</li>
                            <li>Activează "Device Toolbar" (Ctrl+Shift+M)</li>
                            <li>Selectează un dispozitiv mobil</li>
                            <li>Testează navigarea pe dimensiuni mobile</li>
                        </ol>
                    </div>
                </div>

                <h4 class="mt-4 mb-3">
                    <i class="fas fa-link mr-2"></i>
                    Link-uri Rapide pentru Test
                </h4>

                <div class="row">
                    <div class="col-md-6">
                        <a href="sedinte.php" class="test-link">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Căutare Ședințe
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="test_sessions.php" class="test-link">
                            <i class="fas fa-vial mr-2"></i>
                            Test SOAP API Ședințe
                        </a>
                    </div>
                </div>

                <div class="info-message mt-4">
                    <strong>Informații tehnice:</strong><br>
                    • Modificările implementate includ gestionarea parametrului instituție opțional<br>
                    • Adăugată funcționalitate de căutare în toate instituțiile când instituția nu este specificată<br>
                    • Îmbunătățită gestionarea erorilor și feedback-ul pentru utilizator<br>
                    • Adăugate atribute data pentru debugging și tracking
                </div>
            </div>
        </div>
    </div>
</body>
</html>
