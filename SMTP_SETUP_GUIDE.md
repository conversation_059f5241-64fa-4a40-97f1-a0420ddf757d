# SMTP Configuration Guide - Portal Judiciar

## 🚨 **Current Issue**
The contact form is failing with "SMTP Error: Could not authenticate." because the SMTP credentials are still set to placeholder values.

## 📧 **Gmail SMTP Setup (Recommended)**

### Step 1: Enable 2-Factor Authentication
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google", click "2-Step Verification"
4. Follow the setup process to enable 2FA

### Step 2: Generate App Password
1. After enabling 2FA, go back to "Security"
2. Under "Signing in to Google", click "App passwords"
3. Select "Mail" as the app and "Windows Computer" as the device
4. Click "Generate"
5. **Copy the 16-character password** (e.g., `abcd efgh ijkl mnop`)

### Step 3: Update Configuration
Edit `src/Config/constants.php` and replace these lines:

```php
// BEFORE (current placeholder values)
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');

// AFTER (your actual credentials)
define('SMTP_USERNAME', '<EMAIL>');  // Your Gmail address
define('SMTP_PASSWORD', 'abcdefghijklmnop');     // The 16-char app password (no spaces)
```

## 🔧 **Alternative SMTP Providers**

### Option 1: Outlook/Hotmail
```php
define('SMTP_HOST', 'smtp-mail.outlook.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-password');
```

### Option 2: Yahoo Mail
```php
define('SMTP_HOST', 'smtp.mail.yahoo.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password'); // Generate in Yahoo security settings
```

### Option 3: Custom SMTP Server
```php
define('SMTP_HOST', 'mail.yourdomain.com');
define('SMTP_PORT', 587); // or 465 for SSL
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-password');
```

## 🛠 **Debugging Steps**

### 1. Run Debug Page
Visit: `http://localhost/just/debug_contact.php`

This will show you:
- ✅ Current SMTP configuration
- ✅ PHP extensions status
- ✅ SMTP connection test
- ✅ Detailed error messages

### 2. Check Error Logs
View: `logs/contact_errors.log`
```bash
# Recent errors will show specific SMTP issues
tail -f logs/contact_errors.log
```

### 3. Test SMTP Connection
The debug page will attempt to connect to your SMTP server and show detailed connection information.

## 🔍 **Common Issues & Solutions**

### Issue 1: "Could not authenticate"
**Cause**: Wrong username/password or app password not generated
**Solution**: 
- Verify email address is correct
- For Gmail, use app password (not account password)
- For other providers, check if app passwords are required

### Issue 2: "Connection timed out"
**Cause**: Firewall blocking SMTP port or wrong server
**Solution**:
- Check SMTP_HOST is correct
- Verify SMTP_PORT (usually 587 for STARTTLS, 465 for SSL)
- Check if your hosting provider blocks SMTP ports

### Issue 3: "SSL/TLS connection failed"
**Cause**: OpenSSL extension missing or SSL certificate issues
**Solution**:
- Ensure OpenSSL extension is enabled in PHP
- Try different SMTP ports (587 vs 465)
- Check if server supports STARTTLS

### Issue 4: "Relay access denied"
**Cause**: SMTP server doesn't allow relaying from your IP
**Solution**:
- Use authenticated SMTP (SMTPAuth = true)
- Check if your IP is whitelisted on the SMTP server
- Use your hosting provider's SMTP server

## 📝 **Quick Test Commands**

### Test 1: Check PHP Extensions
```php
<?php
echo "OpenSSL: " . (extension_loaded('openssl') ? 'OK' : 'Missing') . "\n";
echo "MBString: " . (extension_loaded('mbstring') ? 'OK' : 'Missing') . "\n";
?>
```

### Test 2: Test SMTP Connection (Manual)
```php
<?php
require_once 'vendor/autoload.php';
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

$mail = new PHPMailer(true);
$mail->SMTPDebug = SMTP::DEBUG_CONNECTION;
$mail->isSMTP();
$mail->Host = 'smtp.gmail.com';
$mail->SMTPAuth = true;
$mail->Username = '<EMAIL>';
$mail->Password = 'your-app-password';
$mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
$mail->Port = 587;

try {
    $mail->smtpConnect();
    echo "SMTP connection successful!";
    $mail->smtpClose();
} catch (Exception $e) {
    echo "SMTP connection failed: " . $e->getMessage();
}
?>
```

## 🎯 **Recommended Configuration for Production**

```php
// src/Config/constants.php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // Dedicated email for the portal
define('SMTP_PASSWORD', 'generated-app-password');    // 16-character app password
define('CONTACT_EMAIL', '<EMAIL>');     // Where messages are sent
define('CONTACT_NAME', 'Portal Judiciar');
```

## 🔐 **Security Best Practices**

1. **Use App Passwords**: Never use your main account password
2. **Dedicated Email**: Create a separate email account for the portal
3. **Environment Variables**: Consider moving credentials to .env file
4. **Rate Limiting**: Keep the 5 messages/hour limit to prevent spam
5. **Log Monitoring**: Regularly check error logs for suspicious activity

## 📞 **Next Steps**

1. **Configure SMTP credentials** using the Gmail setup above
2. **Run the debug page** to verify configuration
3. **Test the contact form** with a real message
4. **Check email delivery** to <EMAIL>
5. **Monitor logs** for any ongoing issues

## 🆘 **If Still Having Issues**

1. Check the debug page output
2. Review the error logs
3. Try a different SMTP provider
4. Contact your hosting provider about SMTP restrictions
5. Consider using a transactional email service (SendGrid, Mailgun, etc.)

---

**Remember**: After updating the SMTP credentials, the contact form should work immediately. The debug page will help you verify each step of the configuration.
