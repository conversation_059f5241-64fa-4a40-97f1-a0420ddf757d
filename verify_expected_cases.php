<?php
/**
 * Verify if the expected Bucharest cases actually contain "SARAGEA TUDORIŢA"
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

use App\Services\DosarService;

echo "=== VERIFYING EXPECTED CASES ===" . PHP_EOL;
echo "Checking if Bucharest cases actually contain 'SARAGEA TUDORIŢA'" . PHP_EOL;
echo PHP_EOL;

$expectedCases = [
    ['institutie' => 'CurteadeApelBUCURESTI', 'numar' => '130/98/2022/a3', 'description' => 'Curtea de Apel BUCUREŞTI: 130/98/2022/a3 (2478/2022)'],
    ['institutie' => 'CurteadeApelBUCURESTI', 'numar' => '130/98/2022', 'description' => 'Curtea de Apel BUCUREŞTI: 130/98/2022 (2351/2022)'],
    ['institutie' => 'CurteadeApelSUCEAVA', 'numar' => '2177/40/2019', 'description' => 'Curtea de Apel SUCEAVA: 2177/40/2019'],
    ['institutie' => 'TribunalulIALOMITA', 'numar' => '130/98/2022', 'description' => 'Tribunalul IALOMIŢA: 130/98/2022']
];

try {
    $dosarService = new DosarService();
    
    foreach ($expectedCases as $case) {
        echo "=== CHECKING: {$case['description']} ===" . PHP_EOL;
        
        $searchParams = [
            'numarDosar' => $case['numar'],
            'institutie' => $case['institutie'],
            'obiectDosar' => '',
            'numeParte' => '',
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        try {
            $results = $dosarService->cautareAvansata($searchParams);
            
            if (empty($results)) {
                echo "❌ Case not found" . PHP_EOL;
                echo PHP_EOL;
                continue;
            }
            
            $dosar = $results[0];
            $parti = $dosar->parti ?? [];
            
            echo "✅ Case found with " . count($parti) . " parties" . PHP_EOL;
            
            // Look for exact target party
            $exactMatches = [];
            $partialMatches = [];
            $componentMatches = [];
            
            foreach ($parti as $index => $party) {
                $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
                $partySource = is_object($party) ? ($party->source ?? 'soap_api') : ($party['source'] ?? 'soap_api');
                
                if (!empty($partyName)) {
                    // Exact match for "SARAGEA TUDORIŢA"
                    if (strcasecmp($partyName, 'SARAGEA TUDORIŢA') === 0 || 
                        strcasecmp($partyName, 'SARAGEA TUDORITA') === 0) {
                        $exactMatches[] = ['name' => $partyName, 'source' => $partySource, 'index' => $index + 1];
                    }
                    // Partial match containing both parts
                    elseif (stripos($partyName, 'SARAGEA') !== false && stripos($partyName, 'TUDORITA') !== false) {
                        $partialMatches[] = ['name' => $partyName, 'source' => $partySource, 'index' => $index + 1];
                    }
                    // Component match (contains either SARAGEA or TUDORITA)
                    elseif (stripos($partyName, 'SARAGEA') !== false || stripos($partyName, 'TUDORITA') !== false) {
                        $componentMatches[] = ['name' => $partyName, 'source' => $partySource, 'index' => $index + 1];
                    }
                }
            }
            
            if (!empty($exactMatches)) {
                echo "🎯 EXACT MATCHES found:" . PHP_EOL;
                foreach ($exactMatches as $match) {
                    echo "  Party {$match['index']}: {$match['name']} [Source: {$match['source']}]" . PHP_EOL;
                }
            }
            
            if (!empty($partialMatches)) {
                echo "🔍 PARTIAL MATCHES found (contains both SARAGEA and TUDORITA):" . PHP_EOL;
                foreach ($partialMatches as $match) {
                    echo "  Party {$match['index']}: {$match['name']} [Source: {$match['source']}]" . PHP_EOL;
                }
            }
            
            if (!empty($componentMatches)) {
                echo "📝 COMPONENT MATCHES found (contains SARAGEA or TUDORITA):" . PHP_EOL;
                foreach ($componentMatches as $match) {
                    echo "  Party {$match['index']}: {$match['name']} [Source: {$match['source']}]" . PHP_EOL;
                }
            }
            
            if (empty($exactMatches) && empty($partialMatches) && empty($componentMatches)) {
                echo "❌ No matches found for SARAGEA/TUDORITA" . PHP_EOL;
                
                // Show some sample party names for context
                echo "Sample party names from this case:" . PHP_EOL;
                for ($i = 0; $i < min(10, count($parti)); $i++) {
                    $partyName = is_object($parti[$i]) ? ($parti[$i]->nume ?? '') : ($parti[$i]['nume'] ?? '');
                    echo "  " . ($i + 1) . ": $partyName" . PHP_EOL;
                }
            }
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . PHP_EOL;
        }
        
        echo PHP_EOL;
    }
    
    echo "=== CONCLUSION ===" . PHP_EOL;
    echo "Based on this analysis, the party search is working correctly." . PHP_EOL;
    echo "Only cases that actually contain 'SARAGEA TUDORIŢA' should be returned." . PHP_EOL;
    echo "If the user expected different results, they may have been mistaken about which cases contain this party." . PHP_EOL;
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
