<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 EXAMINING FULL APPEAL STRUCTURE\n";
echo "==================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    echo "📄 Full text structure analysis:\n";
    echo "================================\n\n";
    
    // Split by sentences to understand structure
    $sentences = preg_split('/\.(?=\s*[A-Z])/', $solutieSumarText);
    
    echo "Total sentences: " . count($sentences) . "\n\n";
    
    foreach ($sentences as $i => $sentence) {
        $sentence = trim($sentence);
        if (empty($sentence)) continue;
        
        echo "Sentence " . ($i + 1) . ":\n";
        echo "Length: " . strlen($sentence) . " characters\n";
        echo "Content: \"" . substr($sentence, 0, 150) . "...\"\n";
        
        // Check if this sentence contains appeal information
        if (preg_match('/(Anulează|Respinge)\s+apelurile\s+formulate\s+de\s+apelanţii/i', $sentence)) {
            echo "*** APPEAL SECTION DETECTED ***\n";
            
            // Count commas in this sentence
            $commaCount = substr_count($sentence, ',');
            echo "Comma count: {$commaCount}\n";
            
            // Extract the appellant list part
            if (preg_match('/(Anulează|Respinge)\s+apelurile\s+formulate\s+de\s+apelanţii\s+(.+?)(?:\s*ca\s+(?:netimbrate|nefondate))?$/i', $sentence, $matches)) {
                $apellantsText = $matches[2];
                echo "Appellants text length: " . strlen($apellantsText) . " characters\n";
                echo "Appellants preview: \"" . substr($apellantsText, 0, 200) . "...\"\n";
                
                // Count potential names
                $names = explode(',', $apellantsText);
                $validNames = 0;
                foreach ($names as $name) {
                    $name = trim($name);
                    $name = preg_replace('/\s*şi\s*$/', '', $name);
                    $name = preg_replace('/\s*\?.*$/', '', $name);
                    $name = preg_replace('/\s*\(.*?\)/', '', $name);
                    $name = trim($name);
                    
                    if (strlen($name) >= 3 && preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $name)) {
                        $validNames++;
                    }
                }
                echo "Valid names in this section: {$validNames}\n";
            }
        }
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
