<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 FINDING THE MISSING 8 NAMES\n";
echo "===============================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current total parties: " . count($dosar->parti) . "\n\n";
    
    // Get current decision text parties
    $currentDecisionParties = [];
    foreach ($dosar->parti as $party) {
        $partyArray = (array) $party;
        if (isset($partyArray['source']) && $partyArray['source'] === 'decision_text') {
            $currentDecisionParties[] = strtolower(trim($partyArray['nume']));
        }
    }
    
    echo "Current decision text parties: " . count($currentDecisionParties) . "\n\n";
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    // Extract all potential names using the same logic as the analysis
    $allPotentialNames = [];
    
    // Pattern 7: Enhanced comma separation
    $commaNames = explode(',', $solutieSumarText);
    foreach ($commaNames as $name) {
        $originalName = $name;
        $name = trim($name);
        $name = preg_replace('/.*(?:apelanţii|apelan\?ii)\s+/', '', $name);
        $name = preg_replace('/\s*ca\s+(?:netimbrate|nefondate).*$/', '', $name);
        $name = preg_replace('/\s*\(.*?\)/', '', $name);
        $name = preg_replace('/\s*şi\s*$/', '', $name);
        $name = preg_replace('/\..*$/', '', $name);
        $name = trim($name);
        
        if (strlen($name) >= 3 && preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $name)) {
            $allPotentialNames[] = [
                'name' => $name,
                'original' => trim($originalName),
                'pattern' => 'Pattern 7'
            ];
        }
    }
    
    // Pattern 8: Appeal patterns
    // Anulează
    if (preg_match('/Anulează\s+apelurile\s+formulate\s+de\s+apelanţii\s+([^.]+?)(?:\s*ca\s+(?:netimbrate|nefondate))?\./', $solutieSumarText, $anuleazaMatch)) {
        $apellantsText = $anuleazaMatch[1];
        $apellantNames = explode(',', $apellantsText);
        foreach ($apellantNames as $name) {
            $originalName = $name;
            $name = trim($name);
            $name = preg_replace('/\s*şi\s*$/', '', $name);
            $name = preg_replace('/\s*\?.*$/', '', $name);
            $name = trim($name);
            if (strlen($name) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $name)) {
                $allPotentialNames[] = [
                    'name' => $name,
                    'original' => trim($originalName),
                    'pattern' => 'Pattern 8 - Anulează'
                ];
            }
        }
    }
    
    // Respinge
    if (preg_match('/Respinge\s+apelurile\s+formulate\s+de\s+apelan[ţ?]ii\s+(.+?)(?:\s*ca\s+(?:netimbrate|nefondate))?(?:\.\s*Admite|$)/s', $solutieSumarText, $respingeMatch)) {
        $apellantsText = $respingeMatch[1];
        $apellantsText = preg_replace('/\.\s*[A-Z][^,]*(?:Georgeta|Elisabeta|Marioara|Anişoara|Florica|Steliana|Florenţa|Sorin)[^,]*/', '', $apellantsText);
        $apellantNames = explode(',', $apellantsText);
        foreach ($apellantNames as $name) {
            $originalName = $name;
            $name = trim($name);
            $name = preg_replace('/\s*şi\s*$/', '', $name);
            $name = preg_replace('/\s*\?.*$/', '', $name);
            $name = trim($name);
            if (strlen($name) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $name)) {
                $allPotentialNames[] = [
                    'name' => $name,
                    'original' => trim($originalName),
                    'pattern' => 'Pattern 8 - Respinge'
                ];
            }
        }
    }
    
    // Other patterns (Pattern 2, 4)
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s*,\s*(?:în calitate de|ca|fiind)/i', $solutieSumarText, $matches)) {
        foreach ($matches[1] as $name) {
            $allPotentialNames[] = [
                'name' => trim($name),
                'original' => trim($name),
                'pattern' => 'Pattern 2'
            ];
        }
    }
    
    if (preg_match_all('/(?:reclamant|pârât|petent|intimat)(?:ul|a)?\s*:?\s*([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)/i', $solutieSumarText, $matches)) {
        foreach ($matches[1] as $name) {
            $allPotentialNames[] = [
                'name' => trim($name),
                'original' => trim($name),
                'pattern' => 'Pattern 4'
            ];
        }
    }
    
    // Remove duplicates and normalize
    $uniquePotentialNames = [];
    $seenNames = [];
    
    foreach ($allPotentialNames as $nameData) {
        $normalizedName = strtolower(trim($nameData['name']));
        $normalizedName = str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], $normalizedName);
        
        if (!isset($seenNames[$normalizedName])) {
            $seenNames[$normalizedName] = true;
            $uniquePotentialNames[] = $nameData;
        }
    }
    
    echo "Total potential names found: " . count($allPotentialNames) . "\n";
    echo "Unique potential names: " . count($uniquePotentialNames) . "\n\n";
    
    // Find missing names
    $missingNames = [];
    foreach ($uniquePotentialNames as $nameData) {
        $normalizedName = strtolower(trim($nameData['name']));
        $normalizedName = str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], $normalizedName);
        
        $found = false;
        foreach ($currentDecisionParties as $currentName) {
            $normalizedCurrent = str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], $currentName);
            if ($normalizedCurrent === $normalizedName) {
                $found = true;
                break;
            }
        }
        
        if (!$found) {
            $missingNames[] = $nameData;
        }
    }
    
    echo "🔍 MISSING NAMES ANALYSIS:\n";
    echo "==========================\n\n";
    echo "Missing names count: " . count($missingNames) . "\n\n";
    
    if (count($missingNames) > 0) {
        echo "Missing names:\n";
        foreach ($missingNames as $i => $nameData) {
            echo ($i + 1) . ". \"{$nameData['name']}\" (from {$nameData['pattern']})\n";
            echo "   Original: \"{$nameData['original']}\"\n";
            
            // Test validation
            $isValid = (strlen($nameData['name']) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $nameData['name']));
            echo "   Validation: " . ($isValid ? "PASS" : "FAIL") . "\n";
            
            if (!$isValid) {
                echo "   Validation issue: ";
                if (strlen($nameData['name']) < 3) {
                    echo "Too short (< 3 chars)";
                } else {
                    echo "Regex pattern failed";
                }
                echo "\n";
            }
            echo "\n";
        }
    }
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
