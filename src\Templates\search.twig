{% extends "layouts/main.twig" %}

{% block title %}{{ app.name }} - Rezultate căutare{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
/* Variabile CSS pentru layout optimizat - 10 coloane cu Acțiuni vizibile */
:root {
    --container-max-width-desktop: 1400px;
    --container-max-width-tablet: 1200px;
    --table-min-width-desktop: 1200px;
    --table-min-width-tablet: 1100px;
}

/* Expandarea containerelor pentru tabelul larg */
.container-fluid {
    max-width: none !important;
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Container principal optimizat pentru desktop cu Acțiuni vizibile */
@media (min-width: 1200px) {
    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl {
        max-width: var(--container-max-width-desktop) !important;
    }

    .container-fluid {
        padding-left: 1.5rem; /* Redus pentru a maximiza spațiul pentru tabel */
        padding-right: 1.5rem;
    }
}

/* Container pentru tablete */
@media (min-width: 768px) and (max-width: 1199px) {
    .container,
    .container-lg,
    .container-md,
    .container-sm {
        max-width: var(--container-max-width-tablet) !important;
    }
}

/* Loading overlay pentru căutare */
.search-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.9);
    z-index: 9998;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

/* Stiluri pentru secțiunea de sumar căutare */
.search-summary-section {
    margin: 0;
    border-top: 1px solid #e9ecef;
}

.search-summary-card {
    background-color: #f8f9fa;
    border: none;
    border-left: 4px solid #007bff;
    border-radius: 0;
    padding: 1rem;
    margin: 0;
}

.search-summary-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.search-summary-header i {
    color: #007bff;
}

.search-summary-content {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057;
}

.search-summary-content strong {
    color: #007bff;
    font-weight: 600;
}

/* Layout expandabil pentru tabelul de rezultate */
.table-responsive {
    width: 100%;
    /* Comportament diferit pe baza dimensiunii ecranului */
}

/* Desktop - fără scroll orizontal */
@media (min-width: 1200px) {
    .table-responsive {
        overflow-x: visible; /* Nu mai avem nevoie de scroll pe desktop */
    }
}

/* Tablete și mobile - păstrează scroll orizontal */
@media (max-width: 1199px) {
    .table-responsive {
        overflow-x: auto; /* Scroll orizontal pentru ecrane mai mici */
    }
}

.table {
    width: 100%;
    table-layout: auto; /* Permite coloanelor să se expandeze pe baza conținutului */
    min-width: 100%;
}

/* Text wrapping natural pentru conținut lung */
.table td {
    white-space: normal; /* Permite text wrapping natural */
    overflow: visible; /* Afișează tot conținutul */
    text-overflow: unset; /* Elimină truncarea cu ellipsis */
    position: relative;
    word-wrap: break-word; /* Sparge cuvintele lungi dacă este necesar */
    hyphens: auto; /* Adaugă cratime automate pentru cuvinte lungi */
    padding: 0.75rem 0.5rem; /* Padding optimizat pentru text multi-linie */
    vertical-align: top; /* Aliniază conținutul la partea de sus a celulei */
    line-height: 1.4; /* Spațiere optimă între linii */
}

.table th {
    white-space: normal; /* Permite text wrapping în antete */
    overflow: visible;
    text-overflow: unset;
    word-wrap: break-word;
    padding: 0.75rem 0.5rem;
    vertical-align: top;
    line-height: 1.3;
}

/* Excepție pentru coloana Acțiuni - nu trunca butoanele */
.table td:nth-child(10) {
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
}

/* Asigură că butoanele din coloana Acțiuni sunt complet vizibile */
.table td:nth-child(10) .btn {
    white-space: nowrap;
    min-width: fit-content;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Stiluri pentru conținut complet afișat */
.full-content {
    word-break: break-word;
    overflow-wrap: break-word;
}

/* Stiluri pentru iconița animată de justiție */
.justice-icon-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    text-decoration: none;
}

.justice-icon-btn:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    text-decoration: none;
}

.justice-icon-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Iconița SVG animată */
.justice-icon {
    width: 24px;
    height: 24px;
    fill: white;
    transition: transform 0.3s ease;
}

.justice-icon-btn:hover .justice-icon {
    transform: scale(1.1);
}

/* Animația de balansare pentru ciocan */
@keyframes gavel-swing {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

.justice-icon-btn:hover .gavel-head {
    animation: gavel-swing 0.6s ease-in-out;
}

/* Tooltip pentru accesibilitate */
.justice-icon-btn[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
    pointer-events: none;
}

.justice-icon-btn[data-tooltip]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1001;
    margin-bottom: 1px;
    pointer-events: none;
}

/* Responsive pentru iconița */
@media (max-width: 767px) {
    .justice-icon-btn {
        width: 36px;
        height: 36px;
    }

    .justice-icon {
        width: 20px;
        height: 20px;
    }
}

/* Fallback pentru browsere mai vechi sau în caz de eroare la încărcarea SVG */
.justice-icon-btn .fallback-icon {
    display: none;
    font-size: 16px;
    color: white;
}

.justice-icon-btn:not(.svg-loaded) .justice-icon {
    display: none;
}

.justice-icon-btn:not(.svg-loaded) .fallback-icon {
    display: inline-block;
}

/* Optimizare pentru performanță - preload pentru animații */
.justice-icon-btn {
    will-change: transform;
}

.justice-icon {
    will-change: transform;
}

/* Stiluri pentru butonul de export Excel */
.excel-export-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    white-space: nowrap;
}

.excel-export-btn:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.excel-export-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.excel-export-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.excel-export-btn .export-count {
    font-size: 0.8em;
    opacity: 0.9;
}

/* Stiluri pentru butonul de monitorizare */
.monitor-case-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: 2px solid #ffc107;
    background: transparent;
    color: #ffc107;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.monitor-case-btn:hover {
    background: #ffc107;
    color: #212529;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.4);
}

.monitor-case-btn:active {
    transform: scale(0.95);
}

.monitor-case-btn.monitoring {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.monitor-case-btn.monitoring:hover {
    background: #218838;
    border-color: #218838;
}

.monitor-case-btn i {
    font-size: 14px;
    transition: transform 0.3s ease;
}

.monitor-case-btn:hover i {
    transform: scale(1.1);
}

/* Animație pentru butonul de monitorizare */
@keyframes pulse-monitor {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

.monitor-case-btn.pulse {
    animation: pulse-monitor 2s infinite;
}

/* Loading overlay pentru export */
.export-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.export-loading-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

.export-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #28a745;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Desktop layout (≥1200px) - Lățimi optimizate pentru vizibilitatea coloanei Acțiuni - 10 coloane */
@media (min-width: 1200px) {
    .table {
        min-width: var(--table-min-width-desktop); /* Lățime minimă pentru a acomoda toate coloanele cu conținut complet */
        width: 100%; /* Ocupă întreaga lățime disponibilă a containerului expandat */
    }

    .table th:nth-child(1), /* Număr dosar */
    .table td:nth-child(1) {
        min-width: 115px;
    }

    .table th:nth-child(2), /* Instanță */
    .table td:nth-child(2) {
        min-width: 160px;
    }

    .table th:nth-child(3), /* Obiect */
    .table td:nth-child(3) {
        min-width: 220px; /* Redus pentru a face loc coloanei Acțiuni */
    }

    .table th:nth-child(4), /* Stadiu procesual */
    .table td:nth-child(4) {
        min-width: 115px;
    }

    .table th:nth-child(5), /* Data */
    .table td:nth-child(5) {
        min-width: 95px;
    }

    .table th:nth-child(6), /* Categorie caz */
    .table td:nth-child(6) {
        min-width: 115px;
    }

    .table th:nth-child(7), /* Data ultimei modificări */
    .table td:nth-child(7) {
        min-width: 115px;
    }

    .table th:nth-child(8), /* Nume */
    .table td:nth-child(8) {
        min-width: 140px; /* Redus ușor pentru a face loc coloanei Acțiuni */
    }

    .table th:nth-child(9), /* Calitate */
    .table td:nth-child(9) {
        min-width: 95px;
    }

    .table th:nth-child(10), /* Acțiuni */
    .table td:nth-child(10) {
        min-width: 100px; /* Optimizat pentru iconița de justiție și butonul de monitorizare */
        width: 100px; /* Lățime fixă pentru consistență */
        text-align: center;
    }
}

/* Tablet layout (768px-1199px) - Lățimi minime pentru conținut complet - 10 coloane */
@media (min-width: 768px) and (max-width: 1199px) {
    .table {
        min-width: var(--table-min-width-tablet);
    }

    .table th:nth-child(1), /* Număr dosar */
    .table td:nth-child(1) {
        min-width: 110px;
    }

    .table th:nth-child(2), /* Instanță */
    .table td:nth-child(2) {
        min-width: 160px;
    }

    .table th:nth-child(3), /* Obiect */
    .table td:nth-child(3) {
        min-width: 220px;
    }

    .table th:nth-child(4), /* Stadiu procesual */
    .table td:nth-child(4) {
        min-width: 110px;
    }

    .table th:nth-child(5), /* Data */
    .table td:nth-child(5) {
        min-width: 100px;
    }

    .table th:nth-child(6), /* Categorie caz */
    .table td:nth-child(6) {
        min-width: 110px;
    }

    .table th:nth-child(7), /* Data ultimei modificări */
    .table td:nth-child(7) {
        min-width: 110px;
    }

    .table th:nth-child(8), /* Nume */
    .table td:nth-child(8) {
        min-width: 130px;
    }

    .table th:nth-child(9), /* Calitate */
    .table td:nth-child(9) {
        min-width: 100px;
    }

    .table th:nth-child(10), /* Acțiuni */
    .table td:nth-child(10) {
        min-width: 100px;
        text-align: center;
    }
}

/* Mobile landscape layout (576px-767px) - Scroll orizontal pentru conținut complet - 10 coloane */
@media (min-width: 576px) and (max-width: 767px) {
    .table {
        min-width: 900px;
    }

    .table th:nth-child(1), /* Număr dosar */
    .table td:nth-child(1) {
        min-width: 95px;
    }

    .table th:nth-child(2), /* Instanță */
    .table td:nth-child(2) {
        min-width: 125px;
    }

    .table th:nth-child(3), /* Obiect */
    .table td:nth-child(3) {
        min-width: 160px;
    }

    .table th:nth-child(4), /* Stadiu procesual */
    .table td:nth-child(4) {
        min-width: 95px;
    }

    .table th:nth-child(5), /* Data */
    .table td:nth-child(5) {
        min-width: 85px;
    }

    .table th:nth-child(6), /* Categorie caz */
    .table td:nth-child(6) {
        min-width: 95px;
    }

    .table th:nth-child(7), /* Data ultimei modificări */
    .table td:nth-child(7) {
        min-width: 95px;
    }

    .table th:nth-child(8), /* Nume */
    .table td:nth-child(8) {
        min-width: 105px;
    }

    .table th:nth-child(9), /* Calitate */
    .table td:nth-child(9) {
        min-width: 85px;
    }

    .table th:nth-child(10), /* Acțiuni */
    .table td:nth-child(10) {
        min-width: 85px;
        text-align: center;
    }
}

    /* Optimizare search summary pentru mobile */
    .search-summary-card {
        padding: 0.75rem;
        border-radius: 0;
    }

    .search-summary-header {
        font-size: 0.9rem;
        margin-bottom: 0.4rem;
    }

    .search-summary-content {
        font-size: 0.85rem;
    }
}

.search-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.search-loading-content {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 90%;
    border-top: 4px solid #007bff;
}

.search-loading-spinner {
    width: 50px;
    height: 50px;
    margin: 0 auto 1rem;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: searchLoadingSpin 1s linear infinite;
}

.search-loading-message {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

.search-loading-submessage {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

@keyframes searchLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design pentru mobile */
@media (max-width: 767.98px) {
    .search-loading-content {
        padding: 1.5rem;
        max-width: 300px;
    }

    .search-loading-spinner {
        width: 40px;
        height: 40px;
    }

    .search-loading-message {
        font-size: 0.9rem;
    }

    .search-loading-submessage {
        font-size: 0.8rem;
    }
}

/* Asigură că conținutul principal este ascuns inițial */
.search-main-content {
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.search-main-content.loaded {
    opacity: 1;
}
</style>
{% endblock %}

{% block content %}

<!-- Loading overlay pentru căutare -->
<div id="searchLoadingOverlay" class="search-loading-overlay" role="status" aria-live="polite" aria-label="Se caută dosarele">
    <div class="search-loading-content">
        <div class="search-loading-spinner" aria-hidden="true"></div>
        <p class="search-loading-message">Se caută dosarele...</p>
        <p class="search-loading-submessage">Vă rugăm să așteptați</p>
    </div>
</div>

<div class="search-main-content" id="searchMainContent">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h2 class="card-title h5 mb-0">
                        <i class="fas fa-search me-2"></i>Criterii de căutare
                    </h2>
                    <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#searchCriteria">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="card-body collapse show" id="searchCriteria">
                    <div class="row">
                        {% if searchParams.numarDosar %}
                            <div class="col-md-6 mb-2">
                                <strong>Număr dosar:</strong> {{ searchParams.numarDosar }}
                            </div>
                        {% endif %}

                        {% if searchParams.institutie %}
                            <div class="col-md-6 mb-2">
                                <strong>Instanță:</strong> {{ instante[searchParams.institutie] ?? searchParams.institutie }}
                            </div>
                        {% endif %}

                        {% if searchParams.numeParte %}
                            <div class="col-md-6 mb-2">
                                <strong>Nume parte:</strong> {{ searchParams.numeParte }}
                            </div>
                        {% endif %}

                        {% if searchParams.obiectDosar %}
                            <div class="col-md-6 mb-2">
                                <strong>Obiect dosar:</strong> {{ searchParams.obiectDosar }}
                            </div>
                        {% endif %}

                        {% if searchParams.dataStart %}
                            <div class="col-md-6 mb-2">
                                <strong>Data început:</strong> {{ format_date(searchParams.dataStart) }}
                            </div>
                        {% endif %}

                        {% if searchParams.dataStop %}
                            <div class="col-md-6 mb-2">
                                <strong>Data sfârșit:</strong> {{ format_date(searchParams.dataStop) }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="mt-3">
                        <a href="index.php#cautare-avansata" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-1"></i>Modifică căutarea
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h2 class="card-title h5 mb-0">
                        <i class="fas fa-list me-2"></i>Rezultate căutare ({{ results|length }})
                    </h2>
                    {% if results|length > 0 %}
                        <div class="d-flex">
                            <button type="button"
                                    class="btn btn-success btn-sm excel-export-btn me-2"
                                    onclick="exportToExcel()"
                                    title="Exportă toate rezultatele în format Excel (.xlsx)"
                                    data-total-results="{{ totalResults }}">
                                <i class="fas fa-file-excel me-1"></i>
                                Export Excel
                                <span class="export-count">({{ totalResults }} rezultate)</span>
                            </button>
                            <button id="downloadPdfBtn" class="btn btn-sm btn-success me-2" type="button"
                                    data-query="{{ searchQuery }}">
                                <i class="fas fa-file-pdf me-1"></i>Descarcă PDF
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-download me-1"></i>Export
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="{{ url('export.php', {'format': 'pdf', 'query': searchQuery}) }}" target="_blank">
                                            <i class="fas fa-file-pdf me-1"></i>PDF
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ url('export.php', {'format': 'html', 'query': searchQuery}) }}">
                                            <i class="fas fa-file-code me-1"></i>HTML
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    {% endif %}
                </div>

                {% if results|length > 0 %}
                    <!-- Search Criteria Summary -->
                    <div class="search-summary-section">
                        <div class="search-summary-card">
                            <div class="search-summary-header">
                                <i class="fas fa-search me-2"></i>
                                <span>Căutare efectuată pentru:</span>
                            </div>
                            <div class="search-summary-content">
                                {% set criteria = [] %}
                                {% if searchParams.numarDosar %}
                                    {% set criteria = criteria|merge(['<strong>Număr dosar:</strong> ' ~ searchParams.numarDosar]) %}
                                {% endif %}
                                {% if searchParams.numeParte %}
                                    {% set criteria = criteria|merge(['<strong>Nume parte:</strong> ' ~ searchParams.numeParte]) %}
                                {% endif %}
                                {% if searchParams.obiectDosar %}
                                    {% set criteria = criteria|merge(['<strong>Obiect dosar:</strong> ' ~ searchParams.obiectDosar]) %}
                                {% endif %}
                                {% if searchParams.institutie %}
                                    {% set criteria = criteria|merge(['<strong>Instanță:</strong> ' ~ (instante[searchParams.institutie] ?? searchParams.institutie)]) %}
                                {% endif %}
                                {% if searchParams.dataStart or searchParams.dataStop %}
                                    {% set dateRange = '' %}
                                    {% if searchParams.dataStart and searchParams.dataStop %}
                                        {% set dateRange = searchParams.dataStart ~ ' - ' ~ searchParams.dataStop %}
                                    {% elseif searchParams.dataStart %}
                                        {% set dateRange = 'de la ' ~ searchParams.dataStart %}
                                    {% elseif searchParams.dataStop %}
                                        {% set dateRange = 'până la ' ~ searchParams.dataStop %}
                                    {% endif %}
                                    {% if dateRange %}
                                        {% set criteria = criteria|merge(['<strong>Perioada:</strong> ' ~ dateRange]) %}
                                    {% endif %}
                                {% endif %}
                                {% if searchParams.dataUltimaModificareStart or searchParams.dataUltimaModificareStop %}
                                    {% set modDateRange = '' %}
                                    {% if searchParams.dataUltimaModificareStart and searchParams.dataUltimaModificareStop %}
                                        {% set modDateRange = searchParams.dataUltimaModificareStart ~ ' - ' ~ searchParams.dataUltimaModificareStop %}
                                    {% elseif searchParams.dataUltimaModificareStart %}
                                        {% set modDateRange = 'de la ' ~ searchParams.dataUltimaModificareStart %}
                                    {% elseif searchParams.dataUltimaModificareStop %}
                                        {% set modDateRange = 'până la ' ~ searchParams.dataUltimaModificareStop %}
                                    {% endif %}
                                    {% if modDateRange %}
                                        {% set criteria = criteria|merge(['<strong>Ultima modificare:</strong> ' ~ modDateRange]) %}
                                    {% endif %}
                                {% endif %}

                                {% if criteria|length > 0 %}
                                    {{ criteria|join(' • ')|raw }}
                                {% else %}
                                    <span class="text-muted">Căutare fără criterii specifice</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div class="card-body">
                    {% if results|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Număr dosar</th>
                                        <th>Instanță</th>
                                        <th>Obiect</th>
                                        <th>Stadiu procesual</th>
                                        <th>Data</th>
                                        <th>Categorie caz</th>
                                        <th>Data ultimei modificări</th>
                                        <th>Nume</th>
                                        <th>Calitate</th>
                                        <th>Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for dosar in results %}
                                        <tr>
                                            <td>{{ dosar.numar }}</td>
                                            <td>{{ instante[dosar.institutie] ?? dosar.institutie }}</td>
                                            <td>{{ truncate(dosar.obiect, 50) }}</td>
                                            <td>{{ dosar.stadiuProcesualNume ?: '-' }}</td>
                                            <td>{{ format_date(dosar.data) }}</td>
                                            <td>{{ dosar.categorieCazNume ?: '-' }}</td>
                                            <td>{{ format_date(dosar.dataModificare) }}</td>
                                            <td>
                                                {% if dosar.parti|length > 0 %}
                                                    {% set relevantParty = null %}
                                                    {# Căutăm partea care se potrivește cu termenul de căutare #}
                                                    {% if searchParams.numeParte %}
                                                        {% set normalizedSearchTerm = searchParams.numeParte|lower|trim %}
                                                        {% for parte in dosar.parti %}
                                                            {% if parte.nume and parte.nume|lower|trim contains normalizedSearchTerm %}
                                                                {% set relevantParty = parte %}
                                                                {% break %}
                                                            {% endif %}
                                                        {% endfor %}
                                                    {% endif %}
                                                    {# Fallback la prima parte dacă nu găsim o potrivire #}
                                                    {% if not relevantParty %}
                                                        {% set relevantParty = dosar.parti[0] %}
                                                    {% endif %}

                                                    {{ relevantParty.nume }}
                                                    {% if dosar.parti|length > 1 %}
                                                        <small class="text-muted">(+{{ dosar.parti|length - 1 }} alții)</small>
                                                    {% endif %}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if dosar.parti|length > 0 %}
                                                    {% set relevantParty = null %}
                                                    {# Căutăm partea care se potrivește cu termenul de căutare #}
                                                    {% if searchParams.numeParte %}
                                                        {% set normalizedSearchTerm = searchParams.numeParte|lower|trim %}
                                                        {% for parte in dosar.parti %}
                                                            {% if parte.nume and parte.nume|lower|trim contains normalizedSearchTerm %}
                                                                {% set relevantParty = parte %}
                                                                {% break %}
                                                            {% endif %}
                                                        {% endfor %}
                                                    {% endif %}
                                                    {# Fallback la prima parte dacă nu găsim o potrivire #}
                                                    {% if not relevantParty %}
                                                        {% set relevantParty = dosar.parti[0] %}
                                                    {% endif %}

                                                    {{ relevantParty.calitate ?: '-' }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1 justify-content-center">
                                                    <a href="{{ url('detalii_dosar.php', {'numar': dosar.numar, 'institutie': dosar.institutie}) }}"
                                                       class="justice-icon-btn"
                                                       data-tooltip="Detalii dosar"
                                                       aria-label="Vezi detaliile dosarului {{ dosar.numar }}"
                                                       title="Vezi detaliile dosarului">
                                                        <svg class="justice-icon" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                                            <!-- Ciocanul de judecător animat -->
                                                            <g class="gavel-head">
                                                                <!-- Capul ciocanului -->
                                                                <rect x="50" y="180" width="120" height="40" rx="8" fill="currentColor"/>
                                                                <rect x="60" y="185" width="100" height="30" rx="4" fill="rgba(255,255,255,0.2)"/>

                                                                <!-- Mânerul ciocanului -->
                                                                <rect x="170" y="195" width="200" height="10" rx="5" fill="currentColor"/>
                                                                <rect x="175" y="197" width="190" height="6" rx="3" fill="rgba(255,255,255,0.2)"/>
                                                            </g>

                                                            <!-- Blocul de lemn -->
                                                            <rect x="380" y="220" width="80" height="30" rx="4" fill="currentColor"/>
                                                            <rect x="385" y="225" width="70" height="20" rx="2" fill="rgba(255,255,255,0.2)"/>

                                                            <!-- Cântarul justiției -->
                                                            <g transform="translate(200, 300)">
                                                                <!-- Stâlpul central -->
                                                                <rect x="55" y="0" width="4" height="120" fill="currentColor"/>

                                                                <!-- Brațul cântarului -->
                                                                <rect x="10" y="25" width="94" height="3" fill="currentColor"/>

                                                                <!-- Talgerele -->
                                                                <ellipse cx="20" cy="40" rx="15" ry="8" fill="currentColor"/>
                                                                <ellipse cx="94" cy="40" rx="15" ry="8" fill="currentColor"/>

                                                                <!-- Lanțurile -->
                                                                <line x1="20" y1="28" x2="20" y2="32" stroke="currentColor" stroke-width="1"/>
                                                                <line x1="94" y1="28" x2="94" y2="32" stroke="currentColor" stroke-width="1"/>

                                                                <!-- Baza -->
                                                                <rect x="40" y="115" width="34" height="8" rx="4" fill="currentColor"/>
                                                            </g>

                                                            <!-- Efecte de strălucire -->
                                                            <defs>
                                                                <linearGradient id="shine-twig" x1="0%" y1="0%" x2="100%" y2="100%">
                                                                    <stop offset="0%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
                                                                    <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
                                                                </linearGradient>
                                                            </defs>
                                                            <rect x="0" y="0" width="512" height="512" fill="url(#shine-twig)" opacity="0.1"/>
                                                        </svg>
                                                    </a>

                                                    <button type="button"
                                                            class="monitor-case-btn btn btn-sm btn-outline-warning"
                                                            data-case-number="{{ dosar.numar }}"
                                                            data-institution-code="{{ dosar.institutie }}"
                                                            data-institution-name="{{ instante[dosar.institutie] ?? dosar.institutie }}"
                                                            data-case-object="{{ dosar.obiect|slice(0, 100) }}"
                                                            title="Adaugă în monitorizare"
                                                            aria-label="Monitorizează dosarul {{ dosar.numar }}">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>Nu au fost găsite rezultate pentru criteriile de căutare specificate.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>



</div> <!-- End search-main-content -->
{% endblock %}

{% block javascripts %}
<script>
    /**
     * Gestionează loading overlay-ul pentru pagina de căutare
     */
    function initSearchLoadingOverlay() {
        const loadingOverlay = document.getElementById('searchLoadingOverlay');
        const mainContent = document.getElementById('searchMainContent');

        if (!loadingOverlay || !mainContent) {
            console.error('Elementele pentru loading overlay nu au fost găsite!');
            return;
        }

        // Funcție pentru ascunderea loading overlay-ului
        function hideLoadingOverlay() {
            // Adăugăm clasa loaded la conținutul principal
            mainContent.classList.add('loaded');

            // Ascundem overlay-ul cu animație
            loadingOverlay.classList.add('fade-out');

            // Eliminăm complet overlay-ul după animație
            setTimeout(() => {
                if (loadingOverlay.parentNode) {
                    loadingOverlay.parentNode.removeChild(loadingOverlay);
                }
            }, 500);

            console.log('Search loading overlay ascuns cu succes.');
        }

        // Verificăm dacă pagina are rezultate sau mesaje
        const hasResults = document.querySelector('.table');
        const hasMessages = document.querySelector('.alert');
        const hasContent = hasResults || hasMessages;

        // Calculăm timpul minim de afișare (1-2 secunde conform cerințelor)
        const minDisplayTime = 1000; // 1 secundă
        const startTime = Date.now();

        // Funcție pentru ascunderea cu respectarea timpului minim
        function hideWithMinTime() {
            const elapsedTime = Date.now() - startTime;
            const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

            setTimeout(hideLoadingOverlay, remainingTime);
        }

        // Ascundem loading-ul după ce conținutul este gata
        if (hasContent) {
            // Dacă avem conținut, ascundem loading-ul
            hideWithMinTime();
        } else {
            // Fallback: ascundem după 2 secunde maxim
            setTimeout(hideLoadingOverlay, 2000);
        }

        console.log('Search loading overlay inițializat pentru căutare.');
    }

    /**
     * Actualizează mesajul din loading overlay pentru căutare
     */
    function updateSearchLoadingMessage(message, submessage = '') {
        const messageElement = document.querySelector('.search-loading-message');
        const submessageElement = document.querySelector('.search-loading-submessage');

        if (messageElement) {
            messageElement.textContent = message;
        }

        if (submessageElement) {
            submessageElement.textContent = submessage;
        }
    }

    /**
     * Funcție eliminată - nu mai este necesară pentru conținut complet afișat
     * Conținutul este acum afișat complet în tabel fără truncare
     */

    document.addEventListener('DOMContentLoaded', function() {
        // Inițializăm loading overlay-ul pentru căutare
        initSearchLoadingOverlay();

        // Nu mai avem nevoie de tooltips pentru conținut trunchiat
        // Conținutul este afișat complet în tabel

        // Inițializare iconițe de justiție
        initJusticeIcons();

        // Implementăm funcționalitatea de căutare rapidă
        const quickSearchLinks = document.querySelectorAll('.quick-search');
        quickSearchLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const searchTerm = this.getAttribute('data-search');
                window.location.href = 'index.php#cautare-avansata?numeParte=' + encodeURIComponent(searchTerm);
            });
        });

        // Inițializăm butoanele de monitorizare
        initMonitoringButtons();
    });

    /**
     * Inițializează iconițele de justiție cu fallback
     */
    function initJusticeIcons() {
        console.debug('Inițializare iconițe de justiție...');

        const justiceButtons = document.querySelectorAll('.justice-icon-btn');

        justiceButtons.forEach(button => {
            const svg = button.querySelector('.justice-icon');

            if (svg) {
                // Verificăm dacă SVG-ul s-a încărcat corect
                svg.addEventListener('load', function() {
                    button.classList.add('svg-loaded');
                    console.debug('SVG încărcat cu succes pentru butonul de justiție');
                });

                svg.addEventListener('error', function() {
                    console.debug('Eroare la încărcarea SVG, folosim fallback');
                    showFallbackIcon(button);
                });

                // Verificăm dacă SVG-ul este deja încărcat
                if (svg.complete || svg.readyState === 'complete') {
                    button.classList.add('svg-loaded');
                } else {
                    // Timeout pentru fallback în caz că SVG-ul nu se încarcă
                    setTimeout(() => {
                        if (!button.classList.contains('svg-loaded')) {
                            console.debug('Timeout pentru încărcarea SVG, folosim fallback');
                            showFallbackIcon(button);
                        }
                    }, 2000);
                }
            } else {
                // Dacă nu există SVG, afișăm direct fallback-ul
                showFallbackIcon(button);
            }
        });

        console.debug(`Inițializate ${justiceButtons.length} iconițe de justiție`);
    }

    /**
     * Afișează iconița de fallback
     */
    function showFallbackIcon(button) {
        // Adăugăm iconița de fallback dacă nu există deja
        let fallbackIcon = button.querySelector('.fallback-icon');
        if (!fallbackIcon) {
            fallbackIcon = document.createElement('i');
            fallbackIcon.className = 'fas fa-gavel fallback-icon';
            fallbackIcon.setAttribute('aria-hidden', 'true');
            button.appendChild(fallbackIcon);
        }

        button.classList.remove('svg-loaded');
        console.debug('Fallback icon afișat pentru butonul de justiție');
    }

    /**
     * Exportă rezultatele căutării în Excel
     */
    function exportToExcel() {
        const exportBtn = document.querySelector('.excel-export-btn');
        const totalResults = exportBtn.getAttribute('data-total-results');

        // Afișăm loading overlay
        showExportLoading(totalResults);

        // Dezactivăm butonul
        exportBtn.disabled = true;
        exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Se exportă...';

        // Construim parametrii pentru export
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.set('export', 'xlsx');
        searchParams.set('all_results', '1'); // Flag pentru toate rezultatele

        // Creăm un link pentru download forțat
        const downloadUrl = 'search.php?' + searchParams.toString();

        // Metodă îmbunătățită pentru download forțat
        try {
            // Încercăm mai întâi cu un link direct
            const downloadLink = document.createElement('a');
            downloadLink.href = downloadUrl;
            downloadLink.download = 'Rezultate_Cautare_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.xlsx';
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);

            // Simulăm click-ul pentru download
            downloadLink.click();

            // Eliminăm link-ul după un scurt delay
            setTimeout(() => {
                if (downloadLink.parentNode) {
                    downloadLink.parentNode.removeChild(downloadLink);
                }
            }, 1000);

            // Fallback cu iframe pentru browsere mai vechi
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.style.width = '0';
            iframe.style.height = '0';
            iframe.style.border = 'none';
            iframe.src = downloadUrl;
            document.body.appendChild(iframe);

            // Eliminăm iframe-ul după download
            setTimeout(() => {
                if (iframe.parentNode) {
                    iframe.parentNode.removeChild(iframe);
                }
            }, 10000);

        } catch (error) {
            console.error('Eroare la inițierea download-ului:', error);
            // Fallback: deschidere în fereastră nouă
            window.open(downloadUrl, '_blank');
        }

        // Monitorizăm progresul și finalizarea
        let progressInterval = setInterval(() => {
            // Simulăm progresul (în realitate ar trebui să vină de la server)
            const progressElement = document.getElementById('exportProgressCount');
            if (progressElement) {
                const currentCount = parseInt(progressElement.textContent) || 0;
                const targetCount = parseInt(totalResults);
                if (currentCount < targetCount) {
                    progressElement.textContent = Math.min(currentCount + Math.ceil(targetCount / 20), targetCount);
                }
            }
        }, 100);

        // Ascundem loading-ul după 3 secunde (redus pentru experiență mai bună)
        setTimeout(() => {
            hideExportLoading();
            clearInterval(progressInterval);

            // Reactivăm butonul
            exportBtn.disabled = false;
            exportBtn.innerHTML = '<i class="fas fa-file-excel me-1"></i>Export Excel <span class="export-count">(' + totalResults + ' rezultate)</span>';

            // Afișăm mesaj de succes
            if (typeof showNotification === 'function') {
                showNotification('Fișierul Excel a fost descărcat cu succes! Poate fi deschis în Microsoft Excel sau LibreOffice Calc.', 'success');
            } else {
                alert('Fișierul Excel a fost descărcat cu succes! Poate fi deschis în Microsoft Excel sau LibreOffice Calc.');
            }
        }, 3000);
    }

    /**
     * Afișează loading overlay pentru export
     */
    function showExportLoading(totalResults) {
        const overlay = document.getElementById('exportLoadingOverlay');
        const progressCount = document.getElementById('exportProgressCount');

        if (overlay) {
            overlay.style.display = 'flex';
            if (progressCount) {
                progressCount.textContent = '0';
            }
        }
    }

    /**
     * Ascunde loading overlay pentru export
     */
    function hideExportLoading() {
        const overlay = document.getElementById('exportLoadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    /**
     * Verifică dacă download-ul a fost inițiat cu succes
     */
    function verifyDownloadStart(downloadUrl) {
        return new Promise((resolve, reject) => {
            // Creăm o cerere HEAD pentru a verifica dacă fișierul poate fi generat
            fetch(downloadUrl, {
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(response => {
                if (response.ok) {
                    resolve(true);
                } else {
                    reject(new Error('Serverul nu poate genera fișierul Excel'));
                }
            })
            .catch(error => {
                reject(error);
            });
        });
    }

    /**
     * Gestionează erorile de export
     */
    function handleExportError(error) {
        console.error('Eroare la exportul Excel:', error);

        hideExportLoading();

        // Reactivăm butonul
        const exportBtn = document.querySelector('.excel-export-btn');
        if (exportBtn) {
            exportBtn.disabled = false;
            const totalResults = exportBtn.getAttribute('data-total-results');
            exportBtn.innerHTML = '<i class="fas fa-file-csv me-1"></i>Export CSV <span class="export-count">(' + totalResults + ' rezultate)</span>';
        }

        // Afișăm mesaj de eroare
        const errorMessage = error.message || 'A apărut o eroare la generarea fișierului CSV';
        if (typeof showNotification === 'function') {
            showNotification('Eroare: ' + errorMessage, 'danger');
        } else {
            alert('Eroare: ' + errorMessage);
        }
    }

    // Inițializează loading overlay-ul imediat (înainte de DOMContentLoaded)
    // pentru a fi siguri că este vizibil de la început
    (function() {
        // Verificăm dacă loading overlay-ul există
        function checkAndInitSearchLoading() {
            const loadingOverlay = document.getElementById('searchLoadingOverlay');
            if (loadingOverlay) {
                // Overlay-ul este deja vizibil prin CSS
                console.log('Search loading overlay detectat și activ.');
            } else {
                // Reîncercăm după un scurt interval
                setTimeout(checkAndInitSearchLoading, 50);
            }
        }

        // Începem verificarea
        if (document.readyState === 'loading') {
            checkAndInitSearchLoading();
        }
    })();

    /**
     * Inițializează butoanele de monitorizare
     */
    function initMonitoringButtons() {
        const monitorButtons = document.querySelectorAll('.monitor-case-btn');

        monitorButtons.forEach(button => {
            button.addEventListener('click', function() {
                const caseNumber = this.dataset.caseNumber;
                const institutionCode = this.dataset.institutionCode;
                const institutionName = this.dataset.institutionName;
                const caseObject = this.dataset.caseObject;

                addCaseToMonitoring(caseNumber, institutionCode, institutionName, caseObject, this);
            });
        });

        console.log(`Inițializate ${monitorButtons.length} butoane de monitorizare`);
    }

    /**
     * Adaugă un dosar în monitorizare
     */
    function addCaseToMonitoring(caseNumber, institutionCode, institutionName, caseObject, buttonElement) {
        // Verificăm dacă utilizatorul este autentificat
        if (!isUserAuthenticated()) {
            if (confirm('Pentru a monitoriza dosare trebuie să vă autentificați. Doriți să mergeți la pagina de autentificare?')) {
                window.location.href = 'auth/login.php';
            }
            return;
        }

        // Dezactivăm butonul temporar
        buttonElement.disabled = true;
        buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // Pregătim datele pentru trimitere
        const formData = new FormData();
        formData.append('action', 'add_case');
        formData.append('case_number', caseNumber);
        formData.append('institution_code', institutionCode);
        formData.append('institution_name', institutionName);
        formData.append('case_object', caseObject);
        formData.append('notification_frequency', 'daily'); // Frecvența implicită

        // Trimitem cererea către sistemul de monitorizare
        fetch('monitor.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Actualizăm butonul pentru a indica că dosarul este monitorizat
                buttonElement.classList.add('monitoring');
                buttonElement.innerHTML = '<i class="fas fa-check"></i>';
                buttonElement.title = 'Dosar monitorizat - Click pentru a vedea detaliile';
                buttonElement.setAttribute('aria-label', `Dosarul ${caseNumber} este monitorizat`);

                // Schimbăm funcționalitatea butonului
                buttonElement.onclick = function() {
                    window.location.href = 'monitor.php';
                };

                // Afișăm notificare de succes
                if (typeof showNotification === 'function') {
                    showNotification(`Dosarul ${caseNumber} a fost adăugat în monitorizare!`, 'success');
                } else {
                    alert(`Dosarul ${caseNumber} a fost adăugat în monitorizare!`);
                }

                // Animație de confirmare
                buttonElement.classList.add('pulse');
                setTimeout(() => {
                    buttonElement.classList.remove('pulse');
                }, 2000);

            } else {
                throw new Error(data.error || 'Eroare la adăugarea în monitorizare');
            }
        })
        .catch(error => {
            console.error('Eroare la monitorizare:', error);

            // Afișăm mesajul de eroare
            const errorMessage = error.message || 'A apărut o eroare la adăugarea în monitorizare';
            if (typeof showNotification === 'function') {
                showNotification(errorMessage, 'danger');
            } else {
                alert('Eroare: ' + errorMessage);
            }

            // Restaurăm butonul
            buttonElement.innerHTML = '<i class="fas fa-eye"></i>';
        })
        .finally(() => {
            buttonElement.disabled = false;
        });
    }

    /**
     * Verifică dacă utilizatorul este autentificat
     */
    function isUserAuthenticated() {
        // Pentru demo, returnăm true
        // În implementarea reală, aceasta ar trebui să verifice sesiunea utilizatorului
        return true;
    }
</script>

<!-- Loading overlay pentru export Excel -->
<div class="export-loading-overlay" id="exportLoadingOverlay">
    <div class="export-loading-content">
        <div class="export-loading-spinner"></div>
        <h5 class="mb-2">Se exportă datele în Excel...</h5>
        <p class="text-muted mb-0">Vă rugăm să așteptați, se procesează <span id="exportProgressCount">0</span> rezultate.</p>
    </div>
</div>
{% endblock %}
