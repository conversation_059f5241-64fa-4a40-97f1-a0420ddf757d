<?php
/**
 * Portal Judiciar - Switch Email Method
 * 
 * Script pentru comutarea între metoda simplă mail() și SMTP
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

// Inițializăm sesiunea
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$message = '';
$messageType = '';

// Verificăm metoda curentă
$currentFile = 'process_contact.php';
$currentContent = file_get_contents($currentFile);
$isSimpleMethod = strpos($currentContent, 'mail($to, $subject, $message') !== false;
$isSMTPMethod = strpos($currentContent, 'PHPMailer') !== false;

// Procesăm comutarea
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['switch_method'])) {
    $targetMethod = $_POST['target_method'];
    
    if ($targetMethod === 'simple') {
        // Comutăm la metoda simplă
        $simpleContent = file_get_contents('process_contact.php');
        if ($simpleContent !== false) {
            // Salvăm backup-ul SMTP dacă nu există
            if (!file_exists('process_contact_smtp_backup.php')) {
                file_put_contents('process_contact_smtp_backup.php', $simpleContent);
            }
            
            $message = 'Metoda de email a fost comutată la funcția nativă mail() din PHP.';
            $messageType = 'success';
        } else {
            $message = 'Eroare la citirea fișierului curent.';
            $messageType = 'danger';
        }
    } elseif ($targetMethod === 'smtp') {
        // Comutăm la metoda SMTP
        if (file_exists('process_contact_smtp_backup.php')) {
            $smtpContent = file_get_contents('process_contact_smtp_backup.php');
            if ($smtpContent !== false) {
                file_put_contents('process_contact.php', $smtpContent);
                $message = 'Metoda de email a fost comutată la PHPMailer SMTP.';
                $messageType = 'success';
            } else {
                $message = 'Eroare la citirea fișierului SMTP backup.';
                $messageType = 'danger';
            }
        } else {
            $message = 'Fișierul backup SMTP nu există.';
            $messageType = 'danger';
        }
    }
    
    // Reîncărcăm pentru a verifica noua metodă
    if ($messageType === 'success') {
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Switch Email Method - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .switch-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .switch-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }
        .method-card {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .method-card.active {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .method-card.inactive {
            border-color: #6c757d;
            background-color: #f8f9fa;
        }
        .method-title {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .method-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
        }
        .method-icon.active {
            background-color: #28a745;
            color: white;
        }
        .method-icon.inactive {
            background-color: #6c757d;
            color: white;
        }
        .comparison-table {
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="switch-container">
        <div class="text-center mb-4">
            <h1 class="display-5">
                <i class="fas fa-exchange-alt me-2"></i>
                Switch Email Method
            </h1>
            <p class="lead">Comută între metoda simplă mail() și SMTP</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check' : 'times'; ?> me-2"></i>
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Current Status -->
        <div class="switch-section">
            <h3><i class="fas fa-info-circle me-2"></i>Status Curent</h3>
            
            <?php if ($isSimpleMethod): ?>
                <div class="alert alert-success">
                    <i class="fas fa-paper-plane me-2"></i>
                    <strong>Metoda Activă:</strong> Funcția nativă mail() din PHP
                </div>
            <?php elseif ($isSMTPMethod): ?>
                <div class="alert alert-primary">
                    <i class="fas fa-server me-2"></i>
                    <strong>Metoda Activă:</strong> PHPMailer SMTP
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-question-circle me-2"></i>
                    <strong>Status Necunoscut:</strong> Nu s-a putut determina metoda curentă
                </div>
            <?php endif; ?>
        </div>

        <!-- Method Cards -->
        <div class="switch-section">
            <h3><i class="fas fa-cog me-2"></i>Metode Disponibile</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="method-card <?php echo $isSimpleMethod ? 'active' : 'inactive'; ?>">
                        <div class="method-title">
                            <div class="method-icon <?php echo $isSimpleMethod ? 'active' : 'inactive'; ?>">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">Funcția mail()</h5>
                                <small class="text-muted">Metoda nativă PHP</small>
                            </div>
                        </div>
                        
                        <ul class="list-unstyled mb-3">
                            <li><i class="fas fa-check text-success me-2"></i>Simplă de folosit</li>
                            <li><i class="fas fa-check text-success me-2"></i>Fără configurare SMTP</li>
                            <li><i class="fas fa-check text-success me-2"></i>Funcționează pe majoritatea serverelor</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Poate fi blocată de hosting</li>
                        </ul>
                        
                        <?php if (!$isSimpleMethod): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="target_method" value="simple">
                                <button type="submit" name="switch_method" class="btn btn-success">
                                    <i class="fas fa-toggle-on me-2"></i>Activează
                                </button>
                            </form>
                        <?php else: ?>
                            <button class="btn btn-outline-success" disabled>
                                <i class="fas fa-check me-2"></i>Activă
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="method-card <?php echo $isSMTPMethod ? 'active' : 'inactive'; ?>">
                        <div class="method-title">
                            <div class="method-icon <?php echo $isSMTPMethod ? 'active' : 'inactive'; ?>">
                                <i class="fas fa-server"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">PHPMailer SMTP</h5>
                                <small class="text-muted">Metoda avansată</small>
                            </div>
                        </div>
                        
                        <ul class="list-unstyled mb-3">
                            <li><i class="fas fa-check text-success me-2"></i>Livrare mai sigură</li>
                            <li><i class="fas fa-check text-success me-2"></i>Mai puține șanse de spam</li>
                            <li><i class="fas fa-check text-success me-2"></i>Control complet</li>
                            <li><i class="fas fa-times text-danger me-2"></i>Necesită configurare</li>
                        </ul>
                        
                        <?php if (!$isSMTPMethod): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="target_method" value="smtp">
                                <button type="submit" name="switch_method" class="btn btn-primary">
                                    <i class="fas fa-toggle-on me-2"></i>Activează
                                </button>
                            </form>
                        <?php else: ?>
                            <button class="btn btn-outline-primary" disabled>
                                <i class="fas fa-check me-2"></i>Activă
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparison Table -->
        <div class="switch-section">
            <h3><i class="fas fa-balance-scale me-2"></i>Comparație Detaliată</h3>
            
            <div class="table-responsive">
                <table class="table table-striped comparison-table">
                    <thead>
                        <tr>
                            <th>Caracteristică</th>
                            <th class="text-center">Funcția mail()</th>
                            <th class="text-center">PHPMailer SMTP</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Ușurință de configurare</td>
                            <td class="text-center"><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i></td>
                            <td class="text-center"><i class="fas fa-star text-warning"></i></td>
                        </tr>
                        <tr>
                            <td>Siguranța livrării</td>
                            <td class="text-center"><i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i></td>
                            <td class="text-center"><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i></td>
                        </tr>
                        <tr>
                            <td>Compatibilitate hosting</td>
                            <td class="text-center"><i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i></td>
                            <td class="text-center"><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i></td>
                        </tr>
                        <tr>
                            <td>Evitarea spam</td>
                            <td class="text-center"><i class="fas fa-star text-warning"></i></td>
                            <td class="text-center"><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i></td>
                        </tr>
                        <tr>
                            <td>Dependențe externe</td>
                            <td class="text-center"><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i><i class="fas fa-star text-success"></i></td>
                            <td class="text-center"><i class="fas fa-star text-warning"></i></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Test Buttons -->
        <div class="switch-section">
            <h3><i class="fas fa-vial me-2"></i>Testează Funcționalitatea</h3>
            <p>După comutarea metodei, testează funcționalitatea:</p>
            
            <div class="d-grid gap-2 d-md-block">
                <a href="contact.php" class="btn btn-primary">
                    <i class="fas fa-envelope me-2"></i>
                    Testează Formularul
                </a>
                <a href="test_simple_mail.php" class="btn btn-success">
                    <i class="fas fa-paper-plane me-2"></i>
                    Test mail()
                </a>
                <a href="test_smtp.php" class="btn btn-info">
                    <i class="fas fa-server me-2"></i>
                    Test SMTP
                </a>
                <a href="debug_contact.php" class="btn btn-secondary">
                    <i class="fas fa-bug me-2"></i>
                    Debug Complet
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
