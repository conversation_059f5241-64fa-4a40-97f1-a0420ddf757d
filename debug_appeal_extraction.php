<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 DEBUGGING APPEAL EXTRACTION PATTERNS\n";
echo "=======================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current parties: " . count($dosar->parti) . "\n\n";
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    echo "📄 SolutieSumar text length: " . strlen($solutieSumarText) . " characters\n\n";
    
    // Test the exact pattern we're using
    echo "🔍 TESTING APPEAL EXTRACTION PATTERN:\n";
    echo "====================================\n\n";
    
    $pattern = '/(Anulează|Respinge)\s+apelurile\s+formulate\s+de\s+apelanţii\s+([^.]+?)(?:\s*ca\s+(?:netimbrate|nefondate))?\./' ;
    
    if (preg_match_all($pattern, $solutieSumarText, $appealMatches, PREG_SET_ORDER)) {
        echo "Found " . count($appealMatches) . " appeal sections:\n\n";
        
        $totalExtracted = 0;
        
        foreach ($appealMatches as $i => $appealMatch) {
            $action = $appealMatch[1]; // "Anulează" or "Respinge"
            $apellantsText = $appealMatch[2];
            
            echo "Section " . ($i + 1) . ": {$action}\n";
            echo "Raw text length: " . strlen($apellantsText) . " characters\n";
            echo "Raw text preview: \"" . substr($apellantsText, 0, 200) . "...\"\n\n";
            
            // Split by comma and extract individual names
            $apellantNames = explode(',', $apellantsText);
            echo "Comma-separated parts: " . count($apellantNames) . "\n";
            
            $validNames = [];
            foreach ($apellantNames as $j => $apellantName) {
                $originalName = trim($apellantName);
                $apellantName = trim($apellantName);
                
                // Enhanced cleanup for appellant names
                $apellantName = preg_replace('/\s*şi\s*$/', '', $apellantName); // Remove trailing "şi"
                $apellantName = preg_replace('/\s*\?.*$/', '', $apellantName); // Remove question marks and following text
                $apellantName = preg_replace('/\s*\(.*?\)/', '', $apellantName); // Remove parenthetical content
                $apellantName = preg_replace('/\s*fostă\s+[A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+/', '', $apellantName); // Remove "fostă" references
                $apellantName = preg_replace('/\s*cu domiciliul.*$/i', '', $apellantName);
                $apellantName = preg_replace('/\s*reprezentat.*$/i', '', $apellantName);
                $apellantName = preg_replace('/\s*prin avocat.*$/i', '', $apellantName);
                $apellantName = trim($apellantName);
                
                // Check if it's valid
                $isValid = (strlen($apellantName) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $apellantName));
                
                if ($isValid) {
                    $validNames[] = $apellantName;
                }
                
                // Show first 10 for debugging
                if ($j < 10) {
                    echo "  " . ($j + 1) . ". Original: \"{$originalName}\"\n";
                    echo "      Cleaned: \"{$apellantName}\"\n";
                    echo "      Valid: " . ($isValid ? "YES" : "NO") . "\n";
                }
            }
            
            if (count($apellantNames) > 10) {
                echo "  ... and " . (count($apellantNames) - 10) . " more names\n";
            }
            
            echo "\nValid names extracted from this section: " . count($validNames) . "\n";
            $totalExtracted += count($validNames);
            
            echo "Sample valid names:\n";
            for ($k = 0; $k < min(5, count($validNames)); $k++) {
                echo "  - " . $validNames[$k] . "\n";
            }
            
            echo "\n" . str_repeat("-", 50) . "\n\n";
        }
        
        echo "TOTAL VALID NAMES EXTRACTED: {$totalExtracted}\n";
        echo "CURRENT DECISION TEXT PARTIES: 251\n";
        echo "DIFFERENCE: " . ($totalExtracted - 251) . "\n\n";
        
    } else {
        echo "❌ No appeal sections found with the pattern\n";
    }
    
    // Let's also try a simpler approach to count all potential names
    echo "🔍 ALTERNATIVE COUNTING APPROACH:\n";
    echo "=================================\n\n";
    
    // Count all potential names in the entire text
    $allCommaNames = explode(',', $solutieSumarText);
    $potentialNames = [];
    
    foreach ($allCommaNames as $name) {
        $name = trim($name);
        $name = preg_replace('/.*apelanţii\s+/', '', $name); // Remove everything before "apelanţii"
        $name = preg_replace('/\s*ca\s+(?:netimbrate|nefondate).*$/', '', $name); // Remove "ca netimbrate/nefondate"
        $name = preg_replace('/\s*\(.*?\)/', '', $name); // Remove parenthetical content
        $name = preg_replace('/\s*fostă\s+[A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+/', '', $name); // Remove "fostă" references
        $name = preg_replace('/\s*şi\s*$/', '', $name); // Remove trailing "şi"
        $name = preg_replace('/\s*\?.*$/', '', $name); // Remove question marks
        $name = preg_replace('/\..*$/', '', $name); // Remove everything after period
        $name = trim($name);
        
        // Check if it looks like a valid name
        if (strlen($name) >= 3 && preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $name)) {
            $potentialNames[] = $name;
        }
    }
    
    echo "Total comma-separated parts in entire text: " . count($allCommaNames) . "\n";
    echo "Potential valid names found: " . count($potentialNames) . "\n\n";
    
    echo "First 20 potential names:\n";
    for ($i = 0; $i < min(20, count($potentialNames)); $i++) {
        echo "  " . ($i + 1) . ". " . $potentialNames[$i] . "\n";
    }
    
    echo "\n✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
