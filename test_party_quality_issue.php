<?php
/**
 * Test party quality display issue for case 130/98/2022
 */

require_once 'bootstrap.php';
require_once 'services/DosarService.php';

use App\Services\DosarService as PSR4DosarService;

echo "=== TESTING PARTY QUALITY DISPLAY ISSUE ===" . PHP_EOL;
echo "Case: 130/98/2022 from TribunalulIALOMITA" . PHP_EOL;
echo "Looking for: SARAGEA TUDORIŢA with quality 'Intervenient în numele altei persoane'" . PHP_EOL;
echo PHP_EOL;

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$targetParty = 'SARAGEA TUDORIŢA';
$expectedQuality = 'Intervenient în numele altei persoane';

try {
    // Test 1: Legacy DosarService
    echo "=== TEST 1: Legacy DosarService ===" . PHP_EOL;
    $legacyService = new DosarService();
    $legacyResult = $legacyService->getDetaliiDosar($numarDosar, $institutie);
    
    if ($legacyResult && !empty((array)$legacyResult)) {
        echo "✅ Retrieved case with " . count($legacyResult->parti) . " parties" . PHP_EOL;
        
        $found = false;
        foreach ($legacyResult->parti as $index => $party) {
            if (stripos($party['nume'], $targetParty) !== false) {
                $found = true;
                echo "✅ FOUND: '{$party['nume']}'" . PHP_EOL;
                echo "   Quality: '{$party['calitate']}'" . PHP_EOL;
                echo "   Expected: '{$expectedQuality}'" . PHP_EOL;
                
                if (trim($party['calitate']) === trim($expectedQuality)) {
                    echo "   ✅ QUALITY MATCH: Correct quality displayed" . PHP_EOL;
                } else {
                    echo "   ❌ QUALITY MISMATCH: Quality does not match expected" . PHP_EOL;
                }
                break;
            }
        }
        
        if (!$found) {
            echo "❌ Party '{$targetParty}' not found in legacy service" . PHP_EOL;
        }
    } else {
        echo "❌ Legacy service returned no data" . PHP_EOL;
    }
    
    echo PHP_EOL;
    
    // Test 2: PSR-4 DosarService
    echo "=== TEST 2: PSR-4 DosarService ===" . PHP_EOL;
    $psr4Service = new PSR4DosarService();
    $psr4Result = $psr4Service->getDetaliiDosar($numarDosar, $institutie);
    
    if ($psr4Result && !empty((array)$psr4Result)) {
        echo "✅ Retrieved case with " . count($psr4Result->parti) . " parties" . PHP_EOL;
        
        $found = false;
        foreach ($psr4Result->parti as $index => $party) {
            if (stripos($party['nume'], $targetParty) !== false) {
                $found = true;
                echo "✅ FOUND: '{$party['nume']}'" . PHP_EOL;
                echo "   Quality: '{$party['calitate']}'" . PHP_EOL;
                echo "   Expected: '{$expectedQuality}'" . PHP_EOL;
                
                if (trim($party['calitate']) === trim($expectedQuality)) {
                    echo "   ✅ QUALITY MATCH: Correct quality displayed" . PHP_EOL;
                } else {
                    echo "   ❌ QUALITY MISMATCH: Quality does not match expected" . PHP_EOL;
                }
                break;
            }
        }
        
        if (!$found) {
            echo "❌ Party '{$targetParty}' not found in PSR-4 service" . PHP_EOL;
        }
    } else {
        echo "❌ PSR-4 service returned no data" . PHP_EOL;
    }
    
    echo PHP_EOL;
    
    // Test 3: Sample of other parties to check quality consistency
    echo "=== TEST 3: Sample Party Quality Check ===" . PHP_EOL;
    
    if ($psr4Result && !empty((array)$psr4Result)) {
        echo "Checking quality field for first 10 parties:" . PHP_EOL;
        
        $emptyQualityCount = 0;
        $totalChecked = min(10, count($psr4Result->parti));
        
        for ($i = 0; $i < $totalChecked; $i++) {
            $party = $psr4Result->parti[$i];
            $quality = trim($party['calitate']);
            
            echo "  " . ($i + 1) . ". {$party['nume']}" . PHP_EOL;
            echo "     Quality: " . ($quality ? "'{$quality}'" : "EMPTY/MISSING") . PHP_EOL;
            
            if (empty($quality)) {
                $emptyQualityCount++;
            }
        }
        
        echo PHP_EOL;
        echo "📊 Quality Statistics:" . PHP_EOL;
        echo "  - Parties checked: {$totalChecked}" . PHP_EOL;
        echo "  - Empty/missing quality: {$emptyQualityCount}" . PHP_EOL;
        echo "  - Quality completion rate: " . round((($totalChecked - $emptyQualityCount) / $totalChecked) * 100, 1) . "%" . PHP_EOL;
        
        if ($emptyQualityCount > 0) {
            echo "  ⚠️  ISSUE: Some parties have missing quality information" . PHP_EOL;
        } else {
            echo "  ✅ GOOD: All checked parties have quality information" . PHP_EOL;
        }
    }
    
    echo PHP_EOL;
    echo "=== SUMMARY ===" . PHP_EOL;
    echo "This test helps identify if party quality information is properly extracted and displayed." . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace:" . PHP_EOL;
    echo $e->getTraceAsString() . PHP_EOL;
}
