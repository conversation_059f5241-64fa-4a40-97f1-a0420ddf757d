<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 CHECKING DUPLICATE DETECTION FOR: Burduşelu Tudoriţa\n";
echo "=======================================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Total parties: " . count($dosar->parti) . "\n\n";
    
    // Check SOAP API parties for similar names
    echo "🔍 CHECKING SOAP API PARTIES:\n";
    echo "==============================\n";
    
    $soapParties = [];
    $foundSimilar = false;
    
    foreach ($dosar->parti as $party) {
        $partyName = is_array($party) ? $party['nume'] : $party->nume;
        $partySource = is_array($party) ? ($party['source'] ?? 'soap_api') : ($party->source ?? 'soap_api');
        
        if ($partySource === 'soap_api') {
            $soapParties[] = $partyName;
            
            // Check for similar names
            if (stripos($partyName, 'Burduşelu') !== false || stripos($partyName, 'Tudoriţa') !== false ||
                stripos($partyName, 'BURDUŞELU') !== false || stripos($partyName, 'TUDORIŢA') !== false ||
                stripos($partyName, 'Burduşelu') !== false || stripos($partyName, 'Tudoriţa') !== false) {
                echo "Similar SOAP party: '{$partyName}'\n";
                $foundSimilar = true;
            }
        }
    }
    
    if (!$foundSimilar) {
        echo "❌ No similar names found in SOAP API\n";
    }
    
    echo "\nTotal SOAP API parties: " . count($soapParties) . "\n\n";
    
    // Test the normalization function
    echo "🔍 TESTING NORMALIZATION:\n";
    echo "==========================\n";
    
    $testName = "Burduşelu Tudoriţa";
    
    // We need to access the private method, so let's create a reflection
    $reflection = new ReflectionClass($dosarService);
    $normalizeMethod = $reflection->getMethod('normalizePartyName');
    $normalizeMethod->setAccessible(true);
    
    $normalizedTest = $normalizeMethod->invoke($dosarService, $testName);
    echo "Original: '{$testName}'\n";
    echo "Normalized: '{$normalizedTest}'\n\n";
    
    // Check if any SOAP party normalizes to the same value
    echo "🔍 CHECKING FOR NORMALIZED DUPLICATES:\n";
    echo "=======================================\n";
    
    $foundDuplicate = false;
    foreach ($soapParties as $soapParty) {
        $normalizedSoap = $normalizeMethod->invoke($dosarService, $soapParty);
        if ($normalizedSoap === $normalizedTest) {
            echo "✅ DUPLICATE FOUND!\n";
            echo "SOAP party: '{$soapParty}'\n";
            echo "Normalized: '{$normalizedSoap}'\n";
            echo "Test name: '{$testName}'\n";
            echo "Normalized: '{$normalizedTest}'\n";
            $foundDuplicate = true;
            break;
        }
    }
    
    if (!$foundDuplicate) {
        echo "❌ No normalized duplicates found\n";
        echo "This means the name should be extracted!\n\n";
        
        // Let's check a few similar normalizations
        echo "🔍 CHECKING SIMILAR NORMALIZATIONS:\n";
        echo "===================================\n";
        
        $similarNames = [
            'BURDUŞELU TUDORIŢA',
            'Burduşelu Tudoriţa',
            'BURDUŞELU TUDORITA',
            'Burduşelu Tudorita',
            'burduşelu tudoriţa'
        ];
        
        foreach ($similarNames as $similar) {
            $normalizedSimilar = $normalizeMethod->invoke($dosarService, $similar);
            echo "'{$similar}' -> '{$normalizedSimilar}'\n";
            
            // Check if this matches any SOAP party
            foreach ($soapParties as $soapParty) {
                $normalizedSoap = $normalizeMethod->invoke($dosarService, $soapParty);
                if ($normalizedSoap === $normalizedSimilar) {
                    echo "  MATCHES SOAP: '{$soapParty}'\n";
                    break;
                }
            }
        }
    }
    
    echo "\n✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
