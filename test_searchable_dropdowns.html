<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Searchable Dropdowns - Romanian Judicial Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Searchable Dropdown Styles */
        .searchable-select-container {
            position: relative;
            margin-bottom: 0;
        }

        .searchable-select-input {
            border-radius: 0.375rem;
            border: 1px solid #ced4da;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            width: 100%;
        }

        .searchable-select-input:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .searchable-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #ffffff;
            border: 1px solid #ced4da;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            max-height: 250px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .dropdown-item {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            color: #495057;
            text-decoration: none;
            background-color: transparent;
            border: 0;
            display: block;
            width: 100%;
            clear: both;
            font-weight: 400;
            text-align: inherit;
            white-space: nowrap;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f8f9fa;
        }

        .dropdown-item:hover,
        .dropdown-item.highlighted {
            background-color: #f8f9fa;
            color: #16181b;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item.text-muted {
            color: #6c757d !important;
            cursor: default;
        }

        .dropdown-item.text-muted:hover {
            background-color: transparent;
        }

        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            background-color: #f8f9fa;
        }

        .test-result {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 0.25rem;
        }

        .test-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .test-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .test-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">
            <i class="fas fa-search me-2"></i>
            Test Searchable Dropdowns - Romanian Judicial Portal
        </h1>

        <!-- Case Category Dropdown Test -->
        <div class="test-section">
            <h3><i class="fas fa-gavel me-2"></i>Case Category Dropdown Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="categorieCaz" class="form-label">Categorie caz:</label>
                    <div class="searchable-select-container">
                        <input type="text" class="form-control searchable-select-input" id="categorieCazSearch" placeholder="Căutați și selectați o categorie..." autocomplete="off">
                        <select class="form-select d-none" id="categorieCaz" name="categorieCaz">
                            <option value="">-- Toate categoriile --</option>
                            <option value="civil">Civil</option>
                            <option value="penal">Penal</option>
                            <option value="comercial">Comercial</option>
                            <option value="contencios_administrativ">Contencios Administrativ</option>
                            <option value="fiscal">Fiscal</option>
                            <option value="munca">Muncă și Asigurări Sociale</option>
                            <option value="familie">Familie și Minori</option>
                            <option value="executare">Executare</option>
                            <option value="insolventa">Insolvență</option>
                        </select>
                        <div class="searchable-select-dropdown" id="categorieCazDropdown"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="test-result test-info">
                        <strong>Selected Value:</strong> <span id="selectedCategoryValue">None</span><br>
                        <strong>Selected Text:</strong> <span id="selectedCategoryText">None</span>
                    </div>
                </div>
            </div>

            <!-- Test Buttons -->
            <div class="mt-3">
                <button class="btn btn-primary btn-sm" onclick="testCategorySearch('civil')">Test: Search "civil"</button>
                <button class="btn btn-primary btn-sm" onclick="testCategorySearch('munca')">Test: Search "munca"</button>
                <button class="btn btn-primary btn-sm" onclick="testCategorySearch('Muncă')">Test: Search "Muncă" (diacritics)</button>
                <button class="btn btn-secondary btn-sm" onclick="clearCategorySearch()">Clear</button>
            </div>

            <div id="categoryTestResults" class="test-result" style="display: none;"></div>
        </div>

        <!-- Institution Dropdown Test -->
        <div class="test-section">
            <h3><i class="fas fa-university me-2"></i>Institution Dropdown Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <label for="institutie" class="form-label">Instanță judecătorească:</label>
                    <div class="searchable-select-container">
                        <input type="text" class="form-control searchable-select-input" id="institutieSearch" placeholder="Căutați și selectați o instanță..." autocomplete="off">
                        <select class="form-select d-none" id="institutie" name="institutie">
                            <option value="">-- Toate instanțele --</option>
                            <option value="ICCJ">Înalta Curte de Casație și Justiție</option>
                            <option value="CA_BUCURESTI">Curtea de Apel București</option>
                            <option value="CA_CLUJ">Curtea de Apel Cluj</option>
                            <option value="TB_BUCURESTI">Tribunalul București</option>
                            <option value="TB_CLUJ">Tribunalul Cluj</option>
                            <option value="JUD_SECTOR1">Judecătoria Sectorului 1 București</option>
                            <option value="JUD_CLUJ">Judecătoria Cluj-Napoca</option>
                        </select>
                        <div class="searchable-select-dropdown" id="institutieDropdown"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="test-result test-info">
                        <strong>Selected Value:</strong> <span id="selectedInstitutionValue">None</span><br>
                        <strong>Selected Text:</strong> <span id="selectedInstitutionText">None</span>
                    </div>
                </div>
            </div>

            <!-- Test Buttons -->
            <div class="mt-3">
                <button class="btn btn-primary btn-sm" onclick="testInstitutionSearch('bucuresti')">Test: Search "bucuresti"</button>
                <button class="btn btn-primary btn-sm" onclick="testInstitutionSearch('cluj')">Test: Search "cluj"</button>
                <button class="btn btn-primary btn-sm" onclick="testInstitutionSearch('Judecătoria')">Test: Search "Judecătoria"</button>
                <button class="btn btn-secondary btn-sm" onclick="clearInstitutionSearch()">Clear</button>
            </div>

            <div id="institutionTestResults" class="test-result" style="display: none;"></div>
        </div>

        <!-- Overall Test Results -->
        <div class="test-section">
            <h3><i class="fas fa-check-circle me-2"></i>Overall Test Results</h3>
            <button class="btn btn-success" onclick="runAllTests()">Run All Tests</button>
            <div id="overallTestResults" class="mt-3"></div>
        </div>
    </div>

    <script>
        // Romanian diacritics normalization function
        function normalizeRomanianText(text) {
            if (!text) return '';
            return text.toLowerCase()
                .replace(/ă/g, 'a')
                .replace(/â/g, 'a')
                .replace(/î/g, 'i')
                .replace(/ș/g, 's')
                .replace(/ț/g, 't');
        }

        // Initialize searchable case category dropdown
        function initSearchableCaseCategoryDropdown() {
            const searchInput = document.getElementById('categorieCazSearch');
            const hiddenSelect = document.getElementById('categorieCaz');
            const dropdown = document.getElementById('categorieCazDropdown');
            
            if (!searchInput || !hiddenSelect || !dropdown) return;

            let options = [];
            let highlightedIndex = -1;

            // Build options array from select element
            Array.from(hiddenSelect.options).forEach(option => {
                if (option.value) {
                    options.push({
                        value: option.value,
                        text: option.textContent.trim()
                    });
                }
            });

            function renderDropdown(filteredOptions) {
                dropdown.innerHTML = '';
                
                if (filteredOptions.length === 0) {
                    dropdown.innerHTML = '<div class="dropdown-item text-muted">Nu au fost găsite rezultate</div>';
                    dropdown.style.display = 'block';
                    return;
                }

                filteredOptions.forEach((option, index) => {
                    const item = document.createElement('div');
                    item.className = 'dropdown-item';
                    item.textContent = option.text;
                    item.dataset.value = option.value;
                    item.dataset.index = index;
                    
                    item.addEventListener('click', function() {
                        selectOption(option);
                    });
                    
                    dropdown.appendChild(item);
                });
                
                dropdown.style.display = 'block';
                highlightedIndex = -1;
            }

            function selectOption(option) {
                searchInput.value = option.text;
                hiddenSelect.value = option.value;
                dropdown.style.display = 'none';
                highlightedIndex = -1;
                updateCategoryDisplay();
            }

            function filterOptions(searchTerm) {
                const normalizedSearch = normalizeRomanianText(searchTerm);
                return options.filter(option => 
                    normalizeRomanianText(option.text).includes(normalizedSearch)
                );
            }

            // Event listeners - Enhanced to show all options when empty
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length === 0) {
                    // Show all options when input is empty
                    renderDropdown(options);
                    hiddenSelect.value = '';
                    updateCategoryDisplay();
                    return;
                }

                const filteredOptions = filterOptions(searchTerm);
                renderDropdown(filteredOptions);
            });

            searchInput.addEventListener('focus', function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length === 0) {
                    // Show all options when input is empty and focused
                    renderDropdown(options);
                } else {
                    const filteredOptions = filterOptions(searchTerm);
                    renderDropdown(filteredOptions);
                }
            });

            searchInput.addEventListener('click', function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length === 0) {
                    // Show all options when input is empty and clicked
                    renderDropdown(options);
                } else {
                    const filteredOptions = filterOptions(searchTerm);
                    renderDropdown(filteredOptions);
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                    highlightedIndex = -1;
                }
            });
        }

        // Initialize searchable institution dropdown
        function initSearchableInstitutionDropdown() {
            const searchInput = document.getElementById('institutieSearch');
            const hiddenSelect = document.getElementById('institutie');
            const dropdown = document.getElementById('institutieDropdown');
            
            if (!searchInput || !hiddenSelect || !dropdown) return;

            let options = [];
            let highlightedIndex = -1;

            // Build options array from select element
            Array.from(hiddenSelect.options).forEach(option => {
                if (option.value) {
                    options.push({
                        value: option.value,
                        text: option.textContent.trim()
                    });
                }
            });

            function renderDropdown(filteredOptions) {
                dropdown.innerHTML = '';
                
                if (filteredOptions.length === 0) {
                    dropdown.innerHTML = '<div class="dropdown-item text-muted">Nu au fost găsite rezultate</div>';
                    dropdown.style.display = 'block';
                    return;
                }

                filteredOptions.forEach((option, index) => {
                    const item = document.createElement('div');
                    item.className = 'dropdown-item';
                    item.textContent = option.text;
                    item.dataset.value = option.value;
                    item.dataset.index = index;
                    
                    item.addEventListener('click', function() {
                        selectOption(option);
                    });
                    
                    dropdown.appendChild(item);
                });
                
                dropdown.style.display = 'block';
                highlightedIndex = -1;
            }

            function selectOption(option) {
                searchInput.value = option.text;
                hiddenSelect.value = option.value;
                dropdown.style.display = 'none';
                highlightedIndex = -1;
                updateInstitutionDisplay();
            }

            function filterOptions(searchTerm) {
                const normalizedSearch = normalizeRomanianText(searchTerm);
                return options.filter(option => 
                    normalizeRomanianText(option.text).includes(normalizedSearch)
                );
            }

            // Event listeners - Enhanced to show all options when empty
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length === 0) {
                    // Show all options when input is empty
                    renderDropdown(options);
                    hiddenSelect.value = '';
                    updateInstitutionDisplay();
                    return;
                }

                const filteredOptions = filterOptions(searchTerm);
                renderDropdown(filteredOptions);
            });

            searchInput.addEventListener('focus', function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length === 0) {
                    // Show all options when input is empty and focused
                    renderDropdown(options);
                } else {
                    const filteredOptions = filterOptions(searchTerm);
                    renderDropdown(filteredOptions);
                }
            });

            searchInput.addEventListener('click', function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length === 0) {
                    // Show all options when input is empty and clicked
                    renderDropdown(options);
                } else {
                    const filteredOptions = filterOptions(searchTerm);
                    renderDropdown(filteredOptions);
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                    highlightedIndex = -1;
                }
            });
        }

        // Update display functions
        function updateCategoryDisplay() {
            const hiddenSelect = document.getElementById('categorieCaz');
            document.getElementById('selectedCategoryValue').textContent = hiddenSelect.value || 'None';
            document.getElementById('selectedCategoryText').textContent = hiddenSelect.options[hiddenSelect.selectedIndex]?.text || 'None';
        }

        function updateInstitutionDisplay() {
            const hiddenSelect = document.getElementById('institutie');
            document.getElementById('selectedInstitutionValue').textContent = hiddenSelect.value || 'None';
            document.getElementById('selectedInstitutionText').textContent = hiddenSelect.options[hiddenSelect.selectedIndex]?.text || 'None';
        }

        // Test functions
        function testCategorySearch(searchTerm) {
            const searchInput = document.getElementById('categorieCazSearch');
            searchInput.value = searchTerm;
            searchInput.dispatchEvent(new Event('input'));
            
            setTimeout(() => {
                const dropdown = document.getElementById('categorieCazDropdown');
                const items = dropdown.querySelectorAll('.dropdown-item:not(.text-muted)');
                const resultsDiv = document.getElementById('categoryTestResults');
                
                resultsDiv.style.display = 'block';
                resultsDiv.className = 'test-result ' + (items.length > 0 ? 'test-success' : 'test-error');
                resultsDiv.innerHTML = `
                    <strong>Search Term:</strong> "${searchTerm}"<br>
                    <strong>Results Found:</strong> ${items.length}<br>
                    <strong>Results:</strong> ${Array.from(items).map(item => item.textContent).join(', ')}
                `;
            }, 100);
        }

        function testInstitutionSearch(searchTerm) {
            const searchInput = document.getElementById('institutieSearch');
            searchInput.value = searchTerm;
            searchInput.dispatchEvent(new Event('input'));
            
            setTimeout(() => {
                const dropdown = document.getElementById('institutieDropdown');
                const items = dropdown.querySelectorAll('.dropdown-item:not(.text-muted)');
                const resultsDiv = document.getElementById('institutionTestResults');
                
                resultsDiv.style.display = 'block';
                resultsDiv.className = 'test-result ' + (items.length > 0 ? 'test-success' : 'test-error');
                resultsDiv.innerHTML = `
                    <strong>Search Term:</strong> "${searchTerm}"<br>
                    <strong>Results Found:</strong> ${items.length}<br>
                    <strong>Results:</strong> ${Array.from(items).map(item => item.textContent).join(', ')}
                `;
            }, 100);
        }

        function clearCategorySearch() {
            document.getElementById('categorieCazSearch').value = '';
            document.getElementById('categorieCaz').value = '';
            document.getElementById('categorieCazDropdown').style.display = 'none';
            updateCategoryDisplay();
        }

        function clearInstitutionSearch() {
            document.getElementById('institutieSearch').value = '';
            document.getElementById('institutie').value = '';
            document.getElementById('institutieDropdown').style.display = 'none';
            updateInstitutionDisplay();
        }

        function runAllTests() {
            const tests = [
                { name: 'Category Search: civil', func: () => testCategorySearch('civil') },
                { name: 'Category Search: munca', func: () => testCategorySearch('munca') },
                { name: 'Category Search: Muncă (diacritics)', func: () => testCategorySearch('Muncă') },
                { name: 'Institution Search: bucuresti', func: () => testInstitutionSearch('bucuresti') },
                { name: 'Institution Search: cluj', func: () => testInstitutionSearch('cluj') }
            ];

            const resultsDiv = document.getElementById('overallTestResults');
            resultsDiv.innerHTML = '<div class="test-result test-info">Running tests...</div>';

            let testIndex = 0;
            function runNextTest() {
                if (testIndex < tests.length) {
                    tests[testIndex].func();
                    testIndex++;
                    setTimeout(runNextTest, 500);
                } else {
                    resultsDiv.innerHTML = '<div class="test-result test-success">All tests completed! Check individual test results above.</div>';
                }
            }

            runNextTest();
        }

        // Initialize dropdowns when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initSearchableCaseCategoryDropdown();
            initSearchableInstitutionDropdown();
            updateCategoryDisplay();
            updateInstitutionDisplay();
        });
    </script>
</body>
</html>
