<?php
/**
 * Scheduled Notifications Cron Job
 * 
 * This script handles daily digest and weekly summary notifications.
 * Should be run daily at 8:00 AM.
 * 
 * Cron configuration example:
 * 0 8 * * * /usr/bin/php /path/to/just/cron/send_scheduled_notifications.php >> /path/to/just/logs/notifications.log 2>&1
 * 
 * Portal Judiciar România - Case Monitoring System
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

// Prevent web access
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    die('This script can only be run from command line.');
}

// Set error reporting for CLI
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set memory limit
ini_set('memory_limit', '256M');

// Set execution time limit
set_time_limit(900); // 15 minutes

// Include required files
require_once dirname(__DIR__) . '/bootstrap.php';
require_once dirname(__DIR__) . '/includes/config.php';

use App\Services\NotificationManager;

/**
 * Scheduled notifications cron job class
 */
class ScheduledNotificationsCron
{
    private $logFile;
    private $errorLogFile;
    private $startTime;
    private $dailyDigestsSent = 0;
    private $weeklySummariesSent = 0;
    private $errors = 0;
    
    private $notificationManager;
    
    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->logFile = dirname(__DIR__) . '/logs/notifications.log';
        $this->errorLogFile = dirname(__DIR__) . '/logs/notifications_errors.log';
        
        // Ensure log directory exists
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Initialize services
        $this->notificationManager = new NotificationManager();
        
        $this->log("=== Scheduled Notifications Cron Job Started ===");
        $this->log("Process ID: " . getmypid());
        $this->log("Current time: " . date('Y-m-d H:i:s'));
    }
    
    /**
     * Main execution method
     */
    public function run()
    {
        try {
            $this->log("Starting scheduled notifications process...");
            
            // Step 1: Send daily digests
            $this->sendDailyDigests();
            
            // Step 2: Send weekly summaries (only on Mondays)
            if (date('N') == 1) { // Monday
                $this->sendWeeklySummaries();
            } else {
                $this->log("Skipping weekly summaries (not Monday)");
            }
            
            // Step 3: Process any queued notifications
            $this->processQueuedNotifications();
            
            // Step 4: Generate summary
            $this->generateSummary();
            
        } catch (Exception $e) {
            $this->logError("Fatal error in scheduled notifications cron: " . $e->getMessage());
            $this->logError("Stack trace: " . $e->getTraceAsString());
            $this->errors++;
        } finally {
            $this->finalize();
        }
    }
    
    /**
     * Send daily digest notifications
     */
    private function sendDailyDigests()
    {
        $this->log("Sending daily digest notifications...");
        
        try {
            // Queue daily digests for users with daily notification frequency
            $queued = $this->notificationManager->queueDailyDigests();
            $this->log("Queued {$queued} daily digest notifications");
            
            // Process the queued notifications immediately
            $processed = $this->notificationManager->processQueue(100);
            $this->dailyDigestsSent = $processed;
            
            $this->log("Sent {$processed} daily digest notifications");
            
        } catch (Exception $e) {
            $this->logError("Error sending daily digests: " . $e->getMessage());
            $this->errors++;
        }
    }
    
    /**
     * Send weekly summary notifications
     */
    private function sendWeeklySummaries()
    {
        $this->log("Sending weekly summary notifications...");
        
        try {
            // Queue weekly summaries for users with weekly notification frequency
            $queued = $this->notificationManager->queueWeeklySummaries();
            $this->log("Queued {$queued} weekly summary notifications");
            
            // Process the queued notifications immediately
            $processed = $this->notificationManager->processQueue(50);
            $this->weeklySummariesSent = $processed;
            
            $this->log("Sent {$processed} weekly summary notifications");
            
        } catch (Exception $e) {
            $this->logError("Error sending weekly summaries: " . $e->getMessage());
            $this->errors++;
        }
    }
    
    /**
     * Process any remaining queued notifications
     */
    private function processQueuedNotifications()
    {
        $this->log("Processing remaining queued notifications...");
        
        try {
            $processed = $this->notificationManager->processQueue(200);
            $this->log("Processed {$processed} additional notifications from queue");
            
        } catch (Exception $e) {
            $this->logError("Error processing queued notifications: " . $e->getMessage());
            $this->errors++;
        }
    }
    
    /**
     * Generate execution summary
     */
    private function generateSummary()
    {
        $executionTime = microtime(true) - $this->startTime;
        $memoryUsage = memory_get_peak_usage();
        
        $this->log("=== Execution Summary ===");
        $this->log("Execution time: " . round($executionTime, 2) . " seconds");
        $this->log("Peak memory usage: " . $this->formatBytes($memoryUsage));
        $this->log("Daily digests sent: {$this->dailyDigestsSent}");
        $this->log("Weekly summaries sent: {$this->weeklySummariesSent}");
        $this->log("Errors encountered: {$this->errors}");
    }
    
    /**
     * Finalize the cron job execution
     */
    private function finalize()
    {
        $this->log("=== Scheduled Notifications Cron Job Completed ===");
        $this->log(""); // Empty line for log separation
        
        // Exit with appropriate code
        if ($this->errors > 0) {
            exit(1); // Error exit code
        } else {
            exit(0); // Success exit code
        }
    }
    
    /**
     * Log a message
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        
        // Write to log file
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // Also output to console if running in CLI
        echo $logMessage;
    }
    
    /**
     * Log an error message
     */
    private function logError($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] ERROR: {$message}" . PHP_EOL;
        
        // Write to error log file
        file_put_contents($this->errorLogFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // Also write to main log
        $this->log("ERROR: {$message}");
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

// Execute the cron job
$cron = new ScheduledNotificationsCron();
$cron->run();
