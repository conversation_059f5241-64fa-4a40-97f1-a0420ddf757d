<?php
/**
 * Test script to verify hybrid party extraction in case detail pages
 * Tests the specific Bucharest case mentioned in the task
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>Test: Hybrid Party Extraction in Case Detail Pages</h1>\n";
echo "<p>Testing case: 130/98/2022 from Curtea de Apel BUCURESTI</p>\n";
echo "<p><strong>URL being tested:</strong> http://localhost/just/detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI</p>\n";

try {
    // Initialize service
    $dosarService = new DosarService();
    
    // Test parameters
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    echo "<h2>1. Testing Legacy DosarService</h2>\n";
    
    // Get case details
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if ($dosar && !empty($dosar->numar)) {
        echo "<h3>✅ Case Found: {$dosar->numar}</h3>\n";
        echo "<p><strong>Institution:</strong> {$dosar->institutie}</p>\n";
        echo "<p><strong>Object:</strong> " . htmlspecialchars($dosar->obiect) . "</p>\n";
        
        // Analyze parties
        $totalParties = count($dosar->parti ?? []);
        echo "<h3>📊 Party Analysis</h3>\n";
        echo "<p><strong>Total Parties:</strong> {$totalParties}</p>\n";
        
        if (!empty($dosar->parti)) {
            // Count by source
            $soapCount = 0;
            $decisionCount = 0;
            $unknownCount = 0;
            $saragea_found = false;
            
            foreach ($dosar->parti as $parte) {
                $source = $parte->source ?? 'unknown';
                switch ($source) {
                    case 'soap_api': $soapCount++; break;
                    case 'decision_text': $decisionCount++; break;
                    default: $unknownCount++; break;
                }
                
                // Check for Saragea Tudorita
                if (stripos($parte->nume, 'SARAGEA') !== false && stripos($parte->nume, 'TUDORITA') !== false) {
                    $saragea_found = true;
                    echo "<p><strong>🎯 FOUND TARGET PARTY:</strong> " . htmlspecialchars($parte->nume) . " (Source: {$source})</p>\n";
                }
            }
            
            echo "<p><strong>SOAP API Parties:</strong> {$soapCount}</p>\n";
            echo "<p><strong>Decision Text Parties:</strong> {$decisionCount}</p>\n";
            echo "<p><strong>Unknown Source:</strong> {$unknownCount}</p>\n";
            
            if ($saragea_found) {
                echo "<p style='color: green;'><strong>✅ SUCCESS: 'Saragea Tudorita' found in parties list!</strong></p>\n";
            } else {
                echo "<p style='color: red;'><strong>❌ ISSUE: 'Saragea Tudorita' NOT found in parties list</strong></p>\n";
                
                // Show first 10 parties for debugging
                echo "<h4>First 10 parties for debugging:</h4>\n";
                echo "<ul>\n";
                $count = 0;
                foreach ($dosar->parti as $parte) {
                    if ($count >= 10) break;
                    echo "<li>" . htmlspecialchars($parte->nume) . " (" . ($parte->source ?? 'unknown') . ")</li>\n";
                    $count++;
                }
                echo "</ul>\n";
                
                // Search for similar names
                echo "<h4>Searching for similar names containing 'SARAGEA' or 'TUDORITA':</h4>\n";
                echo "<ul>\n";
                foreach ($dosar->parti as $parte) {
                    if (stripos($parte->nume, 'SARAGEA') !== false || stripos($parte->nume, 'TUDORITA') !== false) {
                        echo "<li style='color: orange;'>" . htmlspecialchars($parte->nume) . " (" . ($parte->source ?? 'unknown') . ")</li>\n";
                    }
                }
                echo "</ul>\n";
            }
        } else {
            echo "<p style='color: red;'><strong>❌ No parties found in case</strong></p>\n";
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ Case not found or empty</strong></p>\n";
    }
    
    echo "<hr>\n";
    
    // Test PSR-4 version if available
    if (class_exists('App\\Services\\DosarService')) {
        echo "<h2>2. Testing PSR-4 DosarService</h2>\n";
        
        $psr4Service = new App\Services\DosarService();
        $psr4Dosar = $psr4Service->getDetaliiDosar($numarDosar, $institutie);
        
        if ($psr4Dosar && !empty($psr4Dosar->numar)) {
            $psr4TotalParties = count($psr4Dosar->parti ?? []);
            echo "<p><strong>PSR-4 Total Parties:</strong> {$psr4TotalParties}</p>\n";
            
            // Check for consistency
            if ($psr4TotalParties === $totalParties) {
                echo "<p style='color: green;'><strong>✅ Consistent party count between legacy and PSR-4</strong></p>\n";
            } else {
                echo "<p style='color: orange;'><strong>⚠️ Different party counts: Legacy={$totalParties}, PSR-4={$psr4TotalParties}</strong></p>\n";
            }
        } else {
            echo "<p style='color: red;'><strong>❌ PSR-4 case not found</strong></p>\n";
        }
    } else {
        echo "<p><strong>ℹ️ PSR-4 DosarService not available for testing</strong></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>3. Testing Working Case (Ialomita)</h2>\n";

try {
    // Test the working case for comparison
    $workingDosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if ($workingDosar && !empty($workingDosar->numar)) {
        $workingTotalParties = count($workingDosar->parti ?? []);
        echo "<p><strong>Working Case Total Parties:</strong> {$workingTotalParties}</p>\n";
        
        // Check for Saragea in working case
        $saragea_found_working = false;
        foreach ($workingDosar->parti as $parte) {
            if (stripos($parte->nume, 'SARAGEA') !== false && stripos($parte->nume, 'TUDORITA') !== false) {
                $saragea_found_working = true;
                echo "<p><strong>🎯 FOUND in working case:</strong> " . htmlspecialchars($parte->nume) . " (Source: " . ($parte->source ?? 'unknown') . ")</p>\n";
                break;
            }
        }
        
        if ($saragea_found_working) {
            echo "<p style='color: green;'><strong>✅ Working case correctly shows 'Saragea Tudorita'</strong></p>\n";
        } else {
            echo "<p style='color: orange;'><strong>⚠️ 'Saragea Tudorita' not found in working case either</strong></p>\n";
        }
    } else {
        echo "<p style='color: red;'><strong>❌ Working case not found</strong></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error testing working case:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>4. Conclusion</h2>\n";
echo "<p>This test verifies whether the hybrid party extraction system is working correctly for individual case detail pages.</p>\n";
echo "<p>The system should automatically extract parties from both SOAP API and decision text when viewing case details.</p>\n";
echo "<p><strong>Expected behavior:</strong> If SOAP API returns exactly 100 parties, decision text parsing should automatically trigger to find additional parties.</p>\n";
