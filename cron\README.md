# Portal Judiciar România - Cron Job System

This directory contains the background processing scripts for the case monitoring system.

## Overview

The case monitoring system uses three main cron jobs:

1. **Case Monitoring** (`monitor_cases.php`) - Checks for case changes every 30 minutes
2. **Scheduled Notifications** (`send_scheduled_notifications.php`) - Sends daily/weekly summaries at 8 AM
3. **System Monitoring** (`monitor_system.php`) - Monitors system health every hour

## Quick Setup

1. Run the setup script to get cron job commands:
   ```bash
   php cron/setup_cron.php
   ```

2. Add the recommended cron entries to your crontab:
   ```bash
   crontab -e
   ```

3. Test the setup:
   ```bash
   php cron/setup_cron.php test
   ```

## Cron Job Details

### 1. Case Monitoring (`monitor_cases.php`)

**Purpose**: Checks all monitored cases for changes and queues immediate notifications.

**Frequency**: Every 30 minutes
```bash
*/30 * * * * /usr/bin/php /path/to/just/cron/monitor_cases.php >> /path/to/just/logs/cron.log 2>&1
```

**What it does**:
- Fetches all active monitored cases from database
- Calls SOAP API to get current case data
- Compares with stored snapshots to detect changes
- Creates new snapshots when changes are detected
- Queues immediate notifications for users
- Processes notification queue
- Cleans up old data (snapshots older than 30 days)

**Performance**:
- Processes cases in batches of 10 to manage memory
- Includes 0.5 second delay between API calls
- Memory limit: 512M
- Time limit: 25 minutes (with 5-minute buffer)

### 2. Scheduled Notifications (`send_scheduled_notifications.php`)

**Purpose**: Sends daily digest and weekly summary emails.

**Frequency**: Daily at 8:00 AM
```bash
0 8 * * * /usr/bin/php /path/to/just/cron/send_scheduled_notifications.php >> /path/to/just/logs/notifications.log 2>&1
```

**What it does**:
- Sends daily digest emails (every day at 8 AM)
- Sends weekly summary emails (Mondays at 8 AM)
- Processes any remaining queued notifications
- Uses email templates with Romanian language support

### 3. System Monitoring (`monitor_system.php`)

**Purpose**: Monitors system health and sends alerts.

**Frequency**: Every hour
```bash
0 * * * * /usr/bin/php /path/to/just/cron/monitor_system.php >> /path/to/just/logs/system_monitor.log 2>&1
```

**What it does**:
- Checks database connectivity and table sizes
- Monitors cron job execution status
- Checks notification queue health
- Monitors disk space usage
- Checks log file sizes
- Monitors system performance
- Generates health reports
- Sends alerts when issues are detected

## Log Files

All cron jobs write to separate log files in the `/logs` directory:

- `cron.log` - Main case monitoring log
- `cron_errors.log` - Case monitoring errors
- `notifications.log` - Scheduled notifications log
- `notifications_errors.log` - Notification errors
- `system_monitor.log` - System monitoring log
- `system_monitor_errors.log` - System monitoring errors

## Management Commands

### Setup and Testing
```bash
# Show setup instructions
php cron/setup_cron.php

# Test cron job functionality
php cron/setup_cron.php test

# View recent logs
php cron/setup_cron.php logs

# Generate cron entry with custom frequency
php cron/setup_cron.php entry "*/15"  # Every 15 minutes
```

### Manual Execution
```bash
# Run case monitoring manually
php cron/monitor_cases.php

# Run scheduled notifications manually
php cron/send_scheduled_notifications.php

# Run system monitoring manually
php cron/monitor_system.php
```

### Log Monitoring
```bash
# Monitor case monitoring in real-time
tail -f logs/cron.log

# Check for errors
tail -f logs/cron_errors.log

# Monitor all logs
tail -f logs/*.log
```

## Troubleshooting

### Common Issues

1. **Cron jobs not running**
   - Check crontab: `crontab -l`
   - Verify PHP path: `which php`
   - Check file permissions: `ls -la cron/`
   - Test manually: `php cron/monitor_cases.php`

2. **Database connection errors**
   - Verify database credentials in `includes/config.php`
   - Check database server status
   - Test connection: `php cron/setup_cron.php test`

3. **SOAP API errors**
   - Check internet connectivity
   - Verify SOAP extension: `php -m | grep soap`
   - Check API endpoint availability

4. **Email sending issues**
   - Verify mail server configuration
   - Check PHP mail settings
   - Test email functionality

5. **High memory usage**
   - Reduce batch size in `monitor_cases.php`
   - Increase memory limit in PHP configuration
   - Clean up old data more frequently

### Performance Optimization

1. **Reduce API calls**
   - Increase monitoring interval (e.g., every hour instead of 30 minutes)
   - Implement smart scheduling based on case activity

2. **Database optimization**
   - Add indexes to frequently queried columns
   - Implement data archiving for old records
   - Use database connection pooling

3. **Memory management**
   - Process cases in smaller batches
   - Implement garbage collection between batches
   - Monitor memory usage in logs

## Security Considerations

1. **File permissions**
   - Cron scripts should be readable/executable by cron user only
   - Log files should be writable by web server and cron user
   - Database credentials should be protected

2. **Error handling**
   - All errors are logged with timestamps
   - Sensitive information is not exposed in logs
   - Failed operations are retried with exponential backoff

3. **Rate limiting**
   - API calls are rate-limited to prevent overwhelming the judicial system
   - Email sending is rate-limited to prevent spam
   - Database operations use transactions for consistency

## Monitoring and Alerts

The system includes comprehensive monitoring:

- **Health checks**: Database, API, disk space, memory usage
- **Performance metrics**: Execution time, memory usage, throughput
- **Error tracking**: Failed operations, retry attempts, success rates
- **Alerting**: Critical issues trigger immediate alerts

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review log files for errors
   - Check system performance metrics
   - Verify cron job execution

2. **Monthly**
   - Clean up old log files
   - Review database growth
   - Update system documentation

3. **Quarterly**
   - Performance optimization review
   - Security audit
   - Backup verification

### Data Retention

- Case snapshots: 30 days (configurable)
- Notification logs: 90 days (configurable)
- System logs: 30 days (manual cleanup)
- Error logs: 90 days (manual cleanup)

## Support

For issues or questions:

1. Check the logs first: `php cron/setup_cron.php logs`
2. Test the system: `php cron/setup_cron.php test`
3. Review this documentation
4. Contact the development team with specific error messages and log excerpts
