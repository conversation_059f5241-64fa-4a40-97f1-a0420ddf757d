<?php
/**
 * Debug Search Processing - TARGETED WEB INTERFACE INVESTIGATION
 * Check exact variable states that control result display
 */

echo "🔍 TARGETED WEB INTERFACE DEBUG\n";
echo "===============================\n\n";

$searchTerm = 'SARAGEA TUDORIŢA';

echo "🔎 Investigating web interface variable states for: '$searchTerm'\n";
echo "=" . str_repeat("=", 60) . "\n";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set up POST data exactly like the web form
$_POST = [
    'bulk_search_terms' => $searchTerm,
    'institutie' => '',
    'categorieInstanta' => '',
    'categorieCaz' => '',
    'dataInceput' => '',
    'dataSfarsit' => ''
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "🔧 STEP 1: Setting up environment\n";
echo "POST data: " . json_encode($_POST, JSON_UNESCAPED_UNICODE) . "\n";
echo "REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD'] . "\n\n";

// Include necessary files WITHOUT including index.php
echo "🔧 STEP 2: Including required files (WITHOUT index.php)\n";
try {
    require_once 'config/config.php';
    echo "✅ config.php loaded\n";

    require_once 'services/DosarService.php';
    echo "✅ DosarService.php loaded\n";

    require_once 'includes/functions.php';
    echo "✅ functions.php loaded\n";
} catch (Exception $e) {
    echo "❌ Error loading files: " . $e->getMessage() . "\n";
    exit(1);
}

// Define the parseBulkSearchTerms function locally to avoid including index.php
function parseBulkSearchTerms($bulkSearchTerms) {
    $terms = [];
    $lines = array_filter(array_map('trim', explode("\n", $bulkSearchTerms)));

    foreach ($lines as $line) {
        if (empty($line)) continue;

        $commaSeparated = array_filter(array_map('trim', explode(',', $line)));
        foreach ($commaSeparated as $term) {
            if (empty($term)) continue;

            // Remove quotes for exact phrase matching
            $cleanTerm = trim($term, '"\'');

            // Detect search type
            if (preg_match('/^\d+\/\d+\/\d{4}$/', $cleanTerm)) {
                $type = 'numarDosar';
            } else {
                $type = 'numeParte';
            }

            $terms[] = [
                'term' => $cleanTerm,
                'type' => $type,
                'exact_phrase' => $term !== $cleanTerm
            ];
        }
    }

    return $terms;
}

echo "\n🔧 STEP 3: Testing search functions directly (without index.php)\n";

// Test parseBulkSearchTerms function
echo "Testing parseBulkSearchTerms...\n";
try {
    $searchTermsData = parseBulkSearchTerms($searchTerm);
    echo "✅ parseBulkSearchTerms successful\n";
    echo "Parsed terms: " . json_encode($searchTermsData, JSON_UNESCAPED_UNICODE) . "\n";
} catch (Exception $e) {
    echo "❌ parseBulkSearchTerms error: " . $e->getMessage() . "\n";
}

// Test performBulkSearchWithFilters function
echo "\nTesting performBulkSearchWithFilters...\n";
try {
    $advancedFilters = [
        'institutie' => null,
        'categorieInstanta' => '',
        'categorieCaz' => '',
        'dataInceput' => '',
        'dataSfarsit' => ''
    ];

    $searchResults = performBulkSearchWithFilters($searchTermsData, $advancedFilters);
    echo "✅ performBulkSearchWithFilters successful\n";
    echo "Results count: " . count($searchResults) . "\n";

    foreach ($searchResults as $index => $result) {
        echo "  Result #" . ($index + 1) . ":\n";
        echo "    - Term: '" . $result['term'] . "'\n";
        echo "    - Type: " . $result['type'] . "\n";
        echo "    - Count: " . count($result['results']) . "\n";

        foreach ($result['results'] as $i => $dosar) {
            echo "      Case #" . ($i + 1) . ": " . $dosar->numar . " from " . $dosar->institutie . "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ performBulkSearchWithFilters error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🔧 STEP 4: CRITICAL INVESTIGATION - Web Interface Variable States\n";

// Reset variables exactly like index.php does
$bulkSearchTerms = '';
$searchResults = [];
$totalResults = 0;
$hasSearchCriteria = false;
$error = null;

echo "Variables reset to match index.php initial state\n";

// Simulate the exact processing logic from index.php
try {
    echo "Processing POST request exactly like index.php...\n";

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $bulkSearchTerms = isset($_POST['bulk_search_terms']) ? trim($_POST['bulk_search_terms']) : '';
        echo "✅ Bulk search terms: '$bulkSearchTerms'\n";

        // Advanced filters - exactly like index.php
        $advancedFilters = [
            'institutie' => isset($_POST['institutie']) ? trim($_POST['institutie']) : '',
            'categorieInstanta' => isset($_POST['categorieInstanta']) ? trim($_POST['categorieInstanta']) : '',
            'categorieCaz' => isset($_POST['categorieCaz']) ? trim($_POST['categorieCaz']) : '',
            'dataInceput' => isset($_POST['dataInceput']) ? trim($_POST['dataInceput']) : '',
            'dataSfarsit' => isset($_POST['dataSfarsit']) ? trim($_POST['dataSfarsit']) : ''
        ];

        echo "✅ Advanced filters: " . json_encode($advancedFilters, JSON_UNESCAPED_UNICODE) . "\n";

        // Check if we have search criteria - exactly like index.php
        $hasSearchCriteria = !empty($bulkSearchTerms) ||
                           !empty($advancedFilters['institutie']) ||
                           !empty($advancedFilters['categorieInstanta']) ||
                           !empty($advancedFilters['categorieCaz']) ||
                           !empty($advancedFilters['dataInceput']) ||
                           !empty($advancedFilters['dataSfarsit']);

        echo "✅ Has search criteria: " . ($hasSearchCriteria ? 'YES' : 'NO') . "\n";

        if ($hasSearchCriteria) {
            if (!empty($bulkSearchTerms)) {
                echo "✅ Processing bulk search terms...\n";

                $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
                echo "✅ Parsed " . count($searchTermsData) . " search terms\n";

                if (count($searchTermsData) > 100) {
                    $error = 'Numărul maxim de termeni este 100. Ați introdus ' . count($searchTermsData) . ' termeni.';
                    echo "❌ Too many terms error: $error\n";
                } elseif (count($searchTermsData) === 0) {
                    $error = 'Nu au fost găsiți termeni valizi de căutare.';
                    echo "❌ No valid terms error: $error\n";
                } else {
                    echo "✅ Performing bulk search with filters...\n";

                    $searchResults = performBulkSearchWithFilters($searchTermsData, $advancedFilters);
                    echo "✅ Search completed successfully\n";

                    // Calculate total results - exactly like index.php
                    $totalResults = array_sum(array_map(function($result) {
                        return count($result['results']);
                    }, $searchResults));

                    echo "✅ Total results calculated: $totalResults\n";

                    foreach ($searchResults as $index => $result) {
                        echo "  ✅ Term '" . $result['term'] . "': " . count($result['results']) . " results\n";
                    }
                }
            }
        }
    }

    echo "\n🎯 CRITICAL VARIABLE STATE ANALYSIS:\n";
    echo "=====================================\n";
    echo "- \$error: " . ($error ? "'$error'" : 'null') . "\n";
    echo "- \$hasSearchCriteria: " . ($hasSearchCriteria ? 'true' : 'false') . "\n";
    echo "- \$searchResults count: " . count($searchResults) . "\n";
    echo "- empty(\$searchResults): " . (empty($searchResults) ? 'true' : 'false') . "\n";
    echo "- \$totalResults: $totalResults\n";

    echo "\n🔍 DISPLAY CONDITION ANALYSIS:\n";
    echo "==============================\n";

    // Test the exact conditions from index.php line 4504-4510
    echo "Condition 1: if (\$error) = " . ($error ? 'TRUE' : 'FALSE') . "\n";
    echo "Condition 2: elseif (\$hasSearchCriteria && !empty(\$searchResults)) = " .
         (($hasSearchCriteria && !empty($searchResults)) ? 'TRUE' : 'FALSE') . "\n";
    echo "Condition 3: elseif (\$hasSearchCriteria && empty(\$searchResults)) = " .
         (($hasSearchCriteria && empty($searchResults)) ? 'TRUE' : 'FALSE') . "\n";

    echo "\n🚨 EXPECTED DISPLAY BEHAVIOR:\n";
    echo "=============================\n";

    if ($error) {
        echo "❌ ERROR MESSAGE should be displayed\n";
        echo "Message: $error\n";
    } elseif ($hasSearchCriteria && !empty($searchResults)) {
        echo "✅ RESULTS SECTION should be displayed\n";
        echo "Results count: " . count($searchResults) . "\n";
        echo "Total results: $totalResults\n";
    } elseif ($hasSearchCriteria && empty($searchResults)) {
        echo "ℹ️ NO RESULTS MESSAGE should be displayed\n";
        echo "Message: 'Nu au fost găsite rezultate pentru termenii de căutare specificați.'\n";
    } else {
        echo "🔍 SEARCH FORM ONLY should be displayed (no search performed)\n";
    }

} catch (Exception $e) {
    echo "❌ Processing error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🏁 Critical investigation completed.\n";
