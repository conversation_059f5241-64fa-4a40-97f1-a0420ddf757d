# Portal Judiciar - Database Schema Documentation

## Overview

This document describes the database schema for the Portal Judiciar case monitoring and email notification system. The schema is designed to support comprehensive case tracking, user management, GDPR compliance, and automated notifications.

## Database Configuration

### Connection Settings
- **Host**: localhost (configurable via `DB_HOST`)
- **Database**: portal_judiciar (configurable via `DB_NAME`)
- **Charset**: utf8mb4 with unicode collation
- **Engine**: InnoDB for ACID compliance and foreign key support

### Setup Instructions

1. **Configure Database Connection**:
   ```php
   // In src/Config/constants.php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'portal_judiciar');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

2. **Run Migrations**:
   ```bash
   # Command line
   php database/migrate.php
   
   # Or via web interface (development only)
   http://localhost/just/database/migrate.php
   ```

## Table Structure

### Core Tables

#### `users`
Stores user account information with GDPR compliance features.

**Key Fields**:
- `email`: Unique user identifier
- `password_hash`: Bcrypt hashed password
- `email_verified`: Email verification status
- `gdpr_consent`: GDPR consent tracking
- `notification_preferences`: JSON field for email preferences

**Indexes**:
- Primary: `id`
- Unique: `email`
- Performance: `email_verified`, `is_active`, `created_at`

#### `monitored_cases`
Central table for case monitoring configuration.

**Key Fields**:
- `case_number`: Case identifier from judicial system
- `institution_code`: Court/institution code
- `notification_frequency`: immediate, daily, weekly
- `last_checked`: Timestamp of last SOAP API check

**Constraints**:
- Unique constraint on `(user_id, case_number, institution_code)`
- Maximum 50 cases per user (enforced at application level)

#### `case_snapshots`
Stores historical snapshots of case data for change detection.

**Key Fields**:
- `snapshot_data`: JSON field containing complete case data
- `snapshot_hash`: SHA-256 hash for quick comparison
- `case_status`, `case_stage`: Extracted key fields for indexing

**Retention**: Snapshots older than 90 days are automatically purged.

#### `case_changes`
Records detected changes between case snapshots.

**Change Types**:
- `hearing_date`: New or modified hearing dates
- `status`: Case status changes
- `stage`: Procedural stage changes
- `parties`: Party information changes
- `judge`: Judge assignment changes
- `solution`: Case resolution/verdict
- `other`: Other significant changes

#### `notification_queue`
Email notification queue with retry logic.

**Notification Types**:
- `immediate`: Sent immediately upon change detection
- `daily_digest`: Aggregated daily summary at 8 AM
- `weekly_summary`: Weekly report sent Monday 8 AM

**Status Flow**: `pending` → `processing` → `sent` / `failed`

### Supporting Tables

#### `user_sessions`
Secure session management with IP tracking.

#### `gdpr_requests`
GDPR compliance for data export/deletion requests.

#### `system_logs`
Comprehensive system logging with structured data.

## Data Relationships

```
users (1) ←→ (N) monitored_cases
monitored_cases (1) ←→ (N) case_snapshots
monitored_cases (1) ←→ (N) case_changes
case_snapshots (1) ←→ (N) case_changes (old_snapshot_id)
case_snapshots (1) ←→ (N) case_changes (new_snapshot_id)
users (1) ←→ (N) notification_queue
monitored_cases (1) ←→ (N) notification_queue
case_changes (1) ←→ (N) notification_queue
```

## JSON Data Structures

### `users.notification_preferences`
```json
{
  "immediate_notifications": true,
  "daily_digest": true,
  "weekly_summary": false,
  "email_format": "html"
}
```

### `case_snapshots.snapshot_data`
```json
{
  "numar": "1234/2024",
  "institutie": "TribunalulBUCURESTI",
  "departament": "Civil",
  "categorieCaz": "Civil",
  "stadiuProcesual": "În curs de judecată",
  "obiect": "Acțiune în răspundere civilă",
  "parti": [
    {"nume": "POPESCU ION", "calitate": "Reclamant"},
    {"nume": "IONESCU MARIA", "calitate": "Pârât"}
  ],
  "sedinte": [
    {
      "data": "2024-07-15",
      "ora": "10:00",
      "complet": "Judecător: Georgescu Ana",
      "solutie": ""
    }
  ]
}
```

### `case_changes.change_details`
```json
{
  "old_status": "În curs de judecată",
  "new_status": "Soluționat",
  "solution": "Admite cererea",
  "timestamp": "2024-07-03 14:30:00"
}
```

## Performance Considerations

### Indexing Strategy
- **Primary Keys**: Auto-increment integers for performance
- **Foreign Keys**: All relationships properly indexed
- **Search Fields**: Case numbers, institution codes, dates
- **Status Fields**: Notification status, case activity flags

### Query Optimization
- **Composite Indexes**: User + case combinations
- **Date Ranges**: Optimized for time-based queries
- **JSON Fields**: Extracted key fields for better performance

### Maintenance Tasks
- **Snapshot Cleanup**: Remove snapshots older than 90 days
- **Log Rotation**: Archive old system logs
- **Index Optimization**: Regular ANALYZE TABLE operations

## Security Features

### Data Protection
- **Password Hashing**: Bcrypt with cost factor 10
- **SQL Injection**: Prepared statements throughout
- **XSS Prevention**: Input sanitization and output encoding

### GDPR Compliance
- **Consent Tracking**: Timestamp and IP logging
- **Data Export**: Complete user data extraction
- **Right to Deletion**: Cascading deletes with audit trail
- **Data Minimization**: Only necessary data stored

### Audit Trail
- **System Logs**: All significant actions logged
- **Change Tracking**: Complete case change history
- **User Activity**: Login/logout tracking

## Backup and Recovery

### Backup Strategy
```bash
# Daily full backup
mysqldump --single-transaction --routines --triggers \
  portal_judiciar > backup_$(date +%Y%m%d).sql

# Incremental binary log backup
mysqlbinlog --start-datetime="2024-07-03 00:00:00" \
  /var/log/mysql/mysql-bin.000001 > incremental.sql
```

### Recovery Procedures
1. **Full Restore**: Import latest full backup
2. **Point-in-Time**: Apply binary logs to specific timestamp
3. **Partial Recovery**: Table-specific restoration if needed

## Migration Management

### Version Control
- **Schema Versions**: Tracked in `schema_migrations` table
- **Migration Files**: Numbered sequentially (001_, 002_, etc.)
- **Rollback Support**: Manual rollback procedures documented

### Development Workflow
1. Create migration file with incremented number
2. Test migration on development database
3. Review schema changes in code review
4. Deploy to staging for integration testing
5. Execute on production during maintenance window

## Monitoring and Alerts

### Key Metrics
- **Queue Length**: Notification queue backlog
- **Processing Time**: Average case check duration
- **Error Rates**: Failed SOAP API calls, email delivery
- **Storage Growth**: Database size and growth rate

### Alert Thresholds
- Queue length > 1000 notifications
- Error rate > 5% over 1 hour
- Database size growth > 10% per week
- Failed email delivery > 10% over 1 hour
