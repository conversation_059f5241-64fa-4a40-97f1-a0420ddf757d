<?php
/**
 * Search for party quality information in decision text
 */

require_once 'bootstrap.php';

use App\Services\DosarService;

echo "=== SEARCHING FOR PARTY QUALITY IN DECISION TEXT ===" . PHP_EOL;
echo "Case: 130/98/2022 from TribunalulIALOMITA" . PHP_EOL;
echo "Target: SARAGEA TUDORIŢA" . PHP_EOL;
echo "Expected quality: Intervenient în numele altei persoane" . PHP_EOL;
echo PHP_EOL;

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$targetParty = 'SARAGEA TUDORIŢA';

try {
    $dosarService = new DosarService();
    
    // Get the case details using the SOAP API
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    // Use reflection to access the private executeSoapCallWithRetry method
    $reflection = new ReflectionClass($dosarService);
    $method = $reflection->getMethod('executeSoapCallWithRetry');
    $method->setAccessible(true);
    
    $response = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Test error");
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $dosar = $dosare[0];
        
        echo "=== SEARCHING DECISION TEXT FOR QUALITY PATTERNS ===" . PHP_EOL;
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            foreach ($sedinte as $sedintaIndex => $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;
                    
                    echo "--- Session " . ($sedintaIndex + 1) . " ---" . PHP_EOL;
                    
                    // Search for various quality patterns
                    $qualityPatterns = [
                        'Intervenient în numele altei persoane',
                        'Intervenient',
                        'Creditor',
                        'Debitor',
                        'Pârât',
                        'Reclamant',
                        'Appelant',
                        'Intimat'
                    ];
                    
                    $foundQualities = [];
                    
                    foreach ($qualityPatterns as $pattern) {
                        if (stripos($solutieText, $pattern) !== false) {
                            $foundQualities[] = $pattern;
                        }
                    }
                    
                    echo "Found quality patterns: " . implode(', ', $foundQualities) . PHP_EOL;
                    
                    // Search for the target party with context
                    if (stripos($solutieText, $targetParty) !== false) {
                        echo "✅ Target party found in this session" . PHP_EOL;
                        
                        // Get larger context around the party
                        $pos = stripos($solutieText, $targetParty);
                        $start = max(0, $pos - 500);
                        $length = 1000;
                        $context = substr($solutieText, $start, $length);
                        
                        echo "Extended context:" . PHP_EOL;
                        echo "..." . $context . "..." . PHP_EOL;
                        echo PHP_EOL;
                        
                        // Look for quality keywords near the party name
                        foreach ($qualityPatterns as $pattern) {
                            $patternPos = stripos($context, $pattern);
                            $partyPos = stripos($context, $targetParty);
                            
                            if ($patternPos !== false && $partyPos !== false) {
                                $distance = abs($patternPos - $partyPos);
                                if ($distance < 200) { // Within 200 characters
                                    echo "🔍 Quality '{$pattern}' found {$distance} characters from party name" . PHP_EOL;
                                }
                            }
                        }
                    }
                    
                    echo PHP_EOL;
                }
            }
        }
        
        echo "=== ANALYSIS ===" . PHP_EOL;
        echo "The decision text contains various quality patterns, but they may not be" . PHP_EOL;
        echo "directly associated with individual party names in a structured way." . PHP_EOL;
        echo "This suggests we need to implement intelligent quality extraction or" . PHP_EOL;
        echo "use a more sophisticated approach to determine party qualities." . PHP_EOL;
        
    } else {
        echo "❌ No SOAP response data" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}
