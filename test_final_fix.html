<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final Fix - Wildcard Search</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { background: #d4edda; border-left: 4px solid #28a745; color: #155724; }
        .error { background: #f8d7da; border-left: 4px solid #dc3545; color: #721c24; }
        .warning { background: #fff3cd; border-left: 4px solid #ffc107; color: #856404; }
        .info { background: #d1ecf1; border-left: 4px solid #17a2b8; color: #0c5460; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #0056b3; }
        .big-button { padding: 15px 30px; font-size: 18px; font-weight: bold; }
        code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
        .highlight { background: #fff3cd; padding: 3px 6px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Test Final Fix - Wildcard Search "14096/3/2024*"</h1>
        
        <div class="test-section success">
            <h2>✅ Fix Applied Successfully!</h2>
            <p><strong>What was fixed:</strong></p>
            <ul>
                <li>Disabled automatic restoration of exact match filter</li>
                <li>Cleared stored filter state to prevent auto-activation</li>
                <li>Explicitly unchecked the filter checkbox</li>
                <li>Ensured all results are visible by default</li>
                <li>Added comprehensive debug logging</li>
            </ul>
        </div>
        
        <div class="test-section warning">
            <h2>🎯 Expected Results</h2>
            <p>When you search for <code>14096/3/2024*</code> you should now see:</p>
            <ol>
                <li><strong>Message:</strong> "3 rezultate găsite pentru termenul '14096/3/2024*'"</li>
                <li><strong>All 3 cases visible:</strong>
                    <ul>
                        <li>14096/3/2024 (Curtea de Apel BUCUREȘTI)</li>
                        <li>14096/3/2024 (Tribunalul BUCUREȘTI)</li>
                        <li><span class="highlight">14096/3/2024*</span> (Tribunalul BUCUREȘTI) ← This should now be visible!</li>
                    </ul>
                </li>
                <li><strong>Exact match filter:</strong> Should be unchecked</li>
                <li><strong>Console messages:</strong> Should show "FILTER RESTORE: All results are now visible and filter is disabled"</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🧪 Test the Fix</h2>
            <p>Click the button below to test the search in a new tab:</p>
            <form action="index.php" method="POST" target="_blank">
                <input type="hidden" name="bulkSearchTerms" value="14096/3/2024*">
                <button type="submit" class="big-button">🔍 Test Search for "14096/3/2024*"</button>
            </form>
            
            <p style="margin-top: 20px;"><strong>Alternative test methods:</strong></p>
            <button onclick="clearAllStorage()">🗑️ Clear All Storage</button>
            <button onclick="openDevTools()">🔧 Open Developer Tools Guide</button>
        </div>
        
        <div class="test-section info">
            <h2>🔍 How to Verify the Fix</h2>
            <ol>
                <li><strong>Click the test button above</strong> to search for "14096/3/2024*"</li>
                <li><strong>Check the result count:</strong> Should say "3 rezultate găsite"</li>
                <li><strong>Count the table rows:</strong> Should see 3 rows in the table</li>
                <li><strong>Look for the asterisk case:</strong> One row should have "14096/3/2024*"</li>
                <li><strong>Check the filter:</strong> Exact match filter checkbox should be unchecked</li>
                <li><strong>Open Console (F12):</strong> Should see debug messages about filter being disabled</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🔧 Debugging Tools</h2>
            <p>If you still see issues, use these debugging commands in the browser console:</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0;">
                <p><strong>Check total rows:</strong></p>
                <code>document.querySelectorAll('table tbody tr').length</code>
                
                <p style="margin-top: 15px;"><strong>Check hidden rows:</strong></p>
                <code>document.querySelectorAll('table tbody tr[style*="display: none"]').length</code>
                
                <p style="margin-top: 15px;"><strong>Check asterisk rows:</strong></p>
                <code>document.querySelectorAll('table tbody tr[data-numar*="*"]').length</code>
                
                <p style="margin-top: 15px;"><strong>Check filter state:</strong></p>
                <code>document.getElementById('exactMatchFilter')?.checked</code>
                
                <p style="margin-top: 15px;"><strong>Check session storage:</strong></p>
                <code>sessionStorage.getItem('exactMatchFilter')</code>
            </div>
        </div>
        
        <div class="test-section" id="testResults" style="display: none;">
            <h2>📊 Test Results</h2>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        function clearAllStorage() {
            try {
                // Clear all storage
                sessionStorage.clear();
                localStorage.clear();
                
                // Show success message
                alert('✅ All browser storage cleared successfully!\n\nNow test the search to see if the issue is resolved.');
                
                console.log('All storage cleared');
            } catch (error) {
                alert('❌ Error clearing storage: ' + error.message);
                console.error('Error clearing storage:', error);
            }
        }
        
        function openDevTools() {
            alert('🔧 Developer Tools Guide:\n\n1. Press F12 to open Developer Tools\n2. Go to the Console tab\n3. Perform the search\n4. Look for messages starting with "FILTER RESTORE:"\n5. Run the debugging commands shown above\n\nThis will help identify any remaining issues.');
        }
        
        // Auto-clear storage when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded - clearing storage to ensure clean state');
            clearAllStorage();
        });
        
        // Function to test if we're in the search results page
        function checkIfSearchPage() {
            if (window.location.pathname.includes('index.php') && document.querySelector('table tbody tr')) {
                // We're on the search results page
                setTimeout(analyzeResults, 1000);
            }
        }
        
        function analyzeResults() {
            const testResults = document.getElementById('testResults');
            const resultsContent = document.getElementById('resultsContent');
            
            if (!testResults || !resultsContent) return;
            
            const totalRows = document.querySelectorAll('table tbody tr').length;
            const hiddenRows = document.querySelectorAll('table tbody tr[style*="display: none"]').length;
            const asteriskRows = document.querySelectorAll('table tbody tr[data-numar*="*"]').length;
            const filterChecked = document.getElementById('exactMatchFilter')?.checked;
            const resultMessage = document.querySelector('[id^="resultMessage"]')?.textContent;
            
            let html = '<h3>Analysis Results:</h3>';
            html += `<p><strong>Total table rows:</strong> ${totalRows}</p>`;
            html += `<p><strong>Hidden rows:</strong> ${hiddenRows}</p>`;
            html += `<p><strong>Rows with asterisk:</strong> ${asteriskRows}</p>`;
            html += `<p><strong>Filter checked:</strong> ${filterChecked}</p>`;
            html += `<p><strong>Result message:</strong> "${resultMessage}"</p>`;
            
            if (totalRows === 3 && asteriskRows === 1 && !filterChecked) {
                html += '<div class="success"><strong>✅ SUCCESS!</strong> All tests passed. The fix is working correctly.</div>';
            } else {
                html += '<div class="error"><strong>❌ ISSUE DETECTED!</strong> Some tests failed. Check the console for more details.</div>';
            }
            
            resultsContent.innerHTML = html;
            testResults.style.display = 'block';
        }
        
        // Check if we're on search page
        checkIfSearchPage();
    </script>
</body>
</html>
