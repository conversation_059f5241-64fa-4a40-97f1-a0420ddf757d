<?php
/**
 * Verification script for PDF alignment improvements
 * Tests the PDF generation with specific focus on text alignment and positioning
 */

// Include the main sedinte.php file to access PDF functions
require_once 'sedinte.php';

// Test data with various content lengths to stress-test alignment
$testSessions = [
    (object)[
        'data' => '24.06.2024',
        'ora' => '09:00',
        'departament' => 'Civil',
        'complet' => 'Judecător: Popescu Ion',
        'dosare' => [
            (object)['numar' => '1234/2024', 'institutie' => 'TribunalulBUCURESTI']
        ]
    ],
    (object)[
        'data' => '24.06.2024',
        'ora' => '14:30',
        'departament' => 'Secția Comercială și de Contencios Administrativ și Fiscal cu Denumire Foarte Lungă',
        'complet' => 'Judecător: <PERSON><PERSON>, Judecător: <PERSON><PERSON>, Grefier: <PERSON><PERSON><PERSON><PERSON> cu Funcție Suplimentară',
        'dosare' => [
            (object)['numar' => '1111/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '2222/2024', 'institutie' => 'JudecatoriaBUCURESTI'],
            (object)['numar' => '3333/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '4444/2024', 'institutie' => 'JudecatoriaBUCURESTI'],
            (object)['numar' => '5555/2024', 'institutie' => 'TribunalulBUCURESTI']
        ]
    ]
];

try {
    echo "<!DOCTYPE html>\n";
    echo "<html lang='ro'>\n";
    echo "<head>\n";
    echo "    <meta charset='UTF-8'>\n";
    echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
    echo "    <title>Verificare Alinierea PDF</title>\n";
    echo "    <style>\n";
    echo "        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n";
    echo "        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
    echo "        .success { color: #28a745; font-weight: bold; }\n";
    echo "        .error { color: #dc3545; font-weight: bold; }\n";
    echo "        .info { color: #007bff; }\n";
    echo "        .test-item { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }\n";
    echo "        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }\n";
    echo "        .btn:hover { background: #0056b3; }\n";
    echo "    </style>\n";
    echo "</head>\n";
    echo "<body>\n";
    echo "    <div class='container'>\n";
    echo "        <h1>🔍 Verificare Îmbunătățiri Alinierea PDF</h1>\n";
    
    if (isset($_GET['generate_pdf'])) {
        echo "        <div class='test-item'>\n";
        echo "            <h3>📄 Generare PDF Test...</h3>\n";
        
        try {
            // Clear any output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }
            
            $filename = 'Verificare_Aliniere_' . date('Y-m-d_H-i-s') . '.pdf';
            generateSessionPdfFile($testSessions, $filename, 'TribunalulBUCURESTI', '24.06.2024');
            exit; // This will trigger the PDF download
            
        } catch (Exception $e) {
            echo "            <p class='error'>❌ Eroare la generarea PDF: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            echo "            <p class='info'>Stack trace: " . htmlspecialchars($e->getTraceAsString()) . "</p>\n";
        }
        
        echo "        </div>\n";
    } else {
        echo "        <div class='test-item'>\n";
        echo "            <h3>🎯 Îmbunătățiri Implementate</h3>\n";
        echo "            <ul>\n";
        echo "                <li><strong>Lățimi coloane optimizate:</strong> [12, 22, 30, 40, 66]mm pentru A4 (170mm total)</li>\n";
        echo "                <li><strong>Alinierea textului:</strong> Centrat pentru Nr și Data/Ora, stânga pentru restul</li>\n";
        echo "                <li><strong>Centrare verticală:</strong> Text centrat vertical în celule pentru conținut scurt</li>\n";
        echo "                <li><strong>Separare contur/conținut:</strong> Desenare separată pentru poziționare precisă</li>\n";
        echo "                <li><strong>Calculare înălțime:</strong> Pentru toate coloanele cu conținut multi-linie</li>\n";
        echo "                <li><strong>Padding optimizat:</strong> Spațiere corectă în celule</li>\n";
        echo "            </ul>\n";
        echo "        </div>\n";
        
        echo "        <div class='test-item'>\n";
        echo "            <h3>📋 Date Test</h3>\n";
        echo "            <p><strong>Ședința 1:</strong> Conținut scurt pentru testarea centrării</p>\n";
        echo "            <p><strong>Ședința 2:</strong> Conținut lung pentru testarea text wrapping și alinierii</p>\n";
        echo "            <ul>\n";
        echo "                <li>Departament cu nume foarte lung</li>\n";
        echo "                <li>Complet cu multiple persoane și funcții</li>\n";
        echo "                <li>5 dosare programate pentru testarea listelor</li>\n";
        echo "            </ul>\n";
        echo "        </div>\n";
        
        echo "        <div style='text-align: center; margin: 30px 0;'>\n";
        echo "            <a href='?generate_pdf=1' class='btn'>📄 Generează PDF Test</a>\n";
        echo "            <a href='test_pdf_layout.php' class='btn'>🔙 Înapoi la Test Principal</a>\n";
        echo "            <a href='sedinte.php' class='btn'>🏛️ Înapoi la Ședințe</a>\n";
        echo "        </div>\n";
        
        echo "        <div class='test-item'>\n";
        echo "            <h3>✅ Verificări de Efectuat</h3>\n";
        echo "            <p>După generarea PDF-ului, verificați:</p>\n";
        echo "            <ol>\n";
        echo "                <li><strong>Alinierea orizontală:</strong> Nr și Data/Ora centrate, restul aliniat stânga</li>\n";
        echo "                <li><strong>Alinierea verticală:</strong> Text centrat vertical în celule</li>\n";
        echo "                <li><strong>Conturul celulelor:</strong> Text nu depășește marginile celulelor</li>\n";
        echo "                <li><strong>Text wrapping:</strong> Textul lung se împarte corect pe mai multe linii</li>\n";
        echo "                <li><strong>Spațierea:</strong> Padding adecvat în toate celulele</li>\n";
        echo "                <li><strong>Diacritice:</strong> Caracterele românești (ă, â, î, ș, ț) afișate corect</li>\n";
        echo "            </ol>\n";
        echo "        </div>\n";
    }
    
    echo "    </div>\n";
    echo "</body>\n";
    echo "</html>\n";
    
} catch (Exception $e) {
    echo "Eroare critică: " . htmlspecialchars($e->getMessage());
}
?>
