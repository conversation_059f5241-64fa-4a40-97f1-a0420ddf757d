<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final Debug - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-output {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 15px;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .test-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug me-2"></i>
            Test Final Debug - Expandează/Restrânge Toate
        </h1>
        
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>Instrucțiuni</h5>
            <ol>
                <li>Acest test simulează exact structura din index.php</li>
                <li>Apasă butoanele pentru a testa funcționalitatea</li>
                <li>Verifică output-ul debug pentru erori</li>
                <li>Compară cu comportamentul din index.php</li>
            </ol>
        </div>
        
        <!-- Test Buttons (exact ca în index.php) -->
        <div class="test-section">
            <h5>Butoane Test (ca în index.php)</h5>
            <div class="text-center">
                <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="expandAllResults()">
                    <i class="fas fa-expand-alt me-1"></i>
                    Expandează toate
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllResults()">
                    <i class="fas fa-compress-alt me-1"></i>
                    Restrânge toate
                </button>
            </div>
        </div>
        
        <!-- Test Elements -->
        <div class="test-section">
            <h5>Elemente Test (simulează rezultatele căutării)</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header" onclick="toggleTermResults(0)" style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>Termen 1</span>
                                <i class="fas fa-chevron-down" id="toggleIcon0"></i>
                            </div>
                        </div>
                        <div id="termContent0" class="card-body" style="display: none;">
                            <p>Rezultate pentru termenul 1</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header" onclick="toggleTermResults(1)" style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>Termen 2</span>
                                <i class="fas fa-chevron-down" id="toggleIcon1"></i>
                            </div>
                        </div>
                        <div id="termContent1" class="card-body" style="display: none;">
                            <p>Rezultate pentru termenul 2</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header" onclick="toggleTermResults(2)" style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>Termen 3</span>
                                <i class="fas fa-chevron-down" id="toggleIcon2"></i>
                            </div>
                        </div>
                        <div id="termContent2" class="card-body" style="display: none;">
                            <p>Rezultate pentru termenul 3</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Debug Output -->
        <div class="test-section">
            <h5>Debug Output</h5>
            <div id="debugOutput" class="debug-output">Inițializare debug...\n</div>
            <div class="mt-2">
                <button class="btn btn-sm btn-secondary" onclick="clearDebug()">
                    <i class="fas fa-trash me-1"></i>
                    Șterge Debug
                </button>
                <button class="btn btn-sm btn-info" onclick="runDiagnostics()">
                    <i class="fas fa-stethoscope me-1"></i>
                    Rulează Diagnostice
                </button>
            </div>
        </div>
        
        <!-- Manual Test Buttons -->
        <div class="test-section">
            <h5>Test Manual Funcții</h5>
            <div class="text-center">
                <button class="btn btn-primary me-2" onclick="testFunction('expandAllResults')">
                    Test expandAllResults()
                </button>
                <button class="btn btn-secondary me-2" onclick="testFunction('collapseAllResults')">
                    Test collapseAllResults()
                </button>
                <button class="btn btn-info me-2" onclick="testFunction('toggleTermResults', 0)">
                    Test toggleTermResults(0)
                </button>
                <button class="btn btn-success" onclick="testFunction('showNotification', 'Test message', 'info')">
                    Test showNotification()
                </button>
            </div>
        </div>
    </div>
    
    <!-- Notification Container (exact ca în index.php) -->
    <div id="notificationContainer" class="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050; display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Debug function
        function debugLog(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearDebug() {
            document.getElementById('debugOutput').textContent = 'Debug cleared...\n';
        }
        
        // EXACT SAME FUNCTIONS AS IN INDEX.PHP
        
        /**
         * Show notification message - GLOBAL FUNCTION
         */
        function showNotification(message, type = 'info') {
            debugLog(`showNotification called: "${message}", type: ${type}`);
            
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');

            if (!container || !notification) {
                debugLog('ERROR: Notification elements not found');
                console.error('Notification elements not found');
                return;
            }

            // Icon mapping
            const iconMap = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };

            const icon = iconMap[type] || 'info-circle';

            // Set notification content and style
            notification.className = `alert alert-${type}`;
            notification.innerHTML = `<i class="fas fa-${icon} me-2"></i>${message}`;

            // Show notification
            container.style.display = 'block';
            debugLog('Notification displayed successfully');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                container.style.display = 'none';
                debugLog('Notification auto-hidden');
            }, 5000);
        }

        /**
         * Toggle term results visibility - GLOBAL FUNCTION
         */
        function toggleTermResults(index) {
            debugLog(`toggleTermResults called with index: ${index}`);
            
            try {
                const content = document.getElementById('termContent' + index);
                const icon = document.getElementById('toggleIcon' + index);

                debugLog(`Content element: ${content ? 'FOUND' : 'NOT FOUND'}`);
                debugLog(`Icon element: ${icon ? 'FOUND' : 'NOT FOUND'}`);

                if (!content) {
                    debugLog('ERROR: Content element not found');
                    showNotification('Eroare: Secțiunea nu a fost găsită.', 'danger');
                    return;
                }

                const isVisible = content.style.display !== 'none';
                debugLog(`Current visibility: ${isVisible ? 'VISIBLE' : 'HIDDEN'}`);

                if (isVisible) {
                    content.style.display = 'none';
                    if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                    debugLog('Section collapsed');
                } else {
                    content.style.display = 'block';
                    if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                    debugLog('Section expanded');
                }

            } catch (error) {
                debugLog(`ERROR in toggleTermResults: ${error.message}`);
                console.error('Error in toggleTermResults:', error);
                showNotification('Eroare la comutarea secțiunii.', 'danger');
            }
        }

        /**
         * Expand all results function - GLOBAL FUNCTION
         */
        function expandAllResults() {
            debugLog('expandAllResults called');
            
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                debugLog(`Found ${termContents.length} content elements and ${toggleIcons.length} icon elements`);

                if (termContents.length === 0) {
                    debugLog('WARNING: No content elements found');
                    showNotification('Nu există secțiuni de rezultate pentru expandare.', 'warning');
                    return;
                }

                termContents.forEach((content, index) => {
                    content.style.display = 'block';
                    debugLog(`Expanded content ${index}`);
                });

                toggleIcons.forEach((icon, index) => {
                    icon.className = 'fas fa-chevron-up toggle-icon';
                    debugLog(`Updated icon ${index}`);
                });

                showNotification('Toate secțiunile au fost expandate.', 'info');
                debugLog('All sections expanded successfully');

            } catch (error) {
                debugLog(`ERROR in expandAllResults: ${error.message}`);
                console.error('Error in expandAllResults:', error);
                showNotification('Eroare la expandarea rezultatelor.', 'danger');
            }
        }

        /**
         * Collapse all results function - GLOBAL FUNCTION
         */
        function collapseAllResults() {
            debugLog('collapseAllResults called');
            
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                debugLog(`Found ${termContents.length} content elements and ${toggleIcons.length} icon elements`);

                if (termContents.length === 0) {
                    debugLog('WARNING: No content elements found');
                    showNotification('Nu există secțiuni de rezultate pentru restrângere.', 'warning');
                    return;
                }

                termContents.forEach((content, index) => {
                    content.style.display = 'none';
                    debugLog(`Collapsed content ${index}`);
                });

                toggleIcons.forEach((icon, index) => {
                    icon.className = 'fas fa-chevron-down toggle-icon';
                    debugLog(`Updated icon ${index}`);
                });

                showNotification('Toate secțiunile au fost restrânse.', 'info');
                debugLog('All sections collapsed successfully');

            } catch (error) {
                debugLog(`ERROR in collapseAllResults: ${error.message}`);
                console.error('Error in collapseAllResults:', error);
                showNotification('Eroare la restrângerea rezultatelor.', 'danger');
            }
        }
        
        // Test functions
        function testFunction(funcName, ...args) {
            debugLog(`Testing function: ${funcName} with args: ${JSON.stringify(args)}`);
            
            try {
                if (typeof window[funcName] === 'function') {
                    window[funcName](...args);
                    debugLog(`SUCCESS: ${funcName} executed`);
                } else {
                    debugLog(`ERROR: ${funcName} is not a function`);
                }
            } catch (error) {
                debugLog(`ERROR testing ${funcName}: ${error.message}`);
            }
        }
        
        function runDiagnostics() {
            debugLog('=== RUNNING DIAGNOSTICS ===');
            
            // Test function existence
            const functions = ['showNotification', 'expandAllResults', 'collapseAllResults', 'toggleTermResults'];
            functions.forEach(func => {
                const exists = typeof window[func] === 'function';
                debugLog(`Function ${func}: ${exists ? 'EXISTS' : 'MISSING'}`);
            });
            
            // Test DOM elements
            const termContents = document.querySelectorAll('[id^="termContent"]');
            const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
            debugLog(`DOM elements - termContent: ${termContents.length}, toggleIcon: ${toggleIcons.length}`);
            
            // Test notification container
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');
            debugLog(`Notification elements - container: ${container ? 'EXISTS' : 'MISSING'}, notification: ${notification ? 'EXISTS' : 'MISSING'}`);
            
            debugLog('=== DIAGNOSTICS COMPLETE ===');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM loaded, initializing...');
            
            // Run initial diagnostics
            setTimeout(runDiagnostics, 100);
            
            debugLog('Initialization complete');
        });
        
        // Global error handler
        window.addEventListener('error', function(e) {
            debugLog(`GLOBAL ERROR: ${e.message} at ${e.filename}:${e.lineno}`);
        });
        
        debugLog('Script loaded successfully');
    </script>
</body>
</html>
