<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #2c3e50;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .content-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .user-status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active {
            background-color: var(--success-color);
            color: white;
        }
        
        .status-suspended {
            background-color: var(--warning-color);
            color: black;
        }
        
        .status-deleted {
            background-color: var(--danger-color);
            color: white;
        }
        
        .role-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .role-super_admin {
            background-color: var(--danger-color);
            color: white;
        }
        
        .role-admin {
            background-color: var(--warning-color);
            color: black;
        }
        
        .role-moderator {
            background-color: var(--info-color);
            color: white;
        }
        
        .role-viewer {
            background-color: var(--success-color);
            color: white;
        }
        
        .user-table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }
        
        .user-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .action-btn {
            padding: 0.25rem 0.5rem;
            margin: 0.1rem;
            border: none;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
        }
        
        .search-container {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        Gestionare Utilizatori
                    </h1>
                    <p class="mb-0 opacity-75">Portal Judiciar România</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="index.php" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-1"></i>
                        Înapoi la Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Search and Filters -->
        <div class="search-container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="userSearch" 
                               placeholder="Căutare utilizatori (nume, email)...">
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" id="filterAll">
                            Toți ({{ total_users }})
                        </button>
                        <button type="button" class="btn btn-outline-success" id="filterActive">
                            Activi
                        </button>
                        <button type="button" class="btn btn-outline-warning" id="filterSuspended">
                            Suspendați
                        </button>
                        <button type="button" class="btn btn-outline-danger" id="filterDeleted">
                            Șterși
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Management Controls -->
        <div class="content-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    Gestionare Utilizatori
                </h5>
                <button class="btn btn-primary" onclick="showCreateUserModal()">
                    <i class="fas fa-plus me-2"></i>
                    Adaugă Utilizator
                </button>
            </div>

            <!-- Search and Filters -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="userSearch"
                               placeholder="Căutare după nume, email...">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary active" id="filterAll">
                            Toți
                        </button>
                        <button type="button" class="btn btn-outline-success" id="filterActive">
                            Activi
                        </button>
                        <button type="button" class="btn btn-outline-warning" id="filterSuspended">
                            Suspendați
                        </button>
                        <button type="button" class="btn btn-outline-danger" id="filterDeleted">
                            Șterși
                        </button>
                    </div>
                </div>
            </div>

            <div class="text-muted mb-3">
                <small id="userCount">{{ users|length }} utilizatori afișați</small>
            </div>
            
            <div class="table-responsive">
                <table class="table user-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Utilizator</th>
                            <th>Email</th>
                            <th>Rol</th>
                            <th>Status</th>
                            <th>Dosare</th>
                            <th>Ultima Conectare</th>
                            <th>Acțiuni</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        {% for user in users %}
                        <tr data-user-id="{{ user.id }}">
                            <td>{{ user.id }}</td>
                            <td>
                                <strong>{{ user.first_name }} {{ user.last_name }}</strong>
                                {% if user.email_verified %}
                                <i class="fas fa-check-circle text-success ms-1" title="Email verificat"></i>
                                {% else %}
                                <i class="fas fa-exclamation-circle text-warning ms-1" title="Email neverificat"></i>
                                {% endif %}
                            </td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.admin_role %}
                                <span class="role-badge role-{{ user.admin_role }}">{{ user.admin_role }}</span>
                                {% else %}
                                <span class="text-muted">Utilizator</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.deleted_at %}
                                <span class="user-status-badge status-deleted">Șters</span>
                                {% elseif user.locked_until and user.locked_until > 'now'|date('Y-m-d H:i:s') %}
                                <span class="user-status-badge status-suspended">Suspendat</span>
                                {% else %}
                                <span class="user-status-badge status-active">Activ</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ user.cases_count ?? 0 }}</span>
                            </td>
                            <td>
                                {% if user.last_login_at %}
                                <small>{{ user.last_login_at|date('d.m.Y H:i') }}</small>
                                {% else %}
                                <small class="text-muted">Niciodată</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-info"
                                            onclick="viewUserDetails({{ user.id }})" title="Detalii">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="editUser({{ user.id }})" title="Editează">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% if user.deleted_at %}
                                    <button class="btn btn-sm btn-outline-success"
                                            onclick="toggleUserStatus({{ user.id }}, 'active')" title="Reactivează">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    {% elseif user.locked_until and user.locked_until > 'now'|date('Y-m-d H:i:s') %}
                                    <button class="btn btn-sm btn-outline-success"
                                            onclick="toggleUserStatus({{ user.id }}, 'active')" title="Activează">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    {% else %}
                                    <button class="btn btn-sm btn-outline-warning"
                                            onclick="toggleUserStatus({{ user.id }}, 'suspended')" title="Suspendă">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    {% endif %}
                                    {% if user.id != current_user_id %}
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteUser({{ user.id }})" title="Șterge">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-secondary"
                                            onclick="exportUserData({{ user.id }})" title="Export GDPR">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <nav aria-label="User pagination" id="userPagination">
                <ul class="pagination justify-content-center">
                    <!-- Pagination will be populated by JavaScript -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user me-2"></i>
                        Detalii Utilizator
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="userDetailsContent">
                    <!-- Content will be loaded by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>
                        Adaugă Utilizator Nou
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="createUserForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createEmail" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="createEmail" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createPassword" class="form-label">Parolă *</label>
                                    <input type="password" class="form-control" id="createPassword" name="password" required minlength="8">
                                    <div class="form-text">Minim 8 caractere</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createFirstName" class="form-label">Prenume *</label>
                                    <input type="text" class="form-control" id="createFirstName" name="first_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createLastName" class="form-label">Nume *</label>
                                    <input type="text" class="form-control" id="createLastName" name="last_name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createPhone" class="form-label">Telefon</label>
                                    <input type="tel" class="form-control" id="createPhone" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="createAdminRole" class="form-label">Rol Administrator</label>
                                    <select class="form-select" id="createAdminRole" name="admin_role">
                                        <option value="">Utilizator obișnuit</option>
                                        {% for role_key, role_name in admin_roles %}
                                        <option value="{{ role_key }}">{{ role_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="createEmailVerified" name="email_verified" checked>
                                <label class="form-check-label" for="createEmailVerified">
                                    Email verificat
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Creează Utilizator
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-edit me-2"></i>
                        Editează Utilizator
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="user_id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editEmail" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="editEmail" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editNewPassword" class="form-label">Parolă Nouă</label>
                                    <input type="password" class="form-control" id="editNewPassword" name="new_password" minlength="8">
                                    <div class="form-text">Lasă gol pentru a păstra parola actuală</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editFirstName" class="form-label">Prenume *</label>
                                    <input type="text" class="form-control" id="editFirstName" name="first_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editLastName" class="form-label">Nume *</label>
                                    <input type="text" class="form-control" id="editLastName" name="last_name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editPhone" class="form-label">Telefon</label>
                                    <input type="tel" class="form-control" id="editPhone" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editAdminRole" class="form-label">Rol Administrator</label>
                                    <select class="form-select" id="editAdminRole" name="admin_role">
                                        <option value="">Utilizator obișnuit</option>
                                        {% for role_key, role_name in admin_roles %}
                                        <option value="{{ role_key }}">{{ role_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editEmailVerified" name="email_verified">
                                <label class="form-check-label" for="editEmailVerified">
                                    Email verificat
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Actualizează Utilizator
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                        Confirmare Ștergere
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Sunteți sigur că doriți să ștergeți acest utilizator?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Atenție:</strong> Această acțiune va marca utilizatorul ca șters (soft delete)
                        în conformitate cu GDPR. Datele vor rămâne în sistem pentru audit, dar utilizatorul
                        nu va mai putea accesa contul.
                    </div>
                    <div id="deleteUserInfo" class="border p-3 rounded bg-light">
                        <!-- User info will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteUser">
                        <i class="fas fa-trash me-2"></i>
                        Șterge Utilizator
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // CSRF tokens and configuration
        const csrfTokens = {{ csrf_tokens|json_encode|raw }};
        const currentUserId = {{ current_user_id }};
        let currentPage = 1;
        let currentFilter = 'all';
        let currentSearch = '';
        
        // Search functionality
        document.getElementById('userSearch').addEventListener('input', function() {
            currentSearch = this.value;
            currentPage = 1;
            loadUsers();
        });
        
        // Filter buttons
        document.getElementById('filterAll').addEventListener('click', () => setFilter('all'));
        document.getElementById('filterActive').addEventListener('click', () => setFilter('active'));
        document.getElementById('filterSuspended').addEventListener('click', () => setFilter('suspended'));
        document.getElementById('filterDeleted').addEventListener('click', () => setFilter('deleted'));
        
        function setFilter(filter) {
            currentFilter = filter;
            currentPage = 1;
            
            // Update button states
            document.querySelectorAll('[id^="filter"]').forEach(btn => btn.classList.remove('active'));
            document.getElementById('filter' + filter.charAt(0).toUpperCase() + filter.slice(1)).classList.add('active');
            
            loadUsers();
        }
        
        // Load users via AJAX
        function loadUsers() {
            const formData = new FormData();
            formData.append('action', 'get_users_list');
            formData.append('page', currentPage);
            formData.append('search', currentSearch);
            formData.append('filter', currentFilter);
            formData.append('csrf_token', csrfTokens.get_users_list);
            
            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateUsersTable(data.data.users);
                    updatePagination(data.data.page, data.data.pages);
                    document.getElementById('userCount').textContent = data.data.total + ' utilizatori găsiți';
                } else {
                    showNotification('Eroare la încărcarea utilizatorilor: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Eroare de conexiune', 'danger');
            });
        }
        
        // View user details
        function viewUserDetails(userId) {
            const formData = new FormData();
            formData.append('action', 'get_user_details');
            formData.append('user_id', userId);
            formData.append('csrf_token', csrfTokens.get_user_details);
            
            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayUserDetails(data.data);
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            });
        }
        
        // Toggle user status
        function toggleUserStatus(userId, status) {
            if (!confirm('Sigur doriți să schimbați statusul acestui utilizator?')) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'toggle_user_status');
            formData.append('user_id', userId);
            formData.append('status', status);
            formData.append('csrf_token', csrfTokens.toggle_user_status);
            
            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Status utilizator actualizat cu succes', 'success');
                    loadUsers(); // Refresh the table
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            });
        }
        
        // Export user data
        function exportUserData(userId) {
            if (!confirm('Doriți să exportați datele acestui utilizator (GDPR)?')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'export_user_data');
            formData.append('user_id', userId);
            formData.append('csrf_token', csrfTokens.export_user_data);

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Create and download JSON file
                    const blob = new Blob([JSON.stringify(data.data, null, 2)], {type: 'application/json'});
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `user_data_${userId}_${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    window.URL.revokeObjectURL(url);

                    showNotification('Date utilizator exportate cu succes', 'success');
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Export error:', error);
                showNotification('Eroare la exportul datelor', 'danger');
            });
        }

        // Show create user modal
        function showCreateUserModal() {
            const modal = new bootstrap.Modal(document.getElementById('createUserModal'));
            document.getElementById('createUserForm').reset();
            modal.show();
        }

        // Handle create user form submission
        document.getElementById('createUserForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'create_user');
            formData.append('csrf_token', csrfTokens.create_user);

            // Disable submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se creează...';

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message || 'Utilizator creat cu succes', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('createUserModal')).hide();
                    loadUsers(); // Refresh the table
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Eroare de conexiune', 'danger');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // Edit user
        function editUser(userId) {
            // First get user details
            const formData = new FormData();
            formData.append('action', 'get_user_details');
            formData.append('user_id', userId);
            formData.append('csrf_token', csrfTokens.get_user_details);

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    populateEditForm(data.data);
                    const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
                    modal.show();
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            });
        }

        // Populate edit form with user data
        function populateEditForm(user) {
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editEmail').value = user.email;
            document.getElementById('editFirstName').value = user.first_name;
            document.getElementById('editLastName').value = user.last_name;
            document.getElementById('editPhone').value = user.phone || '';
            document.getElementById('editAdminRole').value = user.admin_role || '';
            document.getElementById('editEmailVerified').checked = user.email_verified == 1;
            document.getElementById('editNewPassword').value = ''; // Always empty for security
        }

        // Handle edit user form submission
        document.getElementById('editUserForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'update_user');
            formData.append('csrf_token', csrfTokens.update_user);

            // Disable submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se actualizează...';

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message || 'Utilizator actualizat cu succes', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                    loadUsers(); // Refresh the table
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Eroare de conexiune', 'danger');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // Delete user
        function deleteUser(userId) {
            // Get user details for confirmation
            const formData = new FormData();
            formData.append('action', 'get_user_details');
            formData.append('user_id', userId);
            formData.append('csrf_token', csrfTokens.get_user_details);

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showDeleteConfirmation(data.data);
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            });
        }

        // Show delete confirmation modal
        function showDeleteConfirmation(user) {
            document.getElementById('deleteUserInfo').innerHTML = `
                <strong>${user.first_name} ${user.last_name}</strong><br>
                <small class="text-muted">${user.email}</small><br>
                <small>Înregistrat: ${user.created_at}</small>
            `;

            const modal = new bootstrap.Modal(document.getElementById('deleteUserModal'));

            // Set up confirm button
            document.getElementById('confirmDeleteUser').onclick = function() {
                performUserDeletion(user.id);
            };

            modal.show();
        }

        // Perform actual user deletion
        function performUserDeletion(userId) {
            const formData = new FormData();
            formData.append('action', 'delete_user');
            formData.append('user_id', userId);
            formData.append('csrf_token', csrfTokens.delete_user);

            const confirmBtn = document.getElementById('confirmDeleteUser');
            const originalText = confirmBtn.innerHTML;
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se șterge...';

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message || 'Utilizator șters cu succes', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('deleteUserModal')).hide();
                    loadUsers(); // Refresh the table
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Eroare de conexiune', 'danger');
            })
            .finally(() => {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalText;
            });
        }

        // Helper functions
        function updateUsersTable(users) {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.setAttribute('data-user-id', user.id);

                // Determine status
                let statusBadge = '';
                let statusActions = '';

                if (user.deleted_at) {
                    statusBadge = '<span class="user-status-badge status-deleted">Șters</span>';
                    statusActions = `
                        <button class="btn btn-sm btn-outline-success"
                                onclick="toggleUserStatus(${user.id}, 'active')" title="Reactivează">
                            <i class="fas fa-undo"></i>
                        </button>`;
                } else if (user.locked_until && new Date(user.locked_until) > new Date()) {
                    statusBadge = '<span class="user-status-badge status-suspended">Suspendat</span>';
                    statusActions = `
                        <button class="btn btn-sm btn-outline-success"
                                onclick="toggleUserStatus(${user.id}, 'active')" title="Activează">
                            <i class="fas fa-play"></i>
                        </button>`;
                } else {
                    statusBadge = '<span class="user-status-badge status-active">Activ</span>';
                    statusActions = `
                        <button class="btn btn-sm btn-outline-warning"
                                onclick="toggleUserStatus(${user.id}, 'suspended')" title="Suspendă">
                            <i class="fas fa-pause"></i>
                        </button>`;
                }

                // Role badge
                let roleBadge = '';
                if (user.admin_role) {
                    roleBadge = `<span class="role-badge role-${user.admin_role}">${user.admin_role}</span>`;
                } else {
                    roleBadge = '<span class="text-muted">Utilizator</span>';
                }

                // Email verification icon
                const emailIcon = user.email_verified == 1
                    ? '<i class="fas fa-check-circle text-success ms-1" title="Email verificat"></i>'
                    : '<i class="fas fa-exclamation-circle text-warning ms-1" title="Email neverificat"></i>';

                // Last login
                const lastLogin = user.last_login_at
                    ? new Date(user.last_login_at).toLocaleDateString('ro-RO') + ' ' + new Date(user.last_login_at).toLocaleTimeString('ro-RO', {hour: '2-digit', minute: '2-digit'})
                    : '<small class="text-muted">Niciodată</small>';

                // Delete button (only if not current user)
                const deleteButton = user.id != currentUserId
                    ? `<button class="btn btn-sm btn-outline-danger"
                              onclick="deleteUser(${user.id})" title="Șterge">
                          <i class="fas fa-trash"></i>
                       </button>`
                    : '';

                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>
                        <strong>${user.first_name} ${user.last_name}</strong>
                        ${emailIcon}
                    </td>
                    <td>${user.email}</td>
                    <td>${roleBadge}</td>
                    <td>${statusBadge}</td>
                    <td><span class="badge bg-info">${user.cases_count || 0}</span></td>
                    <td><small>${lastLogin}</small></td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-info"
                                    onclick="viewUserDetails(${user.id})" title="Detalii">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="editUser(${user.id})" title="Editează">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${statusActions}
                            ${deleteButton}
                            <button class="btn btn-sm btn-outline-secondary"
                                    onclick="exportUserData(${user.id})" title="Export GDPR">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        function updatePagination(page, totalPages) {
            const pagination = document.querySelector('#userPagination .pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) return;

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${page === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `
                <a class="page-link" href="#" onclick="changePage(${page - 1}); return false;">
                    <i class="fas fa-chevron-left"></i>
                </a>
            `;
            pagination.appendChild(prevLi);

            // Page numbers
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(totalPages, page + 2);

            if (startPage > 1) {
                const firstLi = document.createElement('li');
                firstLi.className = 'page-item';
                firstLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(1); return false;">1</a>`;
                pagination.appendChild(firstLi);

                if (startPage > 2) {
                    const dotsLi = document.createElement('li');
                    dotsLi.className = 'page-item disabled';
                    dotsLi.innerHTML = '<span class="page-link">...</span>';
                    pagination.appendChild(dotsLi);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === page ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>`;
                pagination.appendChild(li);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const dotsLi = document.createElement('li');
                    dotsLi.className = 'page-item disabled';
                    dotsLi.innerHTML = '<span class="page-link">...</span>';
                    pagination.appendChild(dotsLi);
                }

                const lastLi = document.createElement('li');
                lastLi.className = 'page-item';
                lastLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a>`;
                pagination.appendChild(lastLi);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${page === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `
                <a class="page-link" href="#" onclick="changePage(${page + 1}); return false;">
                    <i class="fas fa-chevron-right"></i>
                </a>
            `;
            pagination.appendChild(nextLi);
        }

        function changePage(page) {
            currentPage = page;
            loadUsers();
        }

        // View user details
        function viewUserDetails(userId) {
            // Get user details
            const formData = new FormData();
            formData.append('action', 'get_user_details');
            formData.append('user_id', userId);
            formData.append('csrf_token', csrfTokens.get_user_details);

            fetch('users.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showUserDetailsModal(data.data);
                } else {
                    showNotification('Eroare: ' + data.error, 'danger');
                }
            });
        }

        // Show user details modal (placeholder)
        function showUserDetailsModal(user) {
            alert(`Detalii utilizator:\nNume: ${user.first_name} ${user.last_name}\nEmail: ${user.email}\nRol: ${user.admin_role || 'Utilizator'}\nCazuri monitorizate: ${user.cases_count || 0}`);
        }


        
        function displayUserDetails(user) {
            // Implementation for user details modal
            const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
            document.getElementById('userDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informații Generale</h6>
                        <p><strong>Nume:</strong> ${user.first_name} ${user.last_name}</p>
                        <p><strong>Email:</strong> ${user.email}</p>
                        <p><strong>Înregistrat:</strong> ${user.created_at}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Activitate</h6>
                        <p><strong>Dosare monitorizate:</strong> ${user.monitored_cases_count || 0}</p>
                        <p><strong>Notificări trimise:</strong> ${user.notifications_sent || 0}</p>
                        <p><strong>Ultima verificare:</strong> ${user.last_case_check || 'Niciodată'}</p>
                    </div>
                </div>
            `;
            modal.show();
        }
        
        function showNotification(message, type) {
            // Simple notification system
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
