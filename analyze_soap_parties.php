<?php
/**
 * Detailed SOAP Response Analysis for Parties Count Investigation
 * This script analyzes the raw SOAP response to understand party limitations
 */

require_once 'config/config.php';

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "=== SOAP PARTIES ANALYSIS ===" . PHP_EOL;
echo "Case: $numarDosar" . PHP_EOL;
echo "Institution: $institutie" . PHP_EOL;
echo "Date: " . date('Y-m-d H:i:s') . PHP_EOL;
echo PHP_EOL;

try {
    // Create SOAP client with detailed tracing
    $wsdl = SOAP_WSDL;
    $options = [
        'soap_version' => SOAP_1_2,
        'trace' => true,
        'exceptions' => true,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'connection_timeout' => 30,
        'features' => SOAP_SINGLE_ELEMENT_ARRAYS
    ];
    
    echo "Creating SOAP client..." . PHP_EOL;
    $client = new SoapClient($wsdl, $options);
    
    // Prepare request parameters
    $params = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "Making SOAP call with parameters:" . PHP_EOL;
    echo "  numarDosar: $numarDosar" . PHP_EOL;
    echo "  institutie: $institutie" . PHP_EOL;
    echo PHP_EOL;
    
    // Make the SOAP call
    $startTime = microtime(true);
    $response = $client->CautareDosare2($params);
    $endTime = microtime(true);
    
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    echo "SOAP call completed in {$responseTime}ms" . PHP_EOL;
    echo PHP_EOL;
    
    // Get raw request and response
    $lastRequest = $client->__getLastRequest();
    $lastResponse = $client->__getLastResponse();
    
    echo "=== RAW RESPONSE SIZE ANALYSIS ===" . PHP_EOL;
    echo "Request size: " . strlen($lastRequest) . " bytes" . PHP_EOL;
    echo "Response size: " . strlen($lastResponse) . " bytes" . PHP_EOL;
    echo PHP_EOL;
    
    // Analyze response structure
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        
        // Handle single vs multiple cases
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        echo "=== CASE ANALYSIS ===" . PHP_EOL;
        echo "Number of cases found: " . count($dosare) . PHP_EOL;
        
        foreach ($dosare as $index => $dosar) {
            echo PHP_EOL . "--- Case " . ($index + 1) . " ---" . PHP_EOL;
            echo "Number: " . ($dosar->numar ?? 'N/A') . PHP_EOL;
            echo "Institution: " . ($dosar->institutie ?? 'N/A') . PHP_EOL;
            
            // Analyze parties structure
            if (isset($dosar->parti)) {
                echo "Parties object exists: YES" . PHP_EOL;
                
                if (isset($dosar->parti->DosarParte)) {
                    $parti = $dosar->parti->DosarParte;
                    
                    // Handle single vs multiple parties
                    if (!is_array($parti)) {
                        $parti = [$parti];
                    }
                    
                    $partiesCount = count($parti);
                    echo "Parties count: $partiesCount" . PHP_EOL;
                    
                    // Check if this is exactly 100 (potential limit indicator)
                    if ($partiesCount == 100) {
                        echo "⚠ WARNING: Exactly 100 parties found - possible API limit!" . PHP_EOL;
                    }
                    
                    // Analyze first few and last few parties
                    echo PHP_EOL . "First 5 parties:" . PHP_EOL;
                    for ($i = 0; $i < min(5, $partiesCount); $i++) {
                        $parte = $parti[$i];
                        echo "  " . ($i + 1) . ". " . ($parte->nume ?? 'N/A') . 
                             " (" . ($parte->calitate ?? 'N/A') . ")" . PHP_EOL;
                    }
                    
                    if ($partiesCount > 10) {
                        echo "  ..." . PHP_EOL;
                        echo "Last 5 parties:" . PHP_EOL;
                        for ($i = max(0, $partiesCount - 5); $i < $partiesCount; $i++) {
                            $parte = $parti[$i];
                            echo "  " . ($i + 1) . ". " . ($parte->nume ?? 'N/A') . 
                                 " (" . ($parte->calitate ?? 'N/A') . ")" . PHP_EOL;
                        }
                    }
                    
                    // Look for pagination indicators in the response
                    echo PHP_EOL . "=== PAGINATION ANALYSIS ===" . PHP_EOL;
                    
                    // Check if there are any pagination-related fields
                    $responseArray = json_decode(json_encode($response), true);
                    $paginationFields = [];
                    
                    // Recursively search for pagination-related fields
                    function findPaginationFields($data, $path = '') {
                        global $paginationFields;
                        
                        if (is_array($data)) {
                            foreach ($data as $key => $value) {
                                $currentPath = $path ? $path . '.' . $key : $key;
                                
                                // Look for pagination-related field names
                                if (preg_match('/page|limit|offset|total|count|next|prev|more|has/i', $key)) {
                                    $paginationFields[$currentPath] = $value;
                                }
                                
                                if (is_array($value) || is_object($value)) {
                                    findPaginationFields($value, $currentPath);
                                }
                            }
                        }
                    }
                    
                    findPaginationFields($responseArray);
                    
                    if (!empty($paginationFields)) {
                        echo "Potential pagination fields found:" . PHP_EOL;
                        foreach ($paginationFields as $field => $value) {
                            echo "  $field: " . (is_scalar($value) ? $value : gettype($value)) . PHP_EOL;
                        }
                    } else {
                        echo "No obvious pagination fields found in response" . PHP_EOL;
                    }
                    
                } else {
                    echo "No DosarParte found in parties object" . PHP_EOL;
                }
            } else {
                echo "No parties object found" . PHP_EOL;
            }
        }
    } else {
        echo "No cases found in response" . PHP_EOL;
    }
    
    // Save raw response for detailed analysis
    $responseFile = 'soap_response_' . date('Y-m-d_H-i-s') . '.xml';
    file_put_contents($responseFile, $lastResponse);
    echo PHP_EOL . "Raw response saved to: $responseFile" . PHP_EOL;
    
    // Search for specific patterns in raw response that might indicate truncation
    echo PHP_EOL . "=== RAW RESPONSE PATTERN ANALYSIS ===" . PHP_EOL;
    
    $patterns = [
        'truncated' => '/truncated|limited|partial/i',
        'more_data' => '/more|additional|continue|next/i',
        'limit_reached' => '/limit|maximum|max|exceeded/i',
        'total_count' => '/total.*?(\d+)/i'
    ];
    
    foreach ($patterns as $name => $pattern) {
        if (preg_match_all($pattern, $lastResponse, $matches)) {
            echo "Pattern '$name' found: " . implode(', ', array_unique($matches[0])) . PHP_EOL;
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . PHP_EOL;
    echo "Trace: " . $e->getTraceAsString() . PHP_EOL;
}

echo PHP_EOL . "=== ANALYSIS COMPLETE ===" . PHP_EOL;
?>
