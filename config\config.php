<?php
/**
 * Configurații generale pentru aplicație
 */

// Numele aplicației
if (!defined('APP_NAME')) {
    define('APP_NAME', 'Portal Dosare Judecă<PERSON>ști');
}

// Setări pentru API-ul SOAP
if (!defined('SOAP_WSDL')) {
    define('SOAP_WSDL', 'http://portalquery.just.ro/query.asmx?WSDL');
}
if (!defined('SOAP_ENDPOINT')) {
    define('SOAP_ENDPOINT', 'http://portalquery.just.ro/query.asmx');
}
if (!defined('SOAP_NAMESPACE')) {
    define('SOAP_NAMESPACE', 'portalquery.just.ro');
}

// Setări pentru afișare
if (!defined('RESULTS_PER_PAGE')) {
    define('RESULTS_PER_PAGE', 25);
}

// Setări pentru formatare dată
if (!defined('DATE_FORMAT')) {
    define('DATE_FORMAT', 'd.m.Y');
}
if (!defined('DATETIME_FORMAT')) {
    define('DATETIME_FORMAT', 'd.m.Y H:i');
}

// Setări pentru debugging
if (!defined('DEBUG_MODE')) {
    define('DEBUG_MODE', true);
}

// Funcție pentru afișarea erorilor în modul debug
if (!function_exists('debug')) {
    function debug($data) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            echo '<pre>';
            print_r($data);
            echo '</pre>';
        }
    }
}
