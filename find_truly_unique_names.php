<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 FINDING TRULY UNIQUE NAMES\n";
echo "==============================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current total parties: " . count($dosar->parti) . "\n\n";
    
    // Get SOAP API parties (normalized)
    $soapParties = [];
    foreach ($dosar->parti as $party) {
        $partyArray = (array) $party;
        if (isset($partyArray['source']) && $partyArray['source'] === 'soap_api') {
            $normalized = strtolower(str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], trim($partyArray['nume'])));
            $soapParties[$normalized] = $partyArray['nume'];
        }
    }
    
    echo "SOAP API parties: " . count($soapParties) . "\n\n";
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    // Extract all potential names using enhanced comma separation
    $allPotentialNames = [];
    $commaNames = explode(',', $solutieSumarText);
    
    foreach ($commaNames as $name) {
        $originalName = $name;
        $name = trim($name);
        $name = preg_replace('/.*(?:apelanţii|apelan\?ii)\s+/', '', $name);
        $name = preg_replace('/\s*ca\s+(?:netimbrate|nefondate).*$/', '', $name);
        $name = preg_replace('/\s*\(.*?\)/', '', $name);
        $name = preg_replace('/\s*şi\s*$/', '', $name);
        $name = preg_replace('/\..*$/', '', $name);
        $name = trim($name);
        
        if (strlen($name) >= 3 && preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $name)) {
            $allPotentialNames[] = [
                'name' => $name,
                'original' => trim($originalName)
            ];
        }
    }
    
    echo "Total potential names from comma separation: " . count($allPotentialNames) . "\n\n";
    
    // Find truly unique names (not in SOAP API)
    $trulyUniqueNames = [];
    foreach ($allPotentialNames as $nameData) {
        $normalized = strtolower(str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], trim($nameData['name'])));
        
        if (!isset($soapParties[$normalized])) {
            $trulyUniqueNames[] = $nameData;
        }
    }
    
    // Remove duplicates from truly unique names
    $uniqueNames = [];
    $seenNames = [];
    
    foreach ($trulyUniqueNames as $nameData) {
        $normalized = strtolower(str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], trim($nameData['name'])));
        
        if (!isset($seenNames[$normalized])) {
            $seenNames[$normalized] = true;
            $uniqueNames[] = $nameData;
        }
    }
    
    echo "🔍 TRULY UNIQUE NAMES (NOT IN SOAP API):\n";
    echo "========================================\n\n";
    echo "Count: " . count($uniqueNames) . "\n\n";
    
    foreach ($uniqueNames as $i => $nameData) {
        echo ($i + 1) . ". \"{$nameData['name']}\"\n";
        echo "   Original: \"{$nameData['original']}\"\n";
        
        // Test validation
        $isValid = (strlen($nameData['name']) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $nameData['name']));
        echo "   Validation: " . ($isValid ? "PASS" : "FAIL") . "\n";
        
        if (!$isValid) {
            echo "   Issue: ";
            if (strlen($nameData['name']) < 3) {
                echo "Too short";
            } else {
                echo "Regex failed";
            }
            echo "\n";
        }
        echo "\n";
    }
    
    // Calculate expected total
    $expectedTotal = count($soapParties) + count($uniqueNames);
    echo "📊 CALCULATION:\n";
    echo "===============\n\n";
    echo "SOAP API parties: " . count($soapParties) . "\n";
    echo "Truly unique decision text names: " . count($uniqueNames) . "\n";
    echo "Expected total: {$expectedTotal}\n";
    echo "Current total: " . count($dosar->parti) . "\n";
    echo "Difference: " . ($expectedTotal - count($dosar->parti)) . "\n";
    echo "Missing to reach 380: " . (380 - count($dosar->parti)) . "\n\n";
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
