<?php

namespace App\Config;

use PDO;
use PDOException;
use Exception;

/**
 * Database Configuration and Connection Manager
 * 
 * Provides secure database connections with proper error handling,
 * connection pooling, and GDPR compliance features.
 */
class Database
{
    /**
     * PDO instance
     * @var PDO|null
     */
    private static $instance = null;

    /**
     * Connection configuration
     * @var array
     */
    private static $config = [
        'host' => DB_HOST,
        'dbname' => DB_NAME,
        'username' => DB_USER,
        'password' => DB_PASS,
        'charset' => DB_CHARSET,
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
        ]
    ];

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {}

    /**
     * Get database connection instance (Singleton pattern)
     * 
     * @return PDO Database connection
     * @throws Exception If connection fails
     */
    public static function getInstance(): PDO
    {
        if (self::$instance === null) {
            try {
                $dsn = sprintf(
                    'mysql:host=%s;dbname=%s;charset=%s',
                    self::$config['host'],
                    self::$config['dbname'],
                    self::$config['charset']
                );

                self::$instance = new PDO(
                    $dsn,
                    self::$config['username'],
                    self::$config['password'],
                    self::$config['options']
                );

                // Set timezone to match PHP timezone
                self::$instance->exec("SET time_zone = '" . date('P') . "'");

            } catch (PDOException $e) {
                // Log the error without exposing sensitive information
                error_log("Database connection failed: " . $e->getMessage());
                throw new Exception("Database connection failed. Please check configuration.");
            }
        }

        return self::$instance;
    }

    /**
     * Execute a prepared statement with parameters
     * 
     * @param string $sql SQL query with placeholders
     * @param array $params Parameters for the query
     * @return PDOStatement Executed statement
     * @throws Exception If query execution fails
     */
    public static function execute(string $sql, array $params = []): \PDOStatement
    {
        try {
            $pdo = self::getInstance();
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Database query failed.");
        }
    }

    /**
     * Fetch a single row
     * 
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return array|false Single row or false if not found
     */
    public static function fetchOne(string $sql, array $params = [])
    {
        $stmt = self::execute($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Fetch all rows
     * 
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return array All matching rows
     */
    public static function fetchAll(string $sql, array $params = []): array
    {
        $stmt = self::execute($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Insert a record and return the last insert ID
     * 
     * @param string $table Table name
     * @param array $data Associative array of column => value
     * @return int Last insert ID
     * @throws Exception If insert fails
     */
    public static function insert(string $table, array $data): int
    {
        $columns = array_keys($data);
        $placeholders = array_map(function($col) { return ':' . $col; }, $columns);
        
        $sql = sprintf(
            'INSERT INTO %s (%s) VALUES (%s)',
            $table,
            implode(', ', $columns),
            implode(', ', $placeholders)
        );

        $params = [];
        foreach ($data as $key => $value) {
            $params[':' . $key] = $value;
        }

        self::execute($sql, $params);
        return (int) self::getInstance()->lastInsertId();
    }

    /**
     * Update records
     * 
     * @param string $table Table name
     * @param array $data Associative array of column => value
     * @param array $where WHERE conditions as column => value
     * @return int Number of affected rows
     */
    public static function update(string $table, array $data, array $where): int
    {
        $setClause = [];
        $params = [];

        foreach ($data as $key => $value) {
            $setClause[] = $key . ' = :set_' . $key;
            $params[':set_' . $key] = $value;
        }

        $whereClause = [];
        foreach ($where as $key => $value) {
            $whereClause[] = $key . ' = :where_' . $key;
            $params[':where_' . $key] = $value;
        }

        $sql = sprintf(
            'UPDATE %s SET %s WHERE %s',
            $table,
            implode(', ', $setClause),
            implode(' AND ', $whereClause)
        );

        $stmt = self::execute($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Delete records
     * 
     * @param string $table Table name
     * @param array $where WHERE conditions as column => value
     * @return int Number of affected rows
     */
    public static function delete(string $table, array $where): int
    {
        $whereClause = [];
        $params = [];

        foreach ($where as $key => $value) {
            $whereClause[] = $key . ' = :' . $key;
            $params[':' . $key] = $value;
        }

        $sql = sprintf(
            'DELETE FROM %s WHERE %s',
            $table,
            implode(' AND ', $whereClause)
        );

        $stmt = self::execute($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Begin a database transaction
     */
    public static function beginTransaction(): void
    {
        self::getInstance()->beginTransaction();
    }

    /**
     * Commit a database transaction
     */
    public static function commit(): void
    {
        self::getInstance()->commit();
    }

    /**
     * Rollback a database transaction
     */
    public static function rollback(): void
    {
        self::getInstance()->rollBack();
    }

    /**
     * Check if currently in a transaction
     * 
     * @return bool True if in transaction
     */
    public static function inTransaction(): bool
    {
        return self::getInstance()->inTransaction();
    }

    /**
     * Get the last insert ID
     * 
     * @return string Last insert ID
     */
    public static function lastInsertId(): string
    {
        return self::getInstance()->lastInsertId();
    }

    /**
     * Close the database connection
     */
    public static function close(): void
    {
        self::$instance = null;
    }

    /**
     * Test database connection
     * 
     * @return bool True if connection successful
     */
    public static function testConnection(): bool
    {
        try {
            self::getInstance();
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get database schema version for migrations
     * 
     * @return int Current schema version
     */
    public static function getSchemaVersion(): int
    {
        try {
            $result = self::fetchOne("SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1");
            return $result ? (int) $result['version'] : 0;
        } catch (Exception $e) {
            // Table doesn't exist yet
            return 0;
        }
    }

    /**
     * Set database schema version
     * 
     * @param int $version Schema version
     */
    public static function setSchemaVersion(int $version): void
    {
        // Create migrations table if it doesn't exist
        $sql = "CREATE TABLE IF NOT EXISTS schema_migrations (
            version INT PRIMARY KEY,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB";
        
        self::execute($sql);
        
        // Insert or update version
        $sql = "INSERT INTO schema_migrations (version) VALUES (?) 
                ON DUPLICATE KEY UPDATE applied_at = CURRENT_TIMESTAMP";
        self::execute($sql, [$version]);
    }
}
