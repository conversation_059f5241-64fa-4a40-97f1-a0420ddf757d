<?php

/**
 * Database Migration Runner
 * 
 * Executes database migrations for the Portal Judiciar monitoring system.
 * Run this script to set up the database schema.
 */

// Include the bootstrap file
require_once dirname(__DIR__) . '/bootstrap.php';

use App\Config\Database;

/**
 * Migration Runner Class
 */
class MigrationRunner
{
    private $migrationsPath;
    private $verbose;

    public function __construct($migrationsPath = null, $verbose = true)
    {
        $this->migrationsPath = $migrationsPath ?: __DIR__ . '/migrations';
        $this->verbose = $verbose;
    }

    /**
     * Run all pending migrations
     */
    public function run()
    {
        $this->log("Starting database migrations...");

        try {
            // Test database connection
            if (!Database::testConnection()) {
                throw new Exception("Cannot connect to database. Please check your configuration.");
            }

            $this->log("Database connection successful.");

            // Get current schema version
            $currentVersion = Database::getSchemaVersion();
            $this->log("Current schema version: " . $currentVersion);

            // Get available migrations
            $migrations = $this->getAvailableMigrations();
            $this->log("Found " . count($migrations) . " migration files.");

            // Run pending migrations
            $executed = 0;
            foreach ($migrations as $version => $file) {
                if ($version > $currentVersion) {
                    $this->runMigration($version, $file);
                    $executed++;
                }
            }

            if ($executed === 0) {
                $this->log("No pending migrations to run.");
            } else {
                $this->log("Successfully executed {$executed} migrations.");
            }

            $this->log("Migration process completed successfully!");

        } catch (Exception $e) {
            $this->log("Migration failed: " . $e->getMessage(), 'ERROR');
            exit(1);
        }
    }

    /**
     * Get available migration files
     */
    private function getAvailableMigrations()
    {
        $migrations = [];
        $files = glob($this->migrationsPath . '/*.sql');

        foreach ($files as $file) {
            $filename = basename($file);
            if (preg_match('/^(\d+)_/', $filename, $matches)) {
                $version = (int) $matches[1];
                $migrations[$version] = $file;
            }
        }

        ksort($migrations);
        return $migrations;
    }

    /**
     * Run a single migration
     */
    private function runMigration($version, $file)
    {
        $this->log("Running migration {$version}: " . basename($file));

        try {
            Database::beginTransaction();

            // Read and execute SQL file
            $sql = file_get_contents($file);
            if ($sql === false) {
                throw new Exception("Cannot read migration file: " . $file);
            }

            // Split SQL into individual statements
            $statements = $this->splitSqlStatements($sql);

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !$this->isComment($statement)) {
                    Database::execute($statement);
                }
            }

            // Update schema version
            Database::setSchemaVersion($version);

            Database::commit();
            $this->log("Migration {$version} completed successfully.");

        } catch (Exception $e) {
            Database::rollback();
            throw new Exception("Migration {$version} failed: " . $e->getMessage());
        }
    }

    /**
     * Split SQL content into individual statements
     */
    private function splitSqlStatements($sql)
    {
        // Remove comments and split by semicolon
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        $statements = explode(';', $sql);
        return array_filter($statements, function($stmt) {
            return !empty(trim($stmt));
        });
    }

    /**
     * Check if a line is a comment
     */
    private function isComment($line)
    {
        $line = trim($line);
        return empty($line) || 
               strpos($line, '--') === 0 || 
               strpos($line, '/*') === 0 ||
               strpos($line, '#') === 0;
    }

    /**
     * Log a message
     */
    private function log($message, $level = 'INFO')
    {
        if ($this->verbose) {
            $timestamp = date('Y-m-d H:i:s');
            echo "[{$timestamp}] [{$level}] {$message}\n";
        }
    }
}

/**
 * Command line interface
 */
if (php_sapi_name() === 'cli') {
    $options = getopt('h', ['help', 'quiet']);

    if (isset($options['h']) || isset($options['help'])) {
        echo "Database Migration Runner\n";
        echo "Usage: php migrate.php [options]\n";
        echo "Options:\n";
        echo "  -h, --help    Show this help message\n";
        echo "  --quiet       Run in quiet mode (no output)\n";
        exit(0);
    }

    $verbose = !isset($options['quiet']);
    $runner = new MigrationRunner(null, $verbose);
    $runner->run();
} else {
    // Web interface for development
    if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
        http_response_code(404);
        exit('Not found');
    }

    echo "<h1>Database Migration Runner</h1>";
    echo "<pre>";
    
    try {
        $runner = new MigrationRunner();
        $runner->run();
        echo "</pre>";
        echo "<p style='color: green;'><strong>Migrations completed successfully!</strong></p>";
    } catch (Exception $e) {
        echo "</pre>";
        echo "<p style='color: red;'><strong>Migration failed: " . htmlspecialchars($e->getMessage()) . "</strong></p>";
    }
}
