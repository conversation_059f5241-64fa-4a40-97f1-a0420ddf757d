<?php

namespace App\Helpers;

/**
 * SEO Helper - Gestionează meta tags, structured data și optimizări SEO
 */
class SEOHelper
{
    /**
     * Configurația SEO pentru fiecare pagină
     */
    private static $seoConfig = [
        'index' => [
            'title' => 'Portal Judiciar România - Căutare Dosare Instanțe',
            'description' => 'Căutați rapid dosare judecătorești din România! Portal oficial pentru verificarea dosarelor civile și penale din toate instanțele. Acces gratuit și instant.',
            'keywords' => 'portal judiciar românia, căutare dosare instanțe, verificare dosare tribunal, dosare civile penale, tribunal, judecătorie',
            'type' => 'website',
            'canonical' => '/'
        ],
        'contact' => [
            'title' => 'Contact Portal Judiciar România - Suport Tehnic',
            'description' => 'Contact portal judiciar românia pentru suport tehnic căutare dosare instanțe. Asistență specializată verificare dosare tribunal și dosare civile penale.',
            'keywords' => 'contact portal judiciar românia, suport tehnic dosare, asistență căutare instanțe, help desk tribunal',
            'type' => 'website',
            'canonical' => '/just/contact.php'
        ],
        'search' => [
            'title' => 'Căutare Dosare Instanțe România - Rezultate Online',
            'description' => 'Căutare dosare instanțe din tribunale și judecătorii România. Portal judiciar pentru verificare dosare tribunal civile și penale cu rezultate actualizate.',
            'keywords' => 'căutare dosare instanțe, portal judiciar românia, verificare dosare tribunal, dosare civile penale, rezultate căutare',
            'type' => 'website',
            'canonical' => '/just/search.php'
        ],
        'sedinte' => [
            'title' => 'Ședințe Judecătorești - Program Instanțe România',
            'description' => 'Program ședințe judecătorești portal judiciar românia. Căutare dosare instanțe după dată și tribunal cu informații actualizate pentru toate instanțele.',
            'keywords' => 'ședințe judecătorești, program instanțe, portal judiciar românia, căutare dosare instanțe, calendar tribunal',
            'type' => 'website',
            'canonical' => '/just/sedinte.php'
        ],
        'detalii_dosar' => [
            'title' => 'Detalii Dosar %s - Portal Judiciar România',
            'description' => 'Detalii complete dosar %s portal judiciar românia. Părți, termene, ședințe și evoluție dosar în instanțe judecătorești cu verificare actualizată.',
            'keywords' => 'detalii dosar, portal judiciar românia, informații dosar, verificare dosare tribunal, părți dosar',
            'type' => 'article',
            'canonical' => '/just/detalii_dosar.php'
        ],
        'avans' => [
            'title' => 'Căutare Avansată Dosare - Portal Judiciar România',
            'description' => 'Căutare dosare instanțe avansată cu filtre multiple portal judiciar românia. Verificare dosare tribunal civile și penale după părți și categorie.',
            'keywords' => 'căutare dosare instanțe, portal judiciar românia, căutare avansată, verificare dosare tribunal, filtre dosare',
            'type' => 'website',
            'canonical' => '/just/avans.php'
        ],
        'avansat' => [
            'title' => 'Căutare Complexă Dosare - Portal Judiciar România',
            'description' => 'Căutare dosare instanțe complexă portal judiciar românia cu opțiuni avansate filtrare. Verificare dosare tribunal cu criterii multiple precise.',
            'keywords' => 'căutare dosare instanțe, portal judiciar românia, căutare complexă, verificare dosare tribunal, opțiuni avansate',
            'type' => 'website',
            'canonical' => '/just/avansat.php'
        ]
    ];

    /**
     * Generează meta tags pentru o pagină specifică
     */
    public static function generateMetaTags($page, $params = [])
    {
        $config = self::$seoConfig[$page] ?? self::$seoConfig['index'];

        // Înlocuim parametrii dinamici în title și description
        $title = $config['title'];
        $description = $config['description'];

        // Tratament special pentru pagina de search cu parametri dinamici
        if ($page === 'search' && isset($params['search_term']) && !empty($params['search_term'])) {
            $searchTerm = htmlspecialchars($params['search_term']);
            $title = 'Rezultate căutare "' . $searchTerm . '" - Dosare România | DosareJust.ro';
            $description = 'Rezultate căutare pentru "' . $searchTerm . '" în dosarele judecătorești din România. Informații complete despre dosare civile și penale.';
        } elseif (!empty($params)) {
            $title = vsprintf($title, $params);
            $description = vsprintf($description, $params);
        }

        // Detectăm automat base URL-ul pentru a evita hardcoding
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $scriptPath = dirname($_SERVER['SCRIPT_NAME']);

        // Pentru producție, folosim domeniul just.gabrielanghel.ro
        if (strpos($host, 'just.gabrielanghel.ro') !== false) {
            $baseUrl = $protocol . '://' . $host;
        } else {
            // Pentru dezvoltare locală
            $baseUrl = $protocol . '://' . $host . $scriptPath;
        }

        $currentUrl = $baseUrl . $config['canonical'];

        // Pentru search cu parametri, adăugăm parametrii în URL
        if ($page === 'search' && isset($params['search_term']) && !empty($params['search_term'])) {
            $currentUrl .= '?search_term=' . urlencode($params['search_term']);
        }
        
        $metaTags = [
            // Meta tags de bază
            'title' => $title,
            'description' => $description,
            'keywords' => $config['keywords'],
            'canonical' => $currentUrl,
            
            // Open Graph
            'og:title' => $title,
            'og:description' => $description,
            'og:type' => $config['type'],
            'og:url' => $currentUrl,
            'og:site_name' => 'Portal Judiciar România',
            'og:locale' => 'ro_RO',
            'og:image' => $baseUrl . '/images/logo.jpg',
            'og:image:width' => '1200',
            'og:image:height' => '630',
            'og:image:alt' => 'Portal Judiciar România - Logo',
            
            // Twitter Cards
            'twitter:card' => 'summary_large_image',
            'twitter:title' => $title,
            'twitter:description' => $description,
            'twitter:image' => $baseUrl . '/images/logo.jpg',
            'twitter:image:alt' => 'Portal Judiciar România - Logo',
            
            // Meta tags suplimentare
            'robots' => 'index, follow',
            'author' => 'Portal Judiciar România',
            'language' => 'ro',
            'geo.region' => 'RO',
            'geo.country' => 'Romania'
        ];
        
        return $metaTags;
    }

    /**
     * Generează HTML pentru meta tags
     */
    public static function renderMetaTags($page, $params = [])
    {
        $metaTags = self::generateMetaTags($page, $params);
        
        $html = '';
        
        // Title
        $html .= '<title>' . htmlspecialchars($metaTags['title']) . '</title>' . "\n";
        
        // Meta tags standard
        $standardTags = ['description', 'keywords', 'robots', 'author', 'language'];
        foreach ($standardTags as $tag) {
            if (isset($metaTags[$tag])) {
                $html .= '<meta name="' . $tag . '" content="' . htmlspecialchars($metaTags[$tag]) . '">' . "\n";
            }
        }
        
        // Canonical URL
        $html .= '<link rel="canonical" href="' . htmlspecialchars($metaTags['canonical']) . '">' . "\n";
        
        // Open Graph tags
        $ogTags = array_filter($metaTags, function($key) {
            return strpos($key, 'og:') === 0;
        }, ARRAY_FILTER_USE_KEY);
        
        foreach ($ogTags as $property => $content) {
            $html .= '<meta property="' . $property . '" content="' . htmlspecialchars($content) . '">' . "\n";
        }
        
        // Twitter Cards
        $twitterTags = array_filter($metaTags, function($key) {
            return strpos($key, 'twitter:') === 0;
        }, ARRAY_FILTER_USE_KEY);
        
        foreach ($twitterTags as $name => $content) {
            $html .= '<meta name="' . $name . '" content="' . htmlspecialchars($content) . '">' . "\n";
        }
        
        // Meta tags geografice
        $geoTags = array_filter($metaTags, function($key) {
            return strpos($key, 'geo.') === 0;
        }, ARRAY_FILTER_USE_KEY);
        
        foreach ($geoTags as $name => $content) {
            $html .= '<meta name="' . $name . '" content="' . htmlspecialchars($content) . '">' . "\n";
        }
        
        return $html;
    }

    /**
     * Generează JSON-LD structured data pentru Organization
     */
    public static function generateOrganizationSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'GovernmentOrganization',
            'name' => 'Portal Judiciar România',
            'alternateName' => 'DosareJust.ro',
            'description' => 'Portal oficial pentru căutarea dosarelor judecătorești din România',
            'url' => 'http://localhost/just/',
            'logo' => 'http://localhost/just/images/logo.jpg',
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'contactType' => 'customer service',
                'availableLanguage' => 'Romanian',
                'url' => 'http://localhost/just/contact.php'
            ],
            'areaServed' => [
                '@type' => 'Country',
                'name' => 'Romania'
            ],
            'serviceType' => 'Servicii judiciare online',
            'governmentType' => 'Portal informativ'
        ];
        
        return json_encode($schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Generează JSON-LD structured data pentru WebSite cu SearchAction
     */
    public static function generateWebSiteSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => 'Portal Judiciar România',
            'alternateName' => 'DosareJust.ro',
            'url' => 'http://localhost/just/',
            'description' => 'Portal pentru căutarea dosarelor și ședințelor judecătorești din România',
            'inLanguage' => 'ro',
            'potentialAction' => [
                [
                    '@type' => 'SearchAction',
                    'target' => [
                        '@type' => 'EntryPoint',
                        'urlTemplate' => 'http://localhost/just/search.php?search_term={search_term_string}'
                    ],
                    'query-input' => 'required name=search_term_string'
                ]
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'Portal Judiciar România'
            ]
        ];
        
        return json_encode($schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Generează JSON-LD structured data pentru BreadcrumbList
     */
    public static function generateBreadcrumbSchema($breadcrumbs)
    {
        $listItems = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }
        
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $listItems
        ];
        
        return json_encode($schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Generează JSON-LD structured data pentru GovernmentService
     */
    public static function generateGovernmentServiceSchema()
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'GovernmentService',
            'name' => 'Căutare Dosare Judecătorești',
            'description' => 'Serviciu online pentru căutarea și verificarea dosarelor judecătorești din România',
            'provider' => [
                '@type' => 'GovernmentOrganization',
                'name' => 'Portal Judiciar România'
            ],
            'areaServed' => [
                '@type' => 'Country',
                'name' => 'Romania'
            ],
            'availableChannel' => [
                '@type' => 'ServiceChannel',
                'serviceUrl' => 'http://localhost/just/',
                'serviceName' => 'Portal Web',
                'availableLanguage' => 'Romanian'
            ],
            'serviceType' => 'Informații judiciare',
            'audience' => [
                '@type' => 'Audience',
                'audienceType' => 'Cetățeni, avocați, instituții'
            ]
        ];
        
        return json_encode($schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Renderează toate schema-urile JSON-LD pentru o pagină
     */
    public static function renderStructuredData($page, $breadcrumbs = [])
    {
        $schemas = [];
        
        // Schema Organization (pe toate paginile)
        $schemas[] = self::generateOrganizationSchema();
        
        // Schema WebSite (doar pe pagina principală)
        if ($page === 'index') {
            $schemas[] = self::generateWebSiteSchema();
            $schemas[] = self::generateGovernmentServiceSchema();
        }
        
        // Schema Breadcrumb (dacă sunt furnizate)
        if (!empty($breadcrumbs)) {
            $schemas[] = self::generateBreadcrumbSchema($breadcrumbs);
        }
        
        $html = '';
        foreach ($schemas as $schema) {
            $html .= '<script type="application/ld+json">' . "\n";
            $html .= $schema . "\n";
            $html .= '</script>' . "\n";
        }
        
        return $html;
    }
}
