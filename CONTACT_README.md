# Contact Page - Portal Judiciar

## Descriere

Pagina de contact pentru Portal Judiciar permite utilizatorilor să trimită mesaje către echipa de administrare. Implementarea include validare completă, protecție CSRF, rate limiting și trimitere email prin PHPMailer.

## Fișiere create/modificate

### Fișiere noi:
- `contact.php` - Pagina principală de contact cu formular
- `process_contact.php` - Backend pentru procesarea formularului
- `src/Helpers/SecurityHelper.php` - Helper pentru funcții de securitate
- `test_contact.php` - Pagină pentru testarea funcționalității
- `CONTACT_README.md` - Această documentație

### Fișiere modificate:
- `includes/footer.php` - Actualizat link-ul de contact
- `src/Templates/layouts/main.twig` - Actualizat link-ul de contact
- `src/Config/constants.php` - Adăugate constante pentru configurarea email-ului

## Caracteristici implementate

### 🔒 Securitate
- **Protecție CSRF**: Token-uri generate și validate pentru fiecare formular
- **Rate Limiting**: Maxim 5 mesaje pe oră per adresă IP
- **Validare input**: Sanitizare și validare completă a datelor
- **Logare securitate**: Înregistrarea tentativelor suspecte

### 📧 Funcționalitate Email
- **PHPMailer**: Trimitere email prin SMTP
- **Template HTML**: Email-uri formatate profesional
- **Encoding UTF-8**: Suport complet pentru diacriticele românești
- **Configurare flexibilă**: Constante pentru configurarea SMTP

### 🎨 Design și UX
- **Responsive Design**: Optimizat pentru toate dispozitivele
- **Blue Judicial Theme**: Respectă schema de culori a portalului (#007bff, #2c3e50)
- **Validare în timp real**: Feedback imediat pentru utilizatori
- **Loading States**: Indicatori vizuali pentru procesare
- **Accessibility**: Conformitate WCAG 2.1 AA

### 📱 Mobile-First
- **Touch Targets**: Minimum 44px pentru elemente interactive
- **Responsive Breakpoints**: 767px, 992px, 1200px
- **Mobile Navigation**: Optimizat pentru dispozitive mobile
- **Card Layouts**: Design adaptat pentru ecrane mici

## Configurare

### 1. Configurare SMTP

Editați fișierul `src/Config/constants.php` și actualizați constantele:

```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_NAME', 'Portal Judiciar');
```

### 2. Permisiuni directoare

Asigurați-vă că directoarele au permisiuni de scriere:

```bash
chmod 755 logs/
chmod 755 cache/
chmod 755 temp/
```

### 3. Dependințe PHP

Verificați că sunt instalate extensiile necesare:
- `mbstring`
- `openssl`
- `json`
- `session`

## Testare

### Test automat
Accesați `test_contact.php` pentru verificarea automată a tuturor componentelor.

### Test manual
1. Accesați `contact.php`
2. Completați formularul cu date valide
3. Verificați validarea în timp real
4. Testați trimiterea mesajului
5. Verificați primirea email-ului

### Test rate limiting
1. Trimiteți 5 mesaje rapid
2. Încercați să trimiteți al 6-lea mesaj
3. Verificați că apare mesajul de rate limiting

## Validări implementate

### Câmpuri obligatorii
- **Nume**: 2-100 caractere
- **Email**: Format valid de email
- **Mesaj**: 10-2000 caractere

### Câmpuri opționale
- **Telefon**: Format românesc valid (dacă completat)

### Validări de securitate
- **CSRF Token**: Validat pentru fiecare cerere
- **Rate Limiting**: 5 mesaje/oră per IP
- **Input Sanitization**: Curățare automată a datelor

## Mesaje de eroare (română)

- "Token de securitate invalid. Vă rugăm să reîncărcați pagina și să încercați din nou."
- "Ați depășit limita de încercări. Vă rugăm să încercați din nou în X minute."
- "Vă rugăm să corectați următoarele erori: ..."
- "Mesajul dumneavoastră a fost trimis cu succes!"

## Logare și monitorizare

### Fișiere de log
- `logs/contact_success.log` - Mesaje trimise cu succes
- `logs/contact_errors.log` - Erori de trimitere
- `logs/rate_limit_contact.json` - Date rate limiting

### Informații logate
- Timestamp
- Adresa IP
- User Agent
- Date formular (fără informații sensibile)
- Erori și excepții

## Integrare cu portalul

### Navigation
- Link adăugat în footer pe toate paginile
- Breadcrumb navigation pe pagina de contact
- Consistent cu designul existent

### Stiluri
- Utilizează clasele CSS existente
- Respectă variabilele CSS definite
- Compatibil cu tema blue judicial

## Troubleshooting

### Email-urile nu se trimit
1. Verificați configurarea SMTP în `constants.php`
2. Verificați că extensia `openssl` este activă
3. Verificați logurile din `logs/contact_errors.log`

### Rate limiting prea restrictiv
1. Modificați parametrii în `process_contact.php`
2. Ștergeți fișierul `logs/rate_limit_contact.json`

### Probleme de validare
1. Verificați că JavaScript este activat
2. Verificați consolă pentru erori
3. Testați cu date diferite

## Dezvoltare viitoare

### Îmbunătățiri posibile
- [ ] Captcha pentru securitate suplimentară
- [ ] Categorii de mesaje (întrebări, probleme, sugestii)
- [ ] Sistem de ticketing pentru urmărirea mesajelor
- [ ] Notificări email pentru administratori
- [ ] Dashboard pentru gestionarea mesajelor
- [ ] Export mesaje în format CSV/Excel
- [ ] Statistici și rapoarte

### Optimizări
- [ ] Cache pentru rate limiting în Redis/Memcached
- [ ] Queue pentru trimiterea email-urilor
- [ ] Compresie CSS/JS pentru performanță
- [ ] CDN pentru assets statice

## Suport

Pentru probleme sau întrebări legate de implementarea paginii de contact, verificați:
1. Fișierele de log din directorul `logs/`
2. Pagina de test `test_contact.php`
3. Documentația PHPMailer
4. Configurarea serverului SMTP
