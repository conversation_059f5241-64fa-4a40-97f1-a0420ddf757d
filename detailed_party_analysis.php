<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 DETAILED PARTY EXTRACTION ANALYSIS\n";
echo "=====================================\n\n";

$dosarService = new DosarService();

// Test cases
$testCases = [
    [
        'numar' => '130/98/2022',
        'institutie' => 'TribunalulIALOMITA',
        'expected' => 380,
        'name' => 'TribunalulIALOMITA'
    ],
    [
        'numar' => '130/98/2022', 
        'institutie' => 'CurteadeApelBUCURESTI',
        'expected' => 380,
        'name' => 'CurteadeApelBUCURESTI'
    ]
];

foreach ($testCases as $testCase) {
    echo "📋 Case: {$testCase['name']}\n";
    echo "=" . str_repeat("=", strlen($testCase['name']) + 6) . "\n";
    
    try {
        // Get case details
        $dosar = $dosarService->getDetaliiDosar($testCase['numar'], $testCase['institutie']);
        
        if ($dosar) {
            echo "✅ Case found\n";
            echo "Data type: " . gettype($dosar) . "\n";
            
            // Get parties from the enhanced dosar object (includes hybrid extraction)
            $parties = [];
            if (isset($dosar->parti) && is_array($dosar->parti)) {
                foreach ($dosar->parti as $parte) {
                    $parties[] = [
                        'nume' => $parte->nume ?? '',
                        'calitate' => $parte->calitate ?? '',
                        'source' => $parte->source ?? 'unknown'
                    ];
                }
            }
            $totalParties = count($parties);
            
            echo "Total parties: {$totalParties} / {$testCase['expected']} expected\n";
            echo "Missing: " . ($testCase['expected'] - $totalParties) . "\n\n";
            
            // Analyze party sources
            $soapParties = 0;
            $decisionParties = 0;
            $qualityBreakdown = [];
            
            foreach ($parties as $party) {
                if (isset($party['source'])) {
                    if ($party['source'] === 'soap_api') {
                        $soapParties++;
                    } elseif ($party['source'] === 'decision_text') {
                        $decisionParties++;
                    }
                }
                
                $quality = $party['calitate'] ?? 'Unknown';
                $qualityBreakdown[$quality] = ($qualityBreakdown[$quality] ?? 0) + 1;
            }
            
            echo "📊 SOURCE BREAKDOWN:\n";
            echo "SOAP API parties: {$soapParties}\n";
            echo "Decision text parties: {$decisionParties}\n\n";
            
            echo "📊 QUALITY BREAKDOWN:\n";
            foreach ($qualityBreakdown as $quality => $count) {
                echo "{$quality}: {$count}\n";
            }
            
            // Show first 10 and last 10 parties for inspection
            echo "\n📝 FIRST 10 PARTIES:\n";
            for ($i = 0; $i < min(10, count($parties)); $i++) {
                $party = $parties[$i];
                $source = $party['source'] ?? 'unknown';
                echo ($i + 1) . ". {$party['nume']} ({$party['calitate']}) [{$source}]\n";
            }
            
            if (count($parties) > 10) {
                echo "\n📝 LAST 10 PARTIES:\n";
                for ($i = max(0, count($parties) - 10); $i < count($parties); $i++) {
                    $party = $parties[$i];
                    $source = $party['source'] ?? 'unknown';
                    echo ($i + 1) . ". {$party['nume']} ({$party['calitate']}) [{$source}]\n";
                }
            }
            
        } else {
            echo "❌ Case not found\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("-", 80) . "\n\n";
}

echo "✅ Analysis complete\n";
?>
