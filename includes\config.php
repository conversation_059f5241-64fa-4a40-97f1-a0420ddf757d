<?php
/**
 * Configurații generale pentru aplicație
 */

// Numele aplicației
if (!defined('APP_NAME')) {
    define('APP_NAME', 'Portal Dosare Judecătorești');
}

// Setări pentru API-ul SOAP
if (!defined('SOAP_WSDL')) {
    define('SOAP_WSDL', 'http://portalquery.just.ro/query.asmx?WSDL');
}
if (!defined('SOAP_ENDPOINT')) {
    define('SOAP_ENDPOINT', 'http://portalquery.just.ro/query.asmx');
}
if (!defined('SOAP_NAMESPACE')) {
    define('SOAP_NAMESPACE', 'portalquery.just.ro');
}

// Setări pentru afișare
if (!defined('RESULTS_PER_PAGE')) {
    define('RESULTS_PER_PAGE', 25);
}

// Setări pentru formatare dată
if (!defined('DATE_FORMAT')) {
    define('DATE_FORMAT', 'd.m.Y');
}
if (!defined('DATETIME_FORMAT')) {
    define('DATETIME_FORMAT', 'd.m.Y H:i');
}

// Setări pentru afișarea erorilor (dezactivați în producție)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

/**
 * Obține lista instanțelor judecătorești
 * Această funcție este acum definită în includes/functions.php
 * Această referință este menținută doar pentru documentație
 */
// Funcția getInstanteList() este definită în functions.php

/**
 * Funcție pentru afișarea erorilor în modul debug
 *
 * @param mixed $data Datele de afișat
 * @return void
 */
if (!function_exists('debug')) {
    function debug($data) {
        echo '<pre>';
        print_r($data);
        echo '</pre>';
    }
}