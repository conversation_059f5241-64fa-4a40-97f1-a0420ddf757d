<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simplu Butoane - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug me-2"></i>
            Test Simplu Butoane
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>Test Rapid</h5>
            <p>Acest test verifică dacă funcțiile JavaScript funcționează corect:</p>
        </div>
        
        <!-- Test Buttons -->
        <div class="text-center mb-4">
            <button type="button" class="btn btn-primary me-2" onclick="testExpandFunction()">
                <i class="fas fa-expand-alt me-1"></i>
                Test expandAllResults()
            </button>
            
            <button type="button" class="btn btn-secondary me-2" onclick="testCollapseFunction()">
                <i class="fas fa-compress-alt me-1"></i>
                Test collapseAllResults()
            </button>
            
            <button type="button" class="btn btn-info me-2" onclick="testToggleFunction()">
                <i class="fas fa-exchange-alt me-1"></i>
                Test toggleTermResults(0)
            </button>
            
            <button type="button" class="btn btn-warning" onclick="testNotificationFunction()">
                <i class="fas fa-bell me-1"></i>
                Test showNotification()
            </button>
        </div>
        
        <!-- Debug Info -->
        <div id="debugInfo" class="debug-info">
            <strong>Debug Info:</strong><br>
            Încărcare...
        </div>
        
        <!-- Test Elements -->
        <div class="row">
            <div class="col-md-6">
                <h5>Secțiune Test 1</h5>
                <div class="p-3 bg-light border rounded mb-2" onclick="toggleTermResults(0)">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Click pentru toggle</span>
                        <i class="fas fa-chevron-down" id="toggleIcon0"></i>
                    </div>
                </div>
                <div id="termContent0" style="display: none;" class="p-3 border rounded">
                    <p>Conținut test pentru secțiunea 1</p>
                </div>
            </div>
            
            <div class="col-md-6">
                <h5>Secțiune Test 2</h5>
                <div class="p-3 bg-light border rounded mb-2" onclick="toggleTermResults(1)">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Click pentru toggle</span>
                        <i class="fas fa-chevron-down" id="toggleIcon1"></i>
                    </div>
                </div>
                <div id="termContent1" style="display: none;" class="p-3 border rounded">
                    <p>Conținut test pentru secțiunea 2</p>
                </div>
            </div>
        </div>
        
        <!-- Butoane ca în index.php -->
        <div class="text-center mt-4">
            <h5>Butoane ca în index.php:</h5>
            <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="expandAllResults()">
                <i class="fas fa-expand-alt me-1"></i>
                Expandează toate
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllResults()">
                <i class="fas fa-compress-alt me-1"></i>
                Restrânge toate
            </button>
        </div>
    </div>
    
    <!-- Notification Container (ca în index.php) -->
    <div id="notificationContainer" class="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050; display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Exact same functions as in index.php
        
        /**
         * Show notification message - GLOBAL FUNCTION
         */
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');

            if (!container || !notification) {
                console.error('Notification elements not found');
                return;
            }

            // Icon mapping
            const iconMap = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };

            const icon = iconMap[type] || 'info-circle';

            // Set notification content and style
            notification.className = `alert alert-${type}`;
            notification.innerHTML = `<i class="fas fa-${icon} me-2"></i>${message}`;

            // Show notification
            container.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }

        /**
         * Toggle term results visibility - GLOBAL FUNCTION
         */
        function toggleTermResults(index) {
            try {
                const content = document.getElementById('termContent' + index);
                const icon = document.getElementById('toggleIcon' + index);

                console.log('Toggling term results for index:', index);
                console.log('Content element:', content);
                console.log('Icon element:', icon);

                if (!content) {
                    console.error('Content element not found for index:', index);
                    showNotification('Eroare: Secțiunea nu a fost găsită.', 'danger');
                    return;
                }

                if (!icon) {
                    console.warn('Icon element not found for index:', index);
                }

                const isVisible = content.style.display !== 'none';

                if (isVisible) {
                    content.style.display = 'none';
                    if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                    console.log('Collapsed section', index);
                } else {
                    content.style.display = 'block';
                    if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                    console.log('Expanded section', index);
                }

            } catch (error) {
                console.error('Error in toggleTermResults:', error);
                showNotification('Eroare la comutarea secțiunii.', 'danger');
            }
        }

        /**
         * Expand all results function - GLOBAL FUNCTION
         */
        function expandAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Expanding all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru expandare.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'block';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-up toggle-icon';
                });

                showNotification('Toate secțiunile au fost expandate.', 'info');
                console.log('Successfully expanded', termContents.length, 'sections');

            } catch (error) {
                console.error('Error in expandAllResults:', error);
                showNotification('Eroare la expandarea rezultatelor.', 'danger');
            }
        }

        /**
         * Collapse all results function - GLOBAL FUNCTION
         */
        function collapseAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Collapsing all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru restrângere.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'none';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-down toggle-icon';
                });

                showNotification('Toate secțiunile au fost restrânse.', 'info');
                console.log('Successfully collapsed', termContents.length, 'sections');

            } catch (error) {
                console.error('Error in collapseAllResults:', error);
                showNotification('Eroare la restrângerea rezultatelor.', 'danger');
            }
        }
        
        // Test functions
        function testExpandFunction() {
            updateDebugInfo('Testing expandAllResults()...');
            if (typeof expandAllResults === 'function') {
                expandAllResults();
                updateDebugInfo('✓ expandAllResults() called successfully', 'success');
            } else {
                updateDebugInfo('✗ expandAllResults() not found', 'error');
            }
        }
        
        function testCollapseFunction() {
            updateDebugInfo('Testing collapseAllResults()...');
            if (typeof collapseAllResults === 'function') {
                collapseAllResults();
                updateDebugInfo('✓ collapseAllResults() called successfully', 'success');
            } else {
                updateDebugInfo('✗ collapseAllResults() not found', 'error');
            }
        }
        
        function testToggleFunction() {
            updateDebugInfo('Testing toggleTermResults(0)...');
            if (typeof toggleTermResults === 'function') {
                toggleTermResults(0);
                updateDebugInfo('✓ toggleTermResults(0) called successfully', 'success');
            } else {
                updateDebugInfo('✗ toggleTermResults() not found', 'error');
            }
        }
        
        function testNotificationFunction() {
            updateDebugInfo('Testing showNotification()...');
            if (typeof showNotification === 'function') {
                showNotification('Test notification message!', 'success');
                updateDebugInfo('✓ showNotification() called successfully', 'success');
            } else {
                updateDebugInfo('✗ showNotification() not found', 'error');
            }
        }
        
        function updateDebugInfo(message, type = 'info') {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : (type === 'error' ? 'error' : 'warning');
            
            debugDiv.innerHTML += `<br>[${timestamp}] <span class="${className}">${message}</span>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('DOM loaded, functions ready for testing');
            
            // Test function accessibility
            updateDebugInfo('Function accessibility test:');
            updateDebugInfo('- expandAllResults: ' + typeof expandAllResults);
            updateDebugInfo('- collapseAllResults: ' + typeof collapseAllResults);
            updateDebugInfo('- toggleTermResults: ' + typeof toggleTermResults);
            updateDebugInfo('- showNotification: ' + typeof showNotification);
            
            // Test elements
            const termContents = document.querySelectorAll('[id^="termContent"]');
            const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
            updateDebugInfo(`Found ${termContents.length} termContent elements and ${toggleIcons.length} toggleIcon elements`);
        });
    </script>
</body>
</html>
