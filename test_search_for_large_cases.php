<?php
/**
 * Search for cases that actually have 500+ parties to test hybrid extraction
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🔍 Search for Large Cases (500+ Parties)</h1>\n";
echo "<p>Looking for cases that actually have 500+ parties to test hybrid extraction</p>\n";

try {
    $dosarService = new DosarService();
    
    // Test various institutions and case numbers that might have large party counts
    $testCases = [
        // Original cases mentioned
        ['130/98/2022', 'CurteadeApelBUCURESTI'],
        ['130/98/2022', 'TribunalulIALOMITA'],
        
        // Try some other potential large cases (insolvency cases often have many parties)
        ['130/98/2022', 'TribunalulBUCURESTI'],
        ['130/98/2022', 'JudecatoriaSECTORUL1BUCURESTI'],
        
        // Try different case numbers that might be insolvency cases
        ['1/2022', 'CurteadeApelBUCURESTI'],
        ['100/2022', 'CurteadeApelBUCURESTI'],
        ['200/2022', 'CurteadeApelBUCURESTI'],
        ['1000/2022', 'CurteadeApelBUCURESTI'],
        
        // Try some other institutions
        ['130/98/2022', 'CurteadeApelCLUJ'],
        ['130/98/2022', 'CurteadeApelCONSTANTA'],
    ];
    
    $largeCases = [];
    
    foreach ($testCases as $index => $caseData) {
        list($numarDosar, $institutie) = $caseData;
        
        echo "<h3>Test " . ($index + 1) . ": {$numarDosar} from {$institutie}</h3>\n";
        
        try {
            $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
            
            if ($dosar && !empty($dosar->numar)) {
                $totalParties = count($dosar->parti ?? []);
                echo "<p><strong>Parties found:</strong> {$totalParties}</p>\n";
                
                if ($totalParties >= 500) {
                    echo "<p style='color: green;'><strong>🎯 LARGE CASE FOUND!</strong></p>\n";
                    $largeCases[] = [
                        'case' => $numarDosar,
                        'institution' => $institutie,
                        'parties' => $totalParties,
                        'dosar' => $dosar
                    ];
                } elseif ($totalParties >= 100) {
                    echo "<p style='color: orange;'>Medium case ({$totalParties} parties)</p>\n";
                } else {
                    echo "<p>Small case ({$totalParties} parties)</p>\n";
                }
                
                // Quick source analysis
                $soapCount = 0;
                $decisionCount = 0;
                foreach ($dosar->parti as $parte) {
                    if ($parte->source === 'soap_api') $soapCount++;
                    if ($parte->source === 'decision_text') $decisionCount++;
                }
                echo "<p>SOAP: {$soapCount}, Decision: {$decisionCount}</p>\n";
                
            } else {
                echo "<p style='color: gray;'>Case not found</p>\n";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
        
        echo "<hr>\n";
        
        // Add a small delay to avoid overwhelming the SOAP API
        usleep(500000); // 0.5 second delay
    }
    
    echo "<h2>📊 Large Cases Summary</h2>\n";
    
    if (count($largeCases) > 0) {
        echo "<p style='color: green;'><strong>Found " . count($largeCases) . " cases with 500+ parties!</strong></p>\n";
        
        foreach ($largeCases as $largeCase) {
            echo "<h3>🎯 {$largeCase['case']} from {$largeCase['institution']} ({$largeCase['parties']} parties)</h3>\n";
            
            // Detailed analysis of the large case
            $dosar = $largeCase['dosar'];
            $soapCount = 0;
            $decisionCount = 0;
            $unknownCount = 0;
            
            foreach ($dosar->parti as $parte) {
                switch ($parte->source ?? 'unknown') {
                    case 'soap_api': $soapCount++; break;
                    case 'decision_text': $decisionCount++; break;
                    default: $unknownCount++; break;
                }
            }
            
            echo "<p><strong>Source breakdown:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>SOAP API: {$soapCount}</li>\n";
            echo "<li>Decision Text: {$decisionCount}</li>\n";
            echo "<li>Unknown: {$unknownCount}</li>\n";
            echo "</ul>\n";
            
            if ($decisionCount > 0) {
                echo "<p style='color: green;'><strong>✅ Hybrid extraction is working for this case!</strong></p>\n";
            } else {
                echo "<p style='color: orange;'><strong>⚠️ All parties from SOAP API only</strong></p>\n";
            }
            
            // Check for Saragea Tudorita
            $saragea_found = false;
            foreach ($dosar->parti as $parte) {
                if (stripos($parte->nume, 'SARAGEA') !== false && stripos($parte->nume, 'TUDORITA') !== false) {
                    $saragea_found = true;
                    echo "<p style='color: green;'><strong>🎯 'Saragea Tudorita' found: " . htmlspecialchars($parte->nume) . " [" . ($parte->source ?? 'unknown') . "]</strong></p>\n";
                    break;
                }
            }
            
            if (!$saragea_found) {
                echo "<p>'Saragea Tudorita' not found in this case</p>\n";
            }
            
            echo "<p><strong>URL:</strong> <a href='detalii_dosar.php?numar=" . urlencode($largeCase['case']) . "&institutie={$largeCase['institution']}' target='_blank'>View Case</a></p>\n";
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ No cases with 500+ parties found in this test set</strong></p>\n";
        echo "<p>This suggests either:</p>\n";
        echo "<ul>\n";
        echo "<li>The case numbers/institutions tested don't have large party counts</li>\n";
        echo "<li>The hybrid extraction system needs enhancement</li>\n";
        echo "<li>The original requirement of 500+ parties may not be realistic for these specific cases</li>\n";
        echo "</ul>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>🔧 Next Steps</h2>\n";
echo "<p>Based on these results, we should:</p>\n";
echo "<ol>\n";
echo "<li>Focus on cases that actually have large party counts</li>\n";
echo "<li>Verify if the 500+ party requirement is realistic for the specific case mentioned</li>\n";
echo "<li>Enhance extraction patterns if large cases are found but hybrid extraction isn't working</li>\n";
echo "<li>Clarify the exact case and institution that should have 500+ parties</li>\n";
echo "</ol>\n";
