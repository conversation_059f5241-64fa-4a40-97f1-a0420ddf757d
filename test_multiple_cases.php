<?php
/**
 * Test Multiple Cases for API Limitations
 * Tests various cases to determine if 100-party limit is consistent
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';

echo "<h1>Multiple Cases API Limitation Test</h1>";
echo "<p><strong>Purpose:</strong> Test different cases to verify if 100-party limit is consistent</p>";
echo "<hr>";

// Test cases to examine
$testCases = [
    // Different institutions
    ['numar' => '130/98/2022', 'institutie' => 'TribunalulIALOMITA', 'description' => 'Original case with exactly 100 parties'],
    ['numar' => '1/2023', 'institutie' => 'TribunalulBUCURESTI', 'description' => 'Recent case from Bucharest'],
    ['numar' => '100/2022', 'institutie' => 'TribunalulCLUJ', 'description' => 'Case from Cluj'],
    ['numar' => '50/2023', 'institutie' => 'TribunalulTIMIS', 'description' => 'Case from Timis'],
    ['numar' => '200/2021', 'institutie' => 'TribunalulCONSTANTA', 'description' => 'Older case from Constanta'],
    
    // Different case patterns
    ['numar' => '1000/2023', 'institutie' => null, 'description' => 'High number case, all institutions'],
    ['numar' => '1/2020', 'institutie' => null, 'description' => 'Old case, all institutions'],
    ['numar' => '500/2024', 'institutie' => null, 'description' => 'Future case, all institutions'],
];

$results = [];

try {
    // Create SOAP client
    $options = [
        'soap_version' => SOAP_1_2,
        'trace' => true,
        'exceptions' => true,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'connection_timeout' => 15
    ];
    
    $client = new SoapClient(SOAP_WSDL, $options);
    
    echo "<h2>Testing Multiple Cases</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Case Number</th>";
    echo "<th>Institution</th>";
    echo "<th>Description</th>";
    echo "<th>Total Cases Found</th>";
    echo "<th>Parties Count</th>";
    echo "<th>Response Size</th>";
    echo "<th>Response Time</th>";
    echo "<th>Status</th>";
    echo "</tr>";
    
    foreach ($testCases as $index => $testCase) {
        $startTime = microtime(true);
        
        try {
            // Prepare request parameters
            $params = [
                'numarDosar' => $testCase['numar'],
                'obiectDosar' => null,
                'numeParte' => null,
                'institutie' => $testCase['institutie'],
                'dataStart' => null,
                'dataStop' => null,
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];
            
            // Make SOAP request
            $response = $client->CautareDosare2($params);
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            // Get raw response for size calculation
            $rawResponse = $client->__getLastResponse();
            $responseSize = strlen($rawResponse);
            
            // Analyze response
            $totalCases = 0;
            $partiesCount = 0;
            $status = "No data";
            
            if (isset($response->CautareDosare2Result->Dosar)) {
                $dosare = $response->CautareDosare2Result->Dosar;
                
                // Handle single vs multiple cases
                if (!is_array($dosare)) {
                    $dosare = [$dosare];
                }
                
                $totalCases = count($dosare);
                
                // Count parties in first case (if exists)
                if ($totalCases > 0 && isset($dosare[0]->parti->DosarParte)) {
                    $parti = $dosare[0]->parti->DosarParte;
                    if (!is_array($parti)) {
                        $parti = [$parti];
                    }
                    $partiesCount = count($parti);
                }
                
                $status = "✓ Success";
            }
            
            // Store results for analysis
            $results[] = [
                'case' => $testCase['numar'],
                'institution' => $testCase['institutie'] ?? 'All',
                'totalCases' => $totalCases,
                'partiesCount' => $partiesCount,
                'responseSize' => $responseSize,
                'responseTime' => $responseTime
            ];
            
            // Display row
            echo "<tr>";
            echo "<td><strong>{$testCase['numar']}</strong></td>";
            echo "<td>" . ($testCase['institutie'] ?? '<em>All institutions</em>') . "</td>";
            echo "<td>{$testCase['description']}</td>";
            echo "<td>{$totalCases}</td>";
            echo "<td style='text-align: center;'>";
            if ($partiesCount > 0) {
                if ($partiesCount == 100) {
                    echo "<span style='color: red; font-weight: bold;'>{$partiesCount}</span>";
                } else {
                    echo "<span style='color: green;'>{$partiesCount}</span>";
                }
            } else {
                echo "<span style='color: gray;'>-</span>";
            }
            echo "</td>";
            echo "<td>" . number_format($responseSize) . " bytes</td>";
            echo "<td>{$responseTime}ms</td>";
            echo "<td>{$status}</td>";
            echo "</tr>";
            
        } catch (Exception $e) {
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            echo "<tr>";
            echo "<td><strong>{$testCase['numar']}</strong></td>";
            echo "<td>" . ($testCase['institutie'] ?? '<em>All institutions</em>') . "</td>";
            echo "<td>{$testCase['description']}</td>";
            echo "<td colspan='4' style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</td>";
            echo "<td>{$responseTime}ms</td>";
            echo "</tr>";
        }
        
        // Small delay between requests to be respectful
        usleep(500000); // 0.5 second delay
    }
    
    echo "</table>";
    
    // Analysis section
    echo "<h2>Analysis Results</h2>";
    
    $casesWithParties = array_filter($results, function($r) { return $r['partiesCount'] > 0; });
    $casesWithExactly100 = array_filter($casesWithParties, function($r) { return $r['partiesCount'] == 100; });
    $casesWithLessThan100 = array_filter($casesWithParties, function($r) { return $r['partiesCount'] < 100; });
    $casesWithMoreThan100 = array_filter($casesWithParties, function($r) { return $r['partiesCount'] > 100; });
    
    echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff;'>";
    echo "<h3>Key Findings:</h3>";
    echo "<ul>";
    echo "<li><strong>Total cases tested:</strong> " . count($results) . "</li>";
    echo "<li><strong>Cases with parties data:</strong> " . count($casesWithParties) . "</li>";
    echo "<li><strong>Cases with exactly 100 parties:</strong> " . count($casesWithExactly100) . "</li>";
    echo "<li><strong>Cases with less than 100 parties:</strong> " . count($casesWithLessThan100) . "</li>";
    echo "<li><strong>Cases with more than 100 parties:</strong> " . count($casesWithMoreThan100) . "</li>";
    echo "</ul>";
    
    if (count($casesWithParties) > 0) {
        $partiesCounts = array_column($casesWithParties, 'partiesCount');
        $minParties = min($partiesCounts);
        $maxParties = max($partiesCounts);
        $avgParties = round(array_sum($partiesCounts) / count($partiesCounts), 2);
        
        echo "<p><strong>Parties Statistics:</strong></p>";
        echo "<ul>";
        echo "<li>Minimum parties: {$minParties}</li>";
        echo "<li>Maximum parties: {$maxParties}</li>";
        echo "<li>Average parties: {$avgParties}</li>";
        echo "</ul>";
    }
    echo "</div>";
    
    // Conclusion
    echo "<h3>Conclusion:</h3>";
    if (count($casesWithMoreThan100) > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; color: #155724;'>";
        echo "<p><strong>✓ NO API LIMITATION DETECTED</strong></p>";
        echo "<p>Found cases with more than 100 parties, indicating the API does not have a hard 100-party limit.</p>";
        echo "</div>";
    } elseif (count($casesWithExactly100) > 0 && count($casesWithLessThan100) == 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; color: #721c24;'>";
        echo "<p><strong>⚠️ POTENTIAL API LIMITATION DETECTED</strong></p>";
        echo "<p>All cases with parties data have exactly 100 parties, strongly suggesting an API limitation.</p>";
        echo "</div>";
    } elseif (count($casesWithLessThan100) > 0 && count($casesWithExactly100) > 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; color: #856404;'>";
        echo "<p><strong>? MIXED RESULTS</strong></p>";
        echo "<p>Found both cases with exactly 100 parties and cases with fewer parties. The 100-party case might be a coincidence.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #e2e3e5; padding: 15px; border: 1px solid #d6d8db; color: #383d41;'>";
        echo "<p><strong>? INSUFFICIENT DATA</strong></p>";
        echo "<p>Not enough cases with parties data to determine if there's an API limitation.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24;'>Exception:</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Analysis completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
