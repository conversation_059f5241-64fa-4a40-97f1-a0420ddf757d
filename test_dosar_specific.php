<?php
// Test specific pentru dosarul "14096/3/2024*" - analiza completă a părților implicate
require_once 'bootstrap.php';

use App\Services\DosarService;

echo "<h1>🔍 Test Specific Dosar 14096/3/2024* - <PERSON><PERSON><PERSON> Implicate</h1>";

// Testează cu toate variantele posibile ale dosarului
$testCases = [
    ["14096/3/2024*", "Tribunalul București"],
    ["14096/3/2024", "Tribunalul București"],
    ["14096/3/2024*", "Curtea de Apel București"],
    ["14096/3/2024", "Curtea de Apel București"]
];

try {
    $dosarService = new DosarService();
    
    foreach ($testCases as $index => $testCase) {
        $numarDosar = $testCase[0];
        $institutie = $testCase[1];
        
        echo "<h2>Test " . ($index + 1) . ": {$numarDosar} - {$institutie}</h2>";
        
        try {
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6; border-radius: 5px;'>";
            echo "<strong>Căutare pentru:</strong><br>";
            echo "Număr dosar: " . htmlspecialchars($numarDosar) . "<br>";
            echo "Instituție: " . htmlspecialchars($institutie) . "<br>";
            echo "</div>";
            
            $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
            
            if (!$dosar) {
                echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
                echo "<strong>❌ Dosarul nu a fost găsit!</strong>";
                echo "</div>";
                continue;
            }
            
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
            echo "<strong>✅ Dosar găsit!</strong><br>";
            echo "Număr: " . htmlspecialchars($dosar->numar ?? 'N/A') . "<br>";
            echo "Instituție: " . htmlspecialchars($dosar->institutie ?? 'N/A') . "<br>";
            echo "Obiect: " . htmlspecialchars(substr($dosar->obiect ?? 'N/A', 0, 100)) . "...<br>";
            echo "</div>";
            
            // Analizează părțile în detaliu
            if (isset($dosar->parti) && is_array($dosar->parti)) {
                $totalParti = count($dosar->parti);
                
                echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff; border-radius: 5px;'>";
                echo "<strong>📊 Analiza Părților Implicate:</strong><br>";
                echo "Total părți găsite: <strong>{$totalParti}</strong><br>";
                echo "</div>";
                
                // Grupează părțile pe surse
                $partiPeSurse = [
                    'soap_api' => [],
                    'decision_text' => [],
                    'unknown' => []
                ];
                
                $partiCuProbleme = [];
                
                foreach ($dosar->parti as $index => $parte) {
                    $source = $parte->source ?? 'unknown';
                    $nume = trim($parte->nume ?? '');
                    $calitate = trim($parte->calitate ?? '');
                    
                    // Verifică pentru probleme
                    $probleme = [];
                    if (empty($nume)) {
                        $probleme[] = 'nume gol';
                    }
                    if (strlen($nume) < 2) {
                        $probleme[] = 'nume prea scurt';
                    }
                    if (empty($calitate)) {
                        $probleme[] = 'calitate goală';
                    }
                    
                    $parteInfo = [
                        'index' => $index,
                        'nume' => $nume,
                        'calitate' => $calitate,
                        'source' => $source,
                        'probleme' => $probleme
                    ];
                    
                    $partiPeSurse[$source][] = $parteInfo;
                    
                    if (!empty($probleme)) {
                        $partiCuProbleme[] = $parteInfo;
                    }
                }
                
                echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107; border-radius: 5px;'>";
                echo "<strong>📈 Distribuția pe surse:</strong><br>";
                echo "SOAP API: " . count($partiPeSurse['soap_api']) . " părți<br>";
                echo "Decision Text: " . count($partiPeSurse['decision_text']) . " părți<br>";
                echo "Unknown: " . count($partiPeSurse['unknown']) . " părți<br>";
                echo "</div>";
                
                // Afișează părțile cu probleme
                if (!empty($partiCuProbleme)) {
                    echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
                    echo "<strong>⚠️ Părți cu probleme găsite: " . count($partiCuProbleme) . "</strong><br>";
                    foreach ($partiCuProbleme as $parte) {
                        echo "- Index {$parte['index']}: \"{$parte['nume']}\" - Probleme: " . implode(', ', $parte['probleme']) . "<br>";
                    }
                    echo "</div>";
                }
                
                // Afișează primele 10 părți pentru verificare
                echo "<h3>📋 Primele 10 părți (pentru verificare):</h3>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<thead>";
                echo "<tr style='background: #e9ecef;'>";
                echo "<th>Index</th><th>Nume</th><th>Calitate</th><th>Sursă</th><th>Lungime Nume</th><th>Status</th>";
                echo "</tr>";
                echo "</thead>";
                echo "<tbody>";
                
                $maxDisplay = min(10, $totalParti);
                for ($i = 0; $i < $maxDisplay; $i++) {
                    $parte = $dosar->parti[$i];
                    $nume = $parte->nume ?? '';
                    $calitate = $parte->calitate ?? '';
                    $source = $parte->source ?? 'unknown';
                    $lungimeNume = strlen($nume);
                    
                    // Determină statusul
                    $status = 'Valid';
                    if (empty(trim($nume)) || strlen(trim($nume)) < 2) {
                        $status = 'Filtrat (nume invalid)';
                    }
                    
                    $rowStyle = '';
                    if ($status !== 'Valid') {
                        $rowStyle = 'background: #f8d7da;';
                    } elseif ($source === 'soap_api') {
                        $rowStyle = 'background: #e7f3ff;';
                    } elseif ($source === 'decision_text') {
                        $rowStyle = 'background: #f8f9fa;';
                    }
                    
                    echo "<tr style='$rowStyle'>";
                    echo "<td>" . ($i + 1) . "</td>";
                    echo "<td>" . htmlspecialchars($nume) . "</td>";
                    echo "<td>" . htmlspecialchars($calitate) . "</td>";
                    echo "<td>" . htmlspecialchars($source) . "</td>";
                    echo "<td>{$lungimeNume}</td>";
                    echo "<td>{$status}</td>";
                    echo "</tr>";
                }
                
                echo "</tbody>";
                echo "</table>";
                
                if ($totalParti > 10) {
                    echo "<p><em>... și încă " . ($totalParti - 10) . " părți</em></p>";
                }
                
                // Calculează statistici de afișare
                $partiValide = 0;
                $partiFiltrate = 0;
                
                foreach ($dosar->parti as $parte) {
                    $nume = trim($parte->nume ?? '');
                    if (!empty($nume) && strlen($nume) >= 2) {
                        $partiValide++;
                    } else {
                        $partiFiltrate++;
                    }
                }
                
                echo "<div style='background: #d1ecf1; padding: 10px; margin: 5px 0; border: 1px solid #bee5eb; border-radius: 5px;'>";
                echo "<strong>📊 Statistici de afișare (simulare frontend):</strong><br>";
                echo "Total părți din backend: {$totalParti}<br>";
                echo "Părți valide (vor fi afișate): <strong>{$partiValide}</strong><br>";
                echo "Părți filtrate (nu vor fi afișate): {$partiFiltrate}<br>";
                echo "Eficiența afișării: " . round(($partiValide / max($totalParti, 1)) * 100, 1) . "%<br>";
                echo "</div>";
                
                // Link pentru testare în interfața web
                echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffeaa7; border-radius: 5px;'>";
                echo "<strong>🔗 Testare în interfața web:</strong><br>";
                echo "<a href='detalii_dosar.php?numar=" . urlencode($numarDosar) . "&institutie=" . urlencode($institutie) . "&debug=1' target='_blank'>";
                echo "Deschide dosarul cu debug activat";
                echo "</a>";
                echo "</div>";
                
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
                echo "<strong>❌ Nu au fost găsite părți sau părțile nu sunt în format array</strong><br>";
                echo "Tip părți: " . gettype($dosar->parti ?? null) . "<br>";
                if (isset($dosar->parti)) {
                    echo "Conținut părți: " . print_r($dosar->parti, true) . "<br>";
                }
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
            echo "<strong>❌ Eroare la procesarea dosarului:</strong><br>";
            echo htmlspecialchars($e->getMessage());
            echo "</div>";
        }
        
        echo "<hr style='margin: 20px 0;'>";
    }
    
    echo "<h2>🎯 Concluzie</h2>";
    echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>✅ Rezultatele testului:</h3>";
    echo "<p>Acest test a verificat:</p>";
    echo "<ul>";
    echo "<li>Extragerea părților din serviciul DosarService</li>";
    echo "<li>Calitatea datelor returnate (nume, calitate, sursă)</li>";
    echo "<li>Identificarea părților cu probleme</li>";
    echo "<li>Simularea logicii de filtrare din frontend</li>";
    echo "<li>Calcularea eficienței afișării</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Eroare generală:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}
?>
