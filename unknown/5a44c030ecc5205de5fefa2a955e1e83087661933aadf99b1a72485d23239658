<?php
/**
 * <PERSON>ript to check the actual HTML output for the parties table
 */

// Start output buffering to capture the HTML
ob_start();

// Include the detalii_dosar.php page
$_GET['numar'] = '130/98/2022';
$_GET['institutie'] = 'TribunalulIALOMITA';
$_GET['debug'] = '1';

try {
    include 'detalii_dosar.php';
} catch (Exception $e) {
    echo "Error including page: " . $e->getMessage();
}

// Get the HTML output
$html = ob_get_clean();

// Count the number of table rows with parte-row class
preg_match_all('/<tr[^>]*class="[^"]*parte-row[^"]*"[^>]*>/', $html, $matches);
$parteRowCount = count($matches[0]);

// Count total tr elements in the parties table
preg_match('/<table[^>]*id="tabelParti"[^>]*>.*?<\/table>/s', $html, $tableMatch);
if (!empty($tableMatch[0])) {
    preg_match_all('/<tr[^>]*>/', $tableMatch[0], $allRowMatches);
    $totalRowCount = count($allRowMatches[0]);
} else {
    $totalRowCount = 0;
}

// Extract debug comments
preg_match_all('/<!-- DEBUG: (.*?) -->/', $html, $debugMatches);

echo "=== HTML OUTPUT ANALYSIS ===\n";
echo "Total <tr> elements in #tabelParti: $totalRowCount\n";
echo "Rows with 'parte-row' class: $parteRowCount\n";
echo "\nDEBUG COMMENTS:\n";
foreach ($debugMatches[1] as $debug) {
    echo "- $debug\n";
}

// Check for any PHP errors in the output
if (strpos($html, 'Fatal error') !== false || strpos($html, 'Parse error') !== false) {
    echo "\n!!! PHP ERRORS DETECTED IN OUTPUT !!!\n";
    preg_match_all('/(Fatal error|Parse error|Warning|Notice).*/', $html, $errorMatches);
    foreach ($errorMatches[0] as $error) {
        echo "- $error\n";
    }
}

echo "\n=== END ANALYSIS ===\n";
?>
