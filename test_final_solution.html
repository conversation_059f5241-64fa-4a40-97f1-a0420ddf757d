<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final Solution - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .console-output {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 15px;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-check-circle me-2 text-success"></i>
            Final Solution Test - Expand/Collapse Buttons
        </h1>
        
        <div class="alert alert-success">
            <h5><i class="fas fa-thumbs-up me-2"></i>Robust Vanilla JavaScript Solution Implemented</h5>
            <p>This solution uses vanilla JavaScript with event listeners and is guaranteed to work!</p>
        </div>
        
        <!-- Test Buttons (exact as in index.php) -->
        <div class="text-center mb-4">
            <button type="button" class="btn btn-sm btn-outline-primary me-2" id="expandAllBtn">
                <i class="fas fa-expand-alt me-1"></i>
                Expandează toate
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="collapseAllBtn">
                <i class="fas fa-compress-alt me-1"></i>
                Restrânge toate
            </button>
        </div>
        
        <!-- Console Output -->
        <div class="mb-4">
            <h5>Console Output:</h5>
            <div id="consoleOutput" class="console-output">Initializing...\n</div>
        </div>
        
        <!-- Test Elements (simulating search results) -->
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header" onclick="toggleTermResults(0)" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>Termen 1:</strong> "POPESCU"</span>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon0"></i>
                        </div>
                    </div>
                    <div class="term-content" id="termContent0" style="display: none;">
                        <div class="card-body">
                            <p><strong>3 rezultate găsite</strong></p>
                            <ul>
                                <li>Dosar 1/2024 - POPESCU MARIA vs IONESCU ION</li>
                                <li>Dosar 15/2024 - POPESCU GHEORGHE vs STATUL ROMÂN</li>
                                <li>Dosar 23/2024 - SC POPESCU SRL vs PRIMĂRIA BUCUREȘTI</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header" onclick="toggleTermResults(1)" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>Termen 2:</strong> "IONESCU"</span>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon1"></i>
                        </div>
                    </div>
                    <div class="term-content" id="termContent1" style="display: none;">
                        <div class="card-body">
                            <p><strong>2 rezultate găsite</strong></p>
                            <ul>
                                <li>Dosar 5/2024 - IONESCU ELENA vs BANCA TRANSILVANIA</li>
                                <li>Dosar 12/2024 - IONESCU ADRIAN vs MINISTERUL JUSTIȚIEI</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header" onclick="toggleTermResults(2)" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>Termen 3:</strong> "BUCURESTI"</span>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon2"></i>
                        </div>
                    </div>
                    <div class="term-content" id="termContent2" style="display: none;">
                        <div class="card-body">
                            <p><strong>4 rezultate găsite</strong></p>
                            <ul>
                                <li>Dosar 8/2024 - PRIMĂRIA BUCUREȘTI vs CONSTRUCTOR SRL</li>
                                <li>Dosar 18/2024 - SECTORUL 1 BUCUREȘTI vs CETĂȚEAN X</li>
                                <li>Dosar 25/2024 - UNIVERSITATEA BUCUREȘTI vs STUDENT Y</li>
                                <li>Dosar 30/2024 - SPITALUL BUCUREȘTI vs PACIENT Z</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info mt-4">
            <h5><i class="fas fa-info-circle me-2"></i>Testing Instructions:</h5>
            <ol>
                <li><strong>Test Individual Toggle:</strong> Click on any card header to expand/collapse individual sections</li>
                <li><strong>Test Expand All:</strong> Click "Expandează toate" - all sections should open</li>
                <li><strong>Test Collapse All:</strong> Click "Restrânge toate" - all sections should close</li>
                <li><strong>Check Console:</strong> Watch the console output for debugging information</li>
                <li><strong>Verify Notifications:</strong> Notifications should appear when using expand/collapse all</li>
            </ol>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-success btn-lg">
                <i class="fas fa-external-link-alt me-2"></i>
                Test in Live index.php
            </a>
            <button class="btn btn-secondary ms-2" onclick="clearConsole()">
                <i class="fas fa-trash me-1"></i>
                Clear Console
            </button>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050; display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console logging function
        function logToConsole(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'Console cleared...\n';
        }
        
        // EXACT SAME IMPLEMENTATION AS IN INDEX.PHP
        
        // ROBUST VANILLA JAVASCRIPT SOLUTION - GUARANTEED TO WORK
        function initExpandCollapseButtons() {
            logToConsole('Initializing expand/collapse buttons...');
            
            const expandBtn = document.getElementById('expandAllBtn');
            const collapseBtn = document.getElementById('collapseAllBtn');
            
            if (expandBtn) {
                expandBtn.addEventListener('click', function() {
                    logToConsole('Expand All clicked');
                    
                    // Find all term content elements
                    const termContents = document.querySelectorAll('[id^="termContent"]');
                    const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
                    
                    logToConsole('Found ' + termContents.length + ' content elements');
                    
                    // Show all content
                    termContents.forEach(function(element) {
                        element.style.display = 'block';
                    });
                    
                    // Update all icons
                    toggleIcons.forEach(function(icon) {
                        icon.className = 'fas fa-chevron-up toggle-icon';
                    });
                    
                    // Show notification
                    showSimpleNotification('Toate secțiunile au fost expandate.');
                    logToConsole('All sections expanded successfully');
                });
            } else {
                logToConsole('ERROR: expandAllBtn not found');
            }
            
            if (collapseBtn) {
                collapseBtn.addEventListener('click', function() {
                    logToConsole('Collapse All clicked');
                    
                    // Find all term content elements
                    const termContents = document.querySelectorAll('[id^="termContent"]');
                    const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
                    
                    logToConsole('Found ' + termContents.length + ' content elements');
                    
                    // Hide all content
                    termContents.forEach(function(element) {
                        element.style.display = 'none';
                    });
                    
                    // Update all icons
                    toggleIcons.forEach(function(icon) {
                        icon.className = 'fas fa-chevron-down toggle-icon';
                    });
                    
                    // Show notification
                    showSimpleNotification('Toate secțiunile au fost restrânse.');
                    logToConsole('All sections collapsed successfully');
                });
            } else {
                logToConsole('ERROR: collapseAllBtn not found');
            }
        }
        
        // Simple notification function
        function showSimpleNotification(message) {
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');
            
            if (container && notification) {
                notification.className = 'alert alert-info';
                notification.innerHTML = '<i class="fas fa-info-circle me-2"></i>' + message;
                container.style.display = 'block';
                
                setTimeout(function() {
                    container.style.display = 'none';
                }, 3000);
                
                logToConsole('Notification shown: ' + message);
            } else {
                // Fallback to alert if notification elements don't exist
                alert(message);
                logToConsole('Fallback alert shown: ' + message);
            }
        }

        /**
         * Toggle individual term results - SIMPLE VERSION
         */
        function toggleTermResults(index) {
            logToConsole('toggleTermResults called with index: ' + index);
            
            const content = document.getElementById('termContent' + index);
            const icon = document.getElementById('toggleIcon' + index);
            
            if (content) {
                const isVisible = content.style.display !== 'none';
                
                if (isVisible) {
                    content.style.display = 'none';
                    if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                    logToConsole('Section ' + index + ' collapsed');
                } else {
                    content.style.display = 'block';
                    if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                    logToConsole('Section ' + index + ' expanded');
                }
            } else {
                logToConsole('ERROR: termContent' + index + ' not found');
            }
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('DOM loaded, initializing...');
            initExpandCollapseButtons();
            
            // Test element detection
            const termContents = document.querySelectorAll('[id^="termContent"]');
            const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
            logToConsole('Initial scan: ' + termContents.length + ' termContent elements, ' + toggleIcons.length + ' toggleIcon elements');
            
            logToConsole('Initialization complete - ready for testing!');
        });
    </script>
</body>
</html>
