document.addEventListener('DOMContentLoaded',function(){if(typeof initDatepickers==='function' && document.querySelector('.datepicker')&& !document.querySelector('.datepicker')._flatpickr){initDatepickers();}if(typeof initSearchFormValidation==='function' && document.getElementById('searchForm')&& !document.getElementById('searchForm').hasAttribute('data-validation-initialized')){initSearchFormValidation();}});if(typeof initDatepickers !=='function'){function initDatepickers(){console.log('Inițializare datepicker din script.js');const datepickers=document.querySelectorAll('.datepicker');if(datepickers.length > 0 && typeof flatpickr !=='undefined'){flatpickr('.datepicker',{dateFormat: 'd.m.Y',locale: 'ro',allowInput: true,disableMobile: true,monthSelectorType: 'dropdown',position: 'auto'});}}}if(typeof initSearchFormValidation !=='function'){function initSearchFormValidation(){console.log('Inițializare validare formular din script.js');const searchForm=document.getElementById('searchForm');if(searchForm){searchForm.setAttribute('data-validation-initialized','true');searchForm.addEventListener('submit',function(e){let isValid=false;const numarDosar=document.getElementById('numarDosar').value.trim();const numeParte=document.getElementById('numeParte').value.trim();const obiectDosar=document.getElementById('obiectDosar').value.trim();if(numarDosar !=='' || numeParte !=='' || obiectDosar !==''){isValid=true;}if(!isValid){e.preventDefault();const alertDiv=document.createElement('div');alertDiv.className='alert alert-danger mt-3';alertDiv.innerHTML='<i class="fas fa-exclamation-circle mr-2"></i><strong>Eroare:</strong> Vă rugăm să completați cel puțin un criteriu de căutare: număr dosar,nume parte sau obiect dosar.';const formBody=searchForm.querySelector('.card-body');if(formBody){formBody.insertBefore(alertDiv,formBody.firstChild);setTimeout(function(){alertDiv.remove();},5000);}}});}}}