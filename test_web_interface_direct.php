<?php
/**
 * Direct Web Interface Test
 * Make actual HTTP request to test the web interface
 */

echo "🌐 DIRECT WEB INTERFACE TEST\n";
echo "============================\n\n";

$searchTerm = 'SARAGEA TUDORIŢA';
$baseUrl = 'http://localhost/just/';

echo "🔎 Testing web interface for: '$searchTerm'\n";
echo "Base URL: $baseUrl\n";
echo "=" . str_repeat("=", 50) . "\n";

// Prepare POST data
$postData = [
    'bulk_search_terms' => $searchTerm,
    'institutie' => '',
    'categorieInstanta' => '',
    'categorieCaz' => '',
    'dataInceput' => '',
    'dataSfarsit' => ''
];

echo "🔧 STEP 1: Preparing HTTP request\n";
echo "POST data: " . json_encode($postData, JSON_UNESCAPED_UNICODE) . "\n\n";

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt_array($ch, [
    CURLOPT_URL => $baseUrl . 'index.php',
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => http_build_query($postData),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 60,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/x-www-form-urlencoded',
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language: ro-RO,ro;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate',
        'Connection: keep-alive',
        'Upgrade-Insecure-Requests: 1'
    ]
]);

echo "🔧 STEP 2: Making HTTP request\n";

// Execute request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

if ($error) {
    echo "❌ cURL Error: $error\n";
    exit(1);
}

echo "✅ HTTP Response Code: $httpCode\n";
echo "✅ Response Length: " . strlen($response) . " characters\n\n";

// Save full response for analysis
$timestamp = date('Y-m-d_H-i-s');
$responseFile = "web_response_direct_$timestamp.html";
file_put_contents($responseFile, $response);
echo "✅ Full response saved to: $responseFile\n\n";

echo "🔍 STEP 3: Analyzing response content\n";

// Check for key indicators
$indicators = [
    'error_message' => 'alert alert-danger',
    'no_results_message' => 'Nu au fost găsite rezultate',
    'results_section' => 'results-section',
    'search_results' => 'term-results',
    'total_results' => 'Total rezultate:',
    'case_130_98_2022' => '130/98/2022',
    'tribunalul_ialomita' => 'TribunalulIALOMITA',
    'saragea_tudorita' => 'SARAGEA TUDORIŢA'
];

foreach ($indicators as $name => $pattern) {
    $found = strpos($response, $pattern) !== false;
    $status = $found ? '✅ FOUND' : '❌ NOT FOUND';
    echo "- $name: $status\n";
    
    if ($found && in_array($name, ['case_130_98_2022', 'tribunalul_ialomita'])) {
        // Get context around the match
        $pos = strpos($response, $pattern);
        $start = max(0, $pos - 100);
        $end = min(strlen($response), $pos + strlen($pattern) + 100);
        $context = substr($response, $start, $end - $start);
        echo "  Context: " . htmlspecialchars($context) . "\n";
    }
}

echo "\n🔍 STEP 4: Extracting key sections\n";

// Extract the main content area
if (preg_match('/<div class="container"[^>]*>.*?<\/div>\s*<\/div>\s*<\/div>/s', $response, $matches)) {
    $mainContent = $matches[0];
    echo "✅ Main content extracted (" . strlen($mainContent) . " characters)\n";
    
    // Save main content for detailed analysis
    $contentFile = "main_content_$timestamp.html";
    file_put_contents($contentFile, $mainContent);
    echo "✅ Main content saved to: $contentFile\n";
} else {
    echo "❌ Could not extract main content\n";
}

// Check for specific error patterns
$errorPatterns = [
    'PHP Fatal error' => '/PHP Fatal error:.*/',
    'PHP Warning' => '/PHP Warning:.*/',
    'PHP Notice' => '/PHP Notice:.*/',
    'Exception' => '/Exception:.*/',
    'Error:' => '/Error:.*/'
];

echo "\n🔍 STEP 5: Checking for errors\n";

foreach ($errorPatterns as $name => $pattern) {
    if (preg_match($pattern, $response, $matches)) {
        echo "❌ $name found: " . trim($matches[0]) . "\n";
    } else {
        echo "✅ No $name found\n";
    }
}

// Extract search form data to verify it was processed
echo "\n🔍 STEP 6: Verifying search form processing\n";

if (preg_match('/value="([^"]*SARAGEA[^"]*)"/', $response, $matches)) {
    echo "✅ Search term found in form: " . htmlspecialchars($matches[1]) . "\n";
} else {
    echo "❌ Search term not found in form\n";
}

// Check for total results counter
if (preg_match('/Total rezultate:\s*<span[^>]*>(\d+)<\/span>/', $response, $matches)) {
    echo "✅ Total results counter found: " . $matches[1] . "\n";
} else {
    echo "❌ Total results counter not found\n";
}

echo "\n🏁 Direct web interface test completed.\n";
echo "Check the saved files for detailed analysis:\n";
echo "- Full response: $responseFile\n";
if (isset($contentFile)) {
    echo "- Main content: $contentFile\n";
}
?>
