{% extends "base.twig" %}

{% block title %}Monitor<PERSON>re <PERSON>e - Portal Judiciar România{% endblock %}

{% block meta_description %}Monitorizați dosarele judiciare și primiți notificări automate pentru modificări. Gestionați dosarele urmărite și configurați preferințele de notificare.{% endblock %}

{% block additional_css %}
<style>
.monitoring-dashboard {
    padding: 20px 0;
}

.dashboard-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
    border-radius: 8px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.action-section {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    color: white;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.cases-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.cases-table th,
.cases-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.cases-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.cases-table tbody tr:hover {
    background-color: #f8f9fa;
}

.frequency-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.frequency-immediate {
    background-color: #d4edda;
    color: #155724;
}

.frequency-daily {
    background-color: #d1ecf1;
    color: #0c5460;
}

.frequency-weekly {
    background-color: #fff3cd;
    color: #856404;
}

.changes-list {
    max-height: 400px;
    overflow-y: auto;
}

.change-item {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
}

.change-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.change-case {
    font-weight: 600;
    color: #007bff;
}

.change-date {
    color: #6c757d;
    font-size: 12px;
}

.change-description {
    color: #495057;
    font-size: 14px;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.loading {
    display: none;
    text-align: center;
    padding: 20px;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cases-table {
        font-size: 12px;
    }
    
    .cases-table th,
    .cases-table td {
        padding: 8px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        padding: 20px 0;
    }
    
    .action-section {
        padding: 15px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="monitoring-dashboard">
    <!-- GDPR Consent Modal -->
    {% if not gdpr_consents.data_processing or not gdpr_consents.monitoring %}
    <div class="modal fade" id="gdprConsentModal" tabindex="-1" role="dialog" aria-labelledby="gdprConsentModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="gdprConsentModalLabel">
                        <i class="fas fa-shield-alt"></i> Consimțământ pentru Prelucrarea Datelor
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Protecția datelor dumneavoastră este importantă pentru noi.</strong>
                        Pentru a utiliza sistemul de monitorizare, avem nevoie de consimțământul dumneavoastră pentru prelucrarea datelor personale.
                    </div>

                    <h6><i class="fas fa-list-check"></i> Ce date prelucrăm:</h6>
                    <ul class="mb-3">
                        <li>Adresa de email pentru trimiterea notificărilor</li>
                        <li>Dosarele judiciare pe care le monitorizați</li>
                        <li>Preferințele de notificare</li>
                        <li>Istoricul modificărilor dosarelor</li>
                    </ul>

                    <h6><i class="fas fa-shield-alt"></i> Drepturile dumneavoastră:</h6>
                    <ul class="mb-3">
                        <li>Dreptul de acces la datele personale</li>
                        <li>Dreptul de rectificare a datelor</li>
                        <li>Dreptul la ștergerea datelor</li>
                        <li>Dreptul la portabilitatea datelor</li>
                        <li>Dreptul de a vă retrage consimțământul</li>
                    </ul>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="consentDataProcessing" required>
                        <label class="form-check-label" for="consentDataProcessing">
                            <strong>Sunt de acord cu prelucrarea datelor personale</strong> pentru funcționarea sistemului de monitorizare
                        </label>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="consentMonitoring" required>
                        <label class="form-check-label" for="consentMonitoring">
                            <strong>Sunt de acord cu monitorizarea dosarelor</strong> și primirea de notificări automate
                        </label>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="consentEmailNotifications">
                        <label class="form-check-label" for="consentEmailNotifications">
                            Sunt de acord să primesc notificări prin email (opțional)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="window.location.href='/'">
                        <i class="fas fa-times"></i> Refuz
                    </button>
                    <button type="button" class="btn btn-primary" id="acceptGdprConsent" disabled>
                        <i class="fas fa-check"></i> Accept și Continui
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="dashboard-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1><i class="fas fa-eye"></i> Monitorizare Dosare</h1>
                    <p class="mb-0">Bună ziua, <strong>{{ user_name }}</strong>! Gestionați dosarele monitorizate și configurați notificările.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ cases_count }}</div>
                <div class="stat-label">Dosare Monitorizate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ notification_stats.changes_this_week }}</div>
                <div class="stat-label">Modificări Săptămâna Aceasta</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ notification_stats.sent_notifications }}</div>
                <div class="stat-label">Notificări Trimise</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ notification_stats.pending_notifications }}</div>
                <div class="stat-label">Notificări în Așteptare</div>
            </div>
        </div>

        <!-- Add New Case Section -->
        <div class="action-section">
            <h2 class="section-title"><i class="fas fa-plus-circle"></i> Adaugă Dosar în Monitorizare</h2>
            
            {% if cases_count >= max_cases %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Limită atinsă:</strong> Aveți deja {{ max_cases }} dosare în monitorizare. Pentru a adăuga un dosar nou, eliminați mai întâi unul existent.
                </div>
            {% else %}
                <form id="addCaseForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_tokens.add_case }}">
                    <input type="hidden" name="action" value="add_case">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="case_number">Numărul Dosarului *</label>
                            <input type="text" id="case_number" name="case_number" class="form-control" 
                                   placeholder="ex: 1234/2024" required>
                        </div>
                        <div class="form-group">
                            <label for="institution_code">Instituția *</label>
                            <select id="institution_code" name="institution_code" class="form-control" required>
                                <option value="">Selectați instituția</option>
                                {% for code, name in institutions %}
                                    <option value="{{ code }}">{{ name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="monitoring_reason">Motivul Monitorizării</label>
                            <input type="text" id="monitoring_reason" name="monitoring_reason" class="form-control" 
                                   placeholder="ex: Urmărire termen de judecată">
                        </div>
                        <div class="form-group">
                            <label for="notification_frequency">Frecvența Notificărilor</label>
                            <select id="notification_frequency" name="notification_frequency" class="form-control">
                                <option value="immediate">Imediat</option>
                                <option value="daily" selected>Zilnic</option>
                                <option value="weekly">Săptămânal</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <button type="button" id="checkCaseBtn" class="btn btn-secondary">
                                <i class="fas fa-search"></i> Verifică Dosarul
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Adaugă în Monitorizare
                            </button>
                        </div>
                    </div>
                </form>
                
                <div id="caseCheckResult" style="display: none; margin-top: 15px;"></div>
            {% endif %}
        </div>

        <!-- Monitored Cases List -->
        <div class="action-section">
            <h2 class="section-title"><i class="fas fa-list"></i> Dosarele Monitorizate ({{ cases_count }}/{{ max_cases }})</h2>
            
            {% if monitored_cases|length > 0 %}
                <div class="table-responsive">
                    <table class="cases-table">
                        <thead>
                            <tr>
                                <th>Numărul Dosarului</th>
                                <th>Instituția</th>
                                <th>Adăugat la</th>
                                <th>Frecvența</th>
                                <th>Ultima Verificare</th>
                                <th>Acțiuni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for case in monitored_cases %}
                            <tr>
                                <td>
                                    <strong>{{ case.case_number }}</strong>
                                    {% if case.monitoring_reason %}
                                        <br><small class="text-muted">{{ case.monitoring_reason }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ case.institution_name }}</td>
                                <td>{{ case.created_at|date('d.m.Y H:i') }}</td>
                                <td>
                                    <span class="frequency-badge frequency-{{ case.notification_frequency }}">
                                        {% if case.notification_frequency == 'immediate' %}
                                            Imediat
                                        {% elseif case.notification_frequency == 'daily' %}
                                            Zilnic
                                        {% elseif case.notification_frequency == 'weekly' %}
                                            Săptămânal
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if case.last_checked_at %}
                                        {{ case.last_checked_at|date('d.m.Y H:i') }}
                                    {% else %}
                                        <span class="text-muted">Niciodată</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary update-frequency-btn" 
                                            data-case-id="{{ case.id }}" 
                                            data-current-frequency="{{ case.notification_frequency }}">
                                        <i class="fas fa-bell"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger remove-case-btn" 
                                            data-case-id="{{ case.id }}" 
                                            data-case-number="{{ case.case_number }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Niciun dosar monitorizat:</strong> Adăugați primul dosar pentru a începe monitorizarea automată.
                </div>
            {% endif %}
        </div>

        <!-- Recent Changes -->
        {% if recent_changes|length > 0 %}
        <div class="action-section">
            <h2 class="section-title"><i class="fas fa-history"></i> Modificări Recente</h2>
            
            <div class="changes-list">
                {% for change in recent_changes %}
                <div class="change-item">
                    <div class="change-header">
                        <span class="change-case">{{ change.case_number }}</span>
                        <span class="change-date">{{ change.detected_at|date('d.m.Y H:i') }}</span>
                    </div>
                    <div class="change-description">
                        <strong>{{ change.change_type|title }}:</strong> {{ change.description }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading">
    <div class="spinner"></div>
    <p>Se procesează cererea...</p>
</div>
{% endblock %}

{% block additional_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add case form submission
    const addCaseForm = document.getElementById('addCaseForm');
    if (addCaseForm) {
        addCaseForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitForm(this, 'Dosarul a fost adăugat cu succes în monitorizare!');
        });
    }

    // Check case button
    const checkCaseBtn = document.getElementById('checkCaseBtn');
    if (checkCaseBtn) {
        checkCaseBtn.addEventListener('click', function() {
            const caseNumber = document.getElementById('case_number').value;
            const institutionCode = document.getElementById('institution_code').value;

            if (!caseNumber || !institutionCode) {
                showNotification('Completați numărul dosarului și selectați instituția', 'warning');
                return;
            }

            checkCase(caseNumber, institutionCode);
        });
    }

    // Remove case buttons
    document.querySelectorAll('.remove-case-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const caseId = this.dataset.caseId;
            const caseNumber = this.dataset.caseNumber;

            if (confirm(`Sigur doriți să eliminați dosarul ${caseNumber} din monitorizare?`)) {
                removeCase(caseId);
            }
        });
    });

    // Update frequency buttons
    document.querySelectorAll('.update-frequency-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const caseId = this.dataset.caseId;
            const currentFrequency = this.dataset.currentFrequency;
            showFrequencyModal(caseId, currentFrequency);
        });
    });

    // Auto-refresh statistics every 5 minutes
    setInterval(refreshStats, 300000);
});

function submitForm(form, successMessage) {
    showLoading(true);

    const formData = new FormData(form);

    fetch('monitor.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(successMessage, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        showNotification('Eroare de conexiune', 'danger');
    })
    .finally(() => {
        showLoading(false);
    });
}

function checkCase(caseNumber, institutionCode) {
    showLoading(true);

    const formData = new FormData();
    formData.append('action', 'check_case');
    formData.append('case_number', caseNumber);
    formData.append('institution_code', institutionCode);
    formData.append('csrf_token', csrfTokens.check_case);

    fetch('monitor.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCaseInfo(data.data);
        } else {
            showNotification(data.error || 'Dosarul nu a fost găsit', 'warning');
        }
    })
    .catch(error => {
        showNotification('Eroare la verificarea dosarului', 'danger');
    })
    .finally(() => {
        showLoading(false);
    });
}

function displayCaseInfo(caseData) {
    const resultDiv = document.getElementById('caseCheckResult');
    resultDiv.innerHTML = `
        <div class="alert alert-info">
            <h5><i class="fas fa-check-circle"></i> Dosar găsit!</h5>
            <p><strong>Numărul:</strong> ${caseData.numar || 'N/A'}</p>
            <p><strong>Obiectul:</strong> ${caseData.obiect || 'N/A'}</p>
            <p><strong>Stadiul:</strong> ${caseData.stadiu || 'N/A'}</p>
            ${caseData.data_urmatoarei_sedinte ? `<p><strong>Următoarea ședință:</strong> ${caseData.data_urmatoarei_sedinte}</p>` : ''}
        </div>
    `;
    resultDiv.style.display = 'block';

    // Auto-fill institution name
    const institutionSelect = document.getElementById('institution_code');
    const selectedOption = institutionSelect.options[institutionSelect.selectedIndex];
    if (selectedOption) {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'institution_name';
        hiddenInput.value = selectedOption.text;
        document.getElementById('addCaseForm').appendChild(hiddenInput);
    }
}

function removeCase(caseId) {
    checkRateLimit('remove_case');
    showLoading(true);

    const formData = new FormData();
    formData.append('action', 'remove_case');
    formData.append('case_id', caseId);
    formData.append('csrf_token', csrfTokens.remove_case);

    fetch('monitor.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Dosarul a fost eliminat din monitorizare', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        showNotification('Eroare de conexiune', 'danger');
    })
    .finally(() => {
        showLoading(false);
    });
}

function showFrequencyModal(caseId, currentFrequency) {
    const frequencies = {
        'immediate': 'Imediat',
        'daily': 'Zilnic',
        'weekly': 'Săptămânal'
    };

    const newFrequency = prompt(`Selectați noua frecvență de notificare:\n\n1 - Imediat\n2 - Zilnic\n3 - Săptămânal\n\nIntroduceți numărul opțiunii:`);

    if (newFrequency) {
        const frequencyMap = {'1': 'immediate', '2': 'daily', '3': 'weekly'};
        const selectedFrequency = frequencyMap[newFrequency];

        if (selectedFrequency) {
            updateFrequency(caseId, selectedFrequency);
        } else {
            showNotification('Opțiune invalidă', 'warning');
        }
    }
}

function updateFrequency(caseId, frequency) {
    checkRateLimit('update_frequency');
    showLoading(true);

    const formData = new FormData();
    formData.append('action', 'update_frequency');
    formData.append('case_id', caseId);
    formData.append('frequency', frequency);
    formData.append('csrf_token', csrfTokens.update_frequency);

    fetch('monitor.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Frecvența notificărilor a fost actualizată', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.error || 'A apărut o eroare', 'danger');
        }
    })
    .catch(error => {
        showNotification('Eroare de conexiune', 'danger');
    })
    .finally(() => {
        showLoading(false);
    });
}

function refreshStats() {
    // Refresh page statistics without full reload
    fetch('monitor.php?refresh_stats=1')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.stats) {
            updateStatsDisplay(data.stats);
        }
    })
    .catch(error => {
        console.log('Stats refresh failed:', error);
    });
}

function updateStatsDisplay(stats) {
    const statNumbers = document.querySelectorAll('.stat-number');
    if (statNumbers.length >= 4) {
        statNumbers[1].textContent = stats.changes_this_week || 0;
        statNumbers[2].textContent = stats.sent_notifications || 0;
        statNumbers[3].textContent = stats.pending_notifications || 0;
    }
}

function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = show ? 'block' : 'none';
}

// GDPR Consent Management
document.addEventListener('DOMContentLoaded', function() {
    // Show GDPR modal if consent is needed
    {% if not gdpr_consents.data_processing or not gdpr_consents.monitoring %}
    $('#gdprConsentModal').modal('show');
    {% endif %}

    // Enable/disable accept button based on required checkboxes
    const requiredCheckboxes = ['consentDataProcessing', 'consentMonitoring'];
    const acceptButton = document.getElementById('acceptGdprConsent');

    function updateAcceptButton() {
        const allChecked = requiredCheckboxes.every(id => document.getElementById(id).checked);
        acceptButton.disabled = !allChecked;
    }

    requiredCheckboxes.forEach(id => {
        document.getElementById(id).addEventListener('change', updateAcceptButton);
    });

    // Handle GDPR consent acceptance
    document.getElementById('acceptGdprConsent').addEventListener('click', function() {
        const consents = {
            data_processing: document.getElementById('consentDataProcessing').checked,
            monitoring: document.getElementById('consentMonitoring').checked,
            email_notifications: document.getElementById('consentEmailNotifications').checked
        };

        const formData = new FormData();
        formData.append('action', 'record_gdpr_consent');
        formData.append('consents', JSON.stringify(consents));
        formData.append('csrf_token', '{{ csrf_tokens.add_case }}');

        fetch('monitor.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#gdprConsentModal').modal('hide');
                showNotification('Consimțământul a fost înregistrat cu succes', 'success');
            } else {
                showNotification(data.error || 'Eroare la înregistrarea consimțământului', 'danger');
            }
        })
        .catch(error => {
            showNotification('Eroare de conexiune', 'danger');
        });
    });
});

// Update CSRF tokens for different actions
const csrfTokens = {
    add_case: '{{ csrf_tokens.add_case }}',
    remove_case: '{{ csrf_tokens.remove_case }}',
    update_frequency: '{{ csrf_tokens.update_frequency }}',
    check_case: '{{ csrf_tokens.check_case }}'
};

// Rate limit information
const rateLimits = {
    add_case: {{ rate_limits.add_case|json_encode|raw }},
    remove_case: {{ rate_limits.remove_case|json_encode|raw }},
    update_frequency: {{ rate_limits.update_frequency|json_encode|raw }}
};

// Display rate limit warnings
function checkRateLimit(action) {
    const limit = rateLimits[action];
    if (limit && limit.remaining < 5) {
        showNotification(`Atenție: Mai aveți doar ${limit.remaining} încercări pentru această acțiune.`, 'warning');
    }
}
</script>
{% endblock %}
