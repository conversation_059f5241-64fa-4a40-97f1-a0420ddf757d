<?php

namespace App\Helpers;

/**
 * Performance Helper - Optimizează performanța site-ului pentru SEO
 */
class PerformanceHelper
{
    /**
     * Minifică CSS-ul
     */
    public static function minifyCSS($css)
    {
        // Elimină comentariile
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Elimină spațiile inutile
        $css = str_replace(["\r\n", "\r", "\n", "\t", '  ', '    ', '    '], '', $css);
        
        // Elimină spațiile din jurul caracterelor speciale
        $css = str_replace(['; ', ' ;', ' {', '{ ', '} ', ' }', ': ', ' :', ', ', ' ,'], [';', ';', '{', '{', '}', '}', ':', ':', ',', ','], $css);
        
        return trim($css);
    }
    
    /**
     * Minifică JavaScript-ul (basic)
     */
    public static function minifyJS($js)
    {
        // Elimină comentariile single-line
        $js = preg_replace('/\/\/.*$/m', '', $js);
        
        // Elimină comentariile multi-line
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Elimină spațiile inutile
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Elimină spațiile din jurul caracterelor speciale
        $js = str_replace([' {', '{ ', '} ', ' }', '; ', ' ;', ', ', ' ,', ' = ', ' == ', ' === ', ' != ', ' !== '], ['{', '{', '}', '}', ';', ';', ',', ',', '=', '==', '===', '!=', '!=='], $js);
        
        return trim($js);
    }
    
    /**
     * Generează hash pentru cache busting
     */
    public static function generateCacheHash($filePath)
    {
        if (!file_exists($filePath)) {
            return time();
        }
        
        return md5_file($filePath);
    }
    
    /**
     * Generează URL cu cache busting
     */
    public static function generateCachedURL($url, $filePath = null)
    {
        if ($filePath && file_exists($filePath)) {
            $hash = self::generateCacheHash($filePath);
            $separator = strpos($url, '?') !== false ? '&' : '?';
            return $url . $separator . 'v=' . $hash;
        }
        
        return $url . (strpos($url, '?') !== false ? '&' : '?') . 'v=' . time();
    }
    
    /**
     * Comprimă output-ul HTML
     */
    public static function compressHTML($html)
    {
        // Păstrează conținutul din tag-urile <pre>, <textarea>, <script>
        $preserveTags = [];
        $preservePatterns = [
            '/<pre[^>]*>.*?<\/pre>/is',
            '/<textarea[^>]*>.*?<\/textarea>/is',
            '/<script[^>]*>.*?<\/script>/is'
        ];
        
        foreach ($preservePatterns as $pattern) {
            preg_match_all($pattern, $html, $matches);
            foreach ($matches[0] as $i => $match) {
                $placeholder = "<!--PRESERVE_TAG_$i-->";
                $preserveTags[$placeholder] = $match;
                $html = str_replace($match, $placeholder, $html);
            }
        }
        
        // Elimină comentariile HTML (exceptând IE conditionals)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);
        
        // Elimină spațiile inutile
        $html = preg_replace('/\s+/', ' ', $html);
        
        // Elimină spațiile din jurul tag-urilor
        $html = preg_replace('/>\s+</', '><', $html);
        
        // Restaurează conținutul păstrat
        foreach ($preserveTags as $placeholder => $content) {
            $html = str_replace($placeholder, $content, $html);
        }
        
        return trim($html);
    }
    
    /**
     * Generează header-e pentru cache
     */
    public static function setCacheHeaders($maxAge = 3600, $type = 'public')
    {
        $expires = gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT';
        
        header("Cache-Control: $type, max-age=$maxAge");
        header("Expires: $expires");
        header('Pragma: cache');
        
        // ETag pentru validarea cache-ului
        $etag = md5($_SERVER['REQUEST_URI'] . $maxAge);
        header("ETag: \"$etag\"");
        
        // Verifică If-None-Match header
        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && 
            trim($_SERVER['HTTP_IF_NONE_MATCH'], '"') === $etag) {
            header('HTTP/1.1 304 Not Modified');
            exit;
        }
    }
    
    /**
     * Generează header-e pentru resurse statice
     */
    public static function setStaticResourceHeaders($fileExtension)
    {
        $mimeTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject'
        ];
        
        $cacheTime = [
            'css' => 86400 * 7,  // 1 săptămână
            'js' => 86400 * 7,   // 1 săptămână
            'png' => 86400 * 30, // 1 lună
            'jpg' => 86400 * 30, // 1 lună
            'jpeg' => 86400 * 30, // 1 lună
            'gif' => 86400 * 30, // 1 lună
            'svg' => 86400 * 30, // 1 lună
            'ico' => 86400 * 30, // 1 lună
            'woff' => 86400 * 365, // 1 an
            'woff2' => 86400 * 365, // 1 an
            'ttf' => 86400 * 365, // 1 an
            'eot' => 86400 * 365  // 1 an
        ];
        
        if (isset($mimeTypes[$fileExtension])) {
            header('Content-Type: ' . $mimeTypes[$fileExtension]);
        }
        
        if (isset($cacheTime[$fileExtension])) {
            self::setCacheHeaders($cacheTime[$fileExtension]);
        }
    }
    
    /**
     * Optimizează încărcarea resurselor
     */
    public static function generateResourceHints($resources)
    {
        $hints = [];
        
        foreach ($resources as $resource) {
            $type = $resource['type'] ?? 'prefetch';
            $url = $resource['url'];
            $as = isset($resource['as']) ? ' as="' . $resource['as'] . '"' : '';
            $crossorigin = isset($resource['crossorigin']) ? ' crossorigin' : '';
            
            switch ($type) {
                case 'preload':
                    $hints[] = "<link rel=\"preload\" href=\"$url\"$as$crossorigin>";
                    break;
                case 'prefetch':
                    $hints[] = "<link rel=\"prefetch\" href=\"$url\"$crossorigin>";
                    break;
                case 'preconnect':
                    $hints[] = "<link rel=\"preconnect\" href=\"$url\"$crossorigin>";
                    break;
                case 'dns-prefetch':
                    $hints[] = "<link rel=\"dns-prefetch\" href=\"$url\">";
                    break;
            }
        }
        
        return implode("\n", $hints);
    }
    
    /**
     * Generează Critical CSS inline
     */
    public static function generateCriticalCSS()
    {
        return '
<style>
/* Critical CSS pentru Portal Judiciar */
body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;margin:0;padding:0;background-color:#f8f9fa}
.navbar{background-color:#f8f9fa!important;border-bottom:2px solid #007bff;box-shadow:0 2px 4px rgba(0,0,0,0.1)}
.navbar-brand{font-weight:600;color:#2c3e50!important;font-size:1.25rem}
.btn-primary{background-color:#007bff;border-color:#007bff;color:#fff}
.card{background-color:#fff;border:1px solid rgba(0,0,0,0.125);border-radius:0.375rem;box-shadow:0 0.125rem 0.25rem rgba(0,0,0,0.075)}
.form-control{background-color:#fff;border:1px solid #ced4da;border-radius:0.375rem;padding:0.375rem 0.75rem}
h1,h2,h3,h4,h5,h6{color:#2c3e50;font-weight:600}
.text-primary{color:#007bff!important}
.bg-primary{background-color:#007bff!important}
</style>';
    }
    
    /**
     * Măsoară timpul de execuție
     */
    public static function startTimer()
    {
        return microtime(true);
    }
    
    /**
     * Calculează timpul de execuție
     */
    public static function endTimer($startTime)
    {
        return microtime(true) - $startTime;
    }
    
    /**
     * Generează raport de performanță
     */
    public static function generatePerformanceReport($startTime, $additionalMetrics = [])
    {
        $executionTime = self::endTimer($startTime);
        $memoryUsage = memory_get_peak_usage(true);
        
        $report = [
            'execution_time' => round($executionTime * 1000, 2) . ' ms',
            'memory_usage' => self::formatBytes($memoryUsage),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        return array_merge($report, $additionalMetrics);
    }
    
    /**
     * Formatează bytes în format lizibil
     */
    private static function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Optimizează imaginile pentru WebP
     */
    public static function optimizeImage($imagePath, $quality = 80)
    {
        if (!file_exists($imagePath)) {
            return false;
        }

        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }

        $mimeType = $imageInfo['mime'];
        $webpPath = preg_replace('/\.(jpg|jpeg|png)$/i', '.webp', $imagePath);

        // Verifică dacă WebP există deja și este mai nou
        if (file_exists($webpPath) && filemtime($webpPath) >= filemtime($imagePath)) {
            return $webpPath;
        }

        // Creează imaginea sursă
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                break;
            default:
                return false;
        }

        if (!$image) {
            return false;
        }

        // Convertește la WebP dacă funcția există
        if (function_exists('imagewebp')) {
            $success = imagewebp($image, $webpPath, $quality);
            imagedestroy($image);
            return $success ? $webpPath : false;
        }

        imagedestroy($image);
        return false;
    }

    /**
     * Generează tag-uri pentru imagini responsive cu WebP
     */
    public static function generateResponsiveImageTag($imagePath, $alt = '', $class = '', $sizes = [])
    {
        $webpPath = self::optimizeImage($imagePath);
        $baseUrl = '/just/';

        if (empty($sizes)) {
            $sizes = [
                'sm' => 576,
                'md' => 768,
                'lg' => 992,
                'xl' => 1200
            ];
        }

        $html = '<picture>';

        // WebP sources
        if ($webpPath && file_exists($webpPath)) {
            $html .= '<source type="image/webp" srcset="' . $baseUrl . $webpPath . '">';
        }

        // Fallback
        $html .= '<img src="' . $baseUrl . $imagePath . '" alt="' . htmlspecialchars($alt) . '"';
        if ($class) {
            $html .= ' class="' . htmlspecialchars($class) . '"';
        }
        $html .= ' loading="lazy" decoding="async">';
        $html .= '</picture>';

        return $html;
    }

    /**
     * Setează cache headers pentru resurse externe (CDN)
     */
    public static function setExternalResourceCacheHeaders()
    {
        // Cache pentru 24 ore pentru resurse externe
        self::setCacheHeaders(86400, 'public');

        // Headers pentru CORS și securitate
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET');
        header('Access-Control-Max-Age: 86400');

        // Preconnect hints pentru CDN-uri
        return self::generateResourceHints([
            [
                'type' => 'preconnect',
                'url' => 'https://cdn.jsdelivr.net',
                'crossorigin' => true
            ],
            [
                'type' => 'preconnect',
                'url' => 'https://cdnjs.cloudflare.com',
                'crossorigin' => true
            ],
            [
                'type' => 'dns-prefetch',
                'url' => 'https://fonts.googleapis.com'
            ],
            [
                'type' => 'dns-prefetch',
                'url' => 'https://fonts.gstatic.com'
            ]
        ]);
    }

    /**
     * Comprimă și cache-uiește CSS/JS extern
     */
    public static function cacheExternalResource($url, $type = 'css')
    {
        $cacheDir = __DIR__ . '/../../cache/';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }

        $filename = md5($url) . '.' . $type;
        $cachePath = $cacheDir . $filename;
        $cacheTime = 86400; // 24 ore

        // Verifică dacă cache-ul este valid
        if (file_exists($cachePath) && (time() - filemtime($cachePath)) < $cacheTime) {
            return file_get_contents($cachePath);
        }

        // Descarcă resursa
        $content = @file_get_contents($url);
        if ($content === false) {
            return false;
        }

        // Minifică conținutul
        if ($type === 'css') {
            $content = self::minifyCSS($content);
        } elseif ($type === 'js') {
            $content = self::minifyJS($content);
        }

        // Salvează în cache
        file_put_contents($cachePath, $content);

        return $content;
    }

    /**
     * Formatează dimensiunea fișierului pentru afișare
     */
    public static function formatFileSize($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Generează reguli de cache pentru .htaccess
     */
    public static function generateCacheRules()
    {
        return '
# Performance Optimization - Cache Rules
<IfModule mod_expires.c>
    ExpiresActive On

    # CSS și JavaScript
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"

    # Imagini
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"

    # Fonturi
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"

    # HTML, XML, JSON
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
</IfModule>

<IfModule mod_headers.c>
    # Cache pentru resurse statice
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|woff|woff2)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>

    # Cache pentru HTML
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>

    # Compresie GZIP
    <FilesMatch "\.(css|js|html|htm|xml|json)$">
        Header set Vary "Accept-Encoding"
    </FilesMatch>
</IfModule>

<IfModule mod_deflate.c>
    # Compresie pentru text
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>';
    }
}
