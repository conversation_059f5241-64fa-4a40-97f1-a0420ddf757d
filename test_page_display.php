<?php
/**
 * Test script to check what's actually displayed on the page
 * Case: 130/98/2022 from Tribunalul IALOMIȚA
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test parameters
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<h1>Page Display Test: Missing Parties Investigation</h1>";
echo "<p><strong>Case Number:</strong> {$numarDosar}</p>";
echo "<p><strong>Institution:</strong> {$institutie}</p>";
echo "<hr>";

try {
    // Initialize service
    $dosarService = new DosarService();
    
    // Get case details
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar || empty((array)$dosar)) {
        echo "<p style='color: red;'>ERROR: No case data returned</p>";
        exit;
    }
    
    echo "<h2>Case Information</h2>";
    echo "<ul>";
    echo "<li><strong>Number:</strong> " . htmlspecialchars($dosar->numar ?? 'N/A') . "</li>";
    echo "<li><strong>Institution:</strong> " . htmlspecialchars($dosar->institutie ?? 'N/A') . "</li>";
    echo "<li><strong>Object:</strong> " . htmlspecialchars($dosar->obiect ?? 'N/A') . "</li>";
    echo "</ul>";
    
    echo "<h2>Parties Display Test</h2>";
    
    if (empty($dosar->parti)) {
        echo "<p style='color: red;'>ERROR: No parties found</p>";
    } else {
        $totalParties = count($dosar->parti);
        echo "<p style='color: green;'><strong>Total parties found:</strong> {$totalParties}</p>";
        
        echo "<h3>Simulating Page Display (first 10 parties):</h3>";
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
        echo "<h4 style='display: flex; justify-content: space-between;'>";
        echo "<span><i class='fas fa-users'></i> Părți implicate</span>";
        echo "<span style='background: #6c757d; color: white; padding: 4px 8px; border-radius: 4px;'>{$totalParties} părți</span>";
        echo "</h4>";
        
        echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
        echo "<thead>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Nume</th>";
        echo "<th style='padding: 8px;'>Calitate</th>";
        echo "<th style='padding: 8px;'>Informații suplimentare</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        // Display first 10 parties to test
        $displayCount = min(10, $totalParties);
        for ($i = 0; $i < $displayCount; $i++) {
            $parte = $dosar->parti[$i];
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($parte['nume']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($parte['calitate'] ?? '-') . "</td>";
            echo "<td style='padding: 8px;'>-</td>";
            echo "</tr>";
        }
        
        if ($totalParties > 10) {
            echo "<tr>";
            echo "<td colspan='3' style='padding: 8px; text-align: center; font-style: italic; background: #f0f0f0;'>";
            echo "... și încă " . ($totalParties - 10) . " părți";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        
        echo "<h3>All Parties List (for verification):</h3>";
        echo "<div style='max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;'>";
        echo "<ol>";
        foreach ($dosar->parti as $index => $parte) {
            echo "<li>" . htmlspecialchars($parte['nume']) . " - " . htmlspecialchars($parte['calitate'] ?? 'N/A') . "</li>";
        }
        echo "</ol>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>EXCEPTION: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
