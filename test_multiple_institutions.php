<?php
require_once 'services/SedinteService.php';

echo "Testing multiple institutions with SedinteService...\n\n";

$testInstitutions = [
    'CurteadeApelALBAIULIA',
    'CurteadeApelBUCURESTI', 
    'TribunalulBUCURESTI',
    'JudecatoriaSECTORUL1BUCURESTI',
    'CurteadeApelCLUJ',
    'JudecatoriaCLUJNAPOCA',
    'CurteadeApelTIMISOARA',
    'JudecatoriaTIMISOARA',
    'CurteadeApelCONSTANTA',
    'JudecatoriaCONSTANTA'
];

$sedinteService = new SedinteService();

foreach ($testInstitutions as $institutie) {
    echo "Testing: $institutie\n";
    echo str_repeat("-", 50) . "\n";
    
    try {
        $searchParams = [
            'dataSedinta' => '2024-01-15T00:00:00',
            'institutie' => $institutie
        ];
        
        $results = $sedinteService->cautareSedinte($searchParams);
        
        echo "✅ SUCCESS: " . count($results) . " ședințe găsite\n";
        
        if (!empty($results)) {
            $firstSession = $results[0];
            echo "   Prima ședință: {$firstSession->departament} la {$firstSession->ora}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "\nTesting search without institution (all institutions)...\n";
echo str_repeat("=", 60) . "\n";

try {
    $searchParams = [
        'dataSedinta' => '2024-01-15T00:00:00',
        'institutie' => '' // Testăm cu string gol pentru a vedea cum se comportă
    ];
    
    $results = $sedinteService->cautareSedinte($searchParams);
    
    echo "✅ SUCCESS: " . count($results) . " ședințe găsite în total\n";
    
    // Grupăm rezultatele pe instituții pentru a vedea distribuția
    $institutionCounts = [];
    foreach ($results as $session) {
        if (!empty($session->dosare)) {
            foreach ($session->dosare as $dosar) {
                if (!empty($dosar->institutie)) {
                    $institutionCounts[$dosar->institutie] = ($institutionCounts[$dosar->institutie] ?? 0) + 1;
                }
            }
        }
    }
    
    echo "Distribuție pe instituții:\n";
    arsort($institutionCounts);
    $count = 0;
    foreach ($institutionCounts as $inst => $cnt) {
        echo "   $inst: $cnt ședințe\n";
        if (++$count >= 10) {
            echo "   ... și altele\n";
            break;
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\nTest completed!\n";
?>
