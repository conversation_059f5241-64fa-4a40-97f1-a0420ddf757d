<?php
/**
 * Test page for investigating "Complet" field encoding in SOAP API responses
 * This page examines different encoding approaches for the court composition field
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/SedinteService.php';
require_once 'services/DosarService.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

$testResults = [];
$error = null;

// Test with a specific date to get session data
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['test'])) {
    $testDate = $_POST['testDate'] ?? $_GET['testDate'] ?? date('d.m.Y');
    $testInstitution = $_POST['testInstitution'] ?? $_GET['testInstitution'] ?? '';
    
    try {
        // Test session search to examine Complet field
        $sedinteService = new SedinteService();
        $dateValidation = validateRomanianDate($testDate);
        
        if ($dateValidation['valid']) {
            $searchParams = [
                'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
                'institutie' => $testInstitution ?: null
            ];
            
            $sessions = $sedinteService->cautareSedinte($searchParams);
            
            foreach ($sessions as $index => $session) {
                $testResults[] = [
                    'type' => 'session',
                    'index' => $index + 1,
                    'complet_raw' => $session->complet ?? '',
                    'complet_length' => strlen($session->complet ?? ''),
                    'complet_bytes' => bin2hex($session->complet ?? ''),
                    'complet_utf8_check' => mb_check_encoding($session->complet ?? '', 'UTF-8'),
                    'complet_entities' => htmlentities($session->complet ?? '', ENT_QUOTES, 'UTF-8'),
                    'complet_urlencode' => urlencode($session->complet ?? ''),
                    'departament' => $session->departament ?? '',
                    'data' => $session->data ?? '',
                    'ora' => $session->ora ?? ''
                ];
            }
            
            // Also test case details if we have cases in sessions
            foreach ($sessions as $session) {
                if (!empty($session->dosare)) {
                    foreach ($session->dosare as $dosar) {
                        if (!empty($dosar->numar) && !empty($dosar->institutie)) {
                            try {
                                $dosarService = new DosarService();
                                $dosarDetails = $dosarService->getDetaliiDosar($dosar->numar, $dosar->institutie);
                                
                                if ($dosarDetails && !empty($dosarDetails->sedinte)) {
                                    foreach ($dosarDetails->sedinte as $sedintaIndex => $sedinta) {
                                        $testResults[] = [
                                            'type' => 'case_session',
                                            'case_number' => $dosar->numar,
                                            'session_index' => $sedintaIndex + 1,
                                            'complet_raw' => $sedinta['complet'] ?? '',
                                            'complet_length' => strlen($sedinta['complet'] ?? ''),
                                            'complet_bytes' => bin2hex($sedinta['complet'] ?? ''),
                                            'complet_utf8_check' => mb_check_encoding($sedinta['complet'] ?? '', 'UTF-8'),
                                            'complet_entities' => htmlentities($sedinta['complet'] ?? '', ENT_QUOTES, 'UTF-8'),
                                            'complet_urlencode' => urlencode($sedinta['complet'] ?? ''),
                                            'data' => $sedinta['data'] ?? '',
                                            'ora' => $sedinta['ora'] ?? ''
                                        ];
                                        
                                        // Only test first few to avoid overload
                                        if (count($testResults) > 10) break 3;
                                    }
                                }
                            } catch (Exception $e) {
                                // Continue with other cases
                                continue;
                            }
                        }
                    }
                }
            }
        } else {
            $error = $dateValidation['error'];
        }
    } catch (Exception $e) {
        $error = 'Eroare la testarea encoding-ului: ' . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Encoding Câmp "Complet" - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
        }
        .test-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px 8px 0 0;
        }
        .test-body {
            padding: 1.5rem;
        }
        .encoding-table {
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        .encoding-table td {
            word-break: break-all;
            max-width: 200px;
        }
        .encoding-analysis {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .test-form {
            background: #e9ecef;
            border-radius: 6px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning-message {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .hex-display {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-search mr-2"></i>
                    Test Encoding Câmp "Complet"
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    Investigare encoding și afișare pentru câmpul "Complet" (compoziția completului de judecată)
                </p>
            </div>
            <div class="test-body">
                <div class="test-form">
                    <h4><i class="fas fa-cog mr-2"></i>Parametri de Test</h4>
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="testDate" class="form-label">Data pentru test:</label>
                                <input type="text" class="form-control" id="testDate" name="testDate" 
                                       value="<?php echo htmlspecialchars($_POST['testDate'] ?? date('d.m.Y')); ?>"
                                       placeholder="ZZ.LL.AAAA" pattern="^(\d{1,2})\.(\d{1,2})\.(\d{4})$">
                                <small class="form-text text-muted">Format: ZZ.LL.AAAA (ex: <?php echo date('d.m.Y'); ?>)</small>
                            </div>
                            <div class="col-md-6">
                                <label for="testInstitution" class="form-label">Instituție (opțional):</label>
                                <select class="form-control" id="testInstitution" name="testInstitution">
                                    <option value="">-- Toate instituțiile --</option>
                                    <?php 
                                    $institutii = getInstanteList();
                                    foreach ($institutii as $cod => $nume): 
                                    ?>
                                        <option value="<?php echo htmlspecialchars($cod); ?>"
                                                <?php echo (isset($_POST['testInstitution']) && $_POST['testInstitution'] === $cod) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($nume); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-play mr-2"></i>Rulează Test Encoding
                                </button>
                                <a href="?test=1&testDate=<?php echo date('d.m.Y'); ?>" class="btn btn-secondary ml-2">
                                    <i class="fas fa-calendar mr-2"></i>Test Rapid (Azi)
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <?php if ($error): ?>
                <div class="error-message">
                    <strong>Eroare:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($testResults)): ?>
                <div class="success-message">
                    <strong>✓ Test completat!</strong> Găsite <?php echo count($testResults); ?> ședințe pentru analiză.
                </div>

                <h4><i class="fas fa-table mr-2"></i>Rezultate Analiză Encoding</h4>
                <div class="table-responsive">
                    <table class="table table-striped encoding-table">
                        <thead>
                            <tr>
                                <th>Tip</th>
                                <th>Index/Dosar</th>
                                <th>Complet (Raw)</th>
                                <th>Lungime</th>
                                <th>UTF-8 Valid</th>
                                <th>HTML Entities</th>
                                <th>URL Encoded</th>
                                <th>Hex Bytes</th>
                                <th>Context</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($testResults as $result): ?>
                            <tr>
                                <td>
                                    <?php if ($result['type'] === 'session'): ?>
                                        <span class="badge badge-primary">Ședință</span>
                                    <?php else: ?>
                                        <span class="badge badge-info">Dosar</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($result['type'] === 'session'): ?>
                                        #<?php echo $result['index']; ?>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($result['case_number']); ?><br>
                                        <small>Ședința #<?php echo $result['session_index']; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($result['complet_raw']); ?></strong>
                                    <?php if (empty($result['complet_raw'])): ?>
                                        <em class="text-muted">(gol)</em>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $result['complet_length']; ?> chars</td>
                                <td>
                                    <?php if ($result['complet_utf8_check']): ?>
                                        <span class="text-success">✓ Da</span>
                                    <?php else: ?>
                                        <span class="text-danger">✗ Nu</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo htmlspecialchars($result['complet_entities']); ?></small>
                                </td>
                                <td>
                                    <small><?php echo htmlspecialchars($result['complet_urlencode']); ?></small>
                                </td>
                                <td>
                                    <div class="hex-display">
                                        <?php echo $result['complet_bytes']; ?>
                                    </div>
                                </td>
                                <td>
                                    <small>
                                        <?php echo htmlspecialchars($result['data']); ?><br>
                                        <?php echo htmlspecialchars($result['ora'] ?: 'N/A'); ?><br>
                                        <?php if (isset($result['departament'])): ?>
                                            <?php echo htmlspecialchars($result['departament']); ?>
                                        <?php endif; ?>
                                    </small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="encoding-analysis">
                    <h5><i class="fas fa-chart-bar mr-2"></i>Analiză Encoding</h5>
                    <?php
                    $totalResults = count($testResults);
                    $emptyComplet = array_filter($testResults, function($r) { return empty($r['complet_raw']); });
                    $validUtf8 = array_filter($testResults, function($r) { return $r['complet_utf8_check']; });
                    $uniqueCompletes = array_unique(array_column($testResults, 'complet_raw'));
                    ?>
                    <ul>
                        <li><strong>Total ședințe analizate:</strong> <?php echo $totalResults; ?></li>
                        <li><strong>Câmpuri "Complet" goale:</strong> <?php echo count($emptyComplet); ?> (<?php echo round(count($emptyComplet)/$totalResults*100, 1); ?>%)</li>
                        <li><strong>Encoding UTF-8 valid:</strong> <?php echo count($validUtf8); ?> (<?php echo round(count($validUtf8)/$totalResults*100, 1); ?>%)</li>
                        <li><strong>Valori unice găsite:</strong> <?php echo count($uniqueCompletes); ?></li>
                    </ul>
                    
                    <?php if (count($uniqueCompletes) > 1): ?>
                    <h6>Valori unice pentru "Complet":</h6>
                    <ul>
                        <?php foreach ($uniqueCompletes as $unique): ?>
                        <li>
                            <?php if (empty($unique)): ?>
                                <em>(gol)</em>
                            <?php else: ?>
                                <code><?php echo htmlspecialchars($unique); ?></code>
                                <small class="text-muted">(<?php echo strlen($unique); ?> chars, hex: <?php echo bin2hex($unique); ?>)</small>
                            <?php endif; ?>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php endif; ?>
                </div>

                <?php else: ?>
                <div class="warning-message">
                    <strong>Informație:</strong> Nu au fost găsite ședințe pentru data și instituția specificate. Încercați cu o altă dată sau cu "Toate instituțiile".
                </div>
                <?php endif; ?>

                <div class="mt-4">
                    <h4><i class="fas fa-info-circle mr-2"></i>Informații despre Test</h4>
                    <p>Acest test analizează câmpul "Complet" din răspunsurile SOAP API pentru a identifica:</p>
                    <ul>
                        <li><strong>Encoding-ul caracterelor</strong> - verifică dacă datele sunt în UTF-8 valid</li>
                        <li><strong>Conținutul raw</strong> - afișează exact ce primim de la API</li>
                        <li><strong>Reprezentarea în hex</strong> - pentru analiză detaliată a byte-ilor</li>
                        <li><strong>Transformări de encoding</strong> - HTML entities și URL encoding</li>
                        <li><strong>Consistența datelor</strong> - compară între ședințe și dosare</li>
                    </ul>
                    
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <a href="sedinte.php" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-calendar-alt mr-2"></i>Căutare Ședințe
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="test_export.php" class="btn btn-outline-success btn-block">
                                <i class="fas fa-download mr-2"></i>Test Export
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="test_navigation.php" class="btn btn-outline-info btn-block">
                                <i class="fas fa-route mr-2"></i>Test Navigare
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
