<?php
/**
 * Test Live Web Interface
 * Simulate exactly what happens when user submits search through the web interface
 * This will help identify discrepancies between backend and frontend
 */

echo "🌐 TESTING LIVE WEB INTERFACE SEARCH\n";
echo "===================================\n\n";

$searchTerm = 'SARAGEA TUDORIŢA';
$expectedCase = '130/98/2022';
$expectedInstitution = 'TribunalulIALOMITA';

echo "🔎 Testing live web search for: '$searchTerm'\n";
echo "Expected to find: $expectedCase from $expectedInstitution\n";
echo "=" . str_repeat("=", 60) . "\n";

// Step 1: Simulate POST request to index.php exactly like the web form
echo "🔧 STEP 1: Simulating POST request to index.php\n";

// Set up the exact POST data that would be sent by the web form
$_POST = [
    'bulk_search_terms' => $searchTerm,
    'institutie' => '',
    'categorieInstanta' => '',
    'categorieCaz' => '',
    'dataInceput' => '',
    'dataSfarsit' => ''
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "POST data set:\n";
foreach ($_POST as $key => $value) {
    echo "  - $key: '" . $value . "'\n";
}
echo "\n";

// Step 2: Capture the output from index.php
echo "🔧 STEP 2: Capturing index.php output\n";

ob_start();
try {
    // Include index.php to process the search
    include 'index.php';
    $webOutput = ob_get_contents();
} catch (Exception $e) {
    $webOutput = "Error: " . $e->getMessage();
} finally {
    ob_end_clean();
}

echo "✅ Web interface processing completed\n";
echo "📊 Output length: " . strlen($webOutput) . " characters\n\n";

// Step 3: Analyze the HTML output for search results
echo "🔧 STEP 3: Analyzing HTML output for search results\n";

// Look for the results table
if (strpos($webOutput, 'class="table') !== false) {
    echo "✅ Results table found in output\n";
    
    // Extract table content
    preg_match('/<table[^>]*class="table[^"]*"[^>]*>(.*?)<\/table>/s', $webOutput, $tableMatches);
    
    if (!empty($tableMatches[1])) {
        $tableContent = $tableMatches[1];
        echo "✅ Table content extracted\n";
        
        // Look for our expected case in the table
        $foundExpectedCase = false;
        
        // Check for case number
        if (strpos($tableContent, $expectedCase) !== false) {
            echo "✅ Expected case number '$expectedCase' found in table\n";
            
            // Check for institution
            if (strpos($tableContent, $expectedInstitution) !== false) {
                echo "✅ Expected institution '$expectedInstitution' found in table\n";
                $foundExpectedCase = true;
            } else {
                echo "❌ Expected institution '$expectedInstitution' NOT found in table\n";
            }
        } else {
            echo "❌ Expected case number '$expectedCase' NOT found in table\n";
        }
        
        // Count total rows in results table
        preg_match_all('/<tr[^>]*>/', $tableContent, $rowMatches);
        $totalRows = count($rowMatches[0]) - 1; // Subtract header row
        echo "📊 Total result rows in table: $totalRows\n";
        
        // Extract all case numbers from the table for analysis
        preg_match_all('/href="[^"]*numar=([^&"]+)/', $tableContent, $caseMatches);
        if (!empty($caseMatches[1])) {
            echo "📋 Cases found in table:\n";
            foreach ($caseMatches[1] as $i => $caseNumber) {
                $decodedCase = urldecode($caseNumber);
                echo "  " . ($i + 1) . ". $decodedCase\n";
                
                if ($decodedCase === $expectedCase) {
                    echo "    ✅ THIS IS OUR EXPECTED CASE!\n";
                    $foundExpectedCase = true;
                }
            }
        }
        
        echo "\n🎯 Expected case found in web interface: " . ($foundExpectedCase ? '✅ YES' : '❌ NO') . "\n";
        
    } else {
        echo "❌ Could not extract table content\n";
    }
} else {
    echo "❌ No results table found in output\n";
    
    // Check for "no results" message
    if (strpos($webOutput, 'Nu au fost găsite') !== false || 
        strpos($webOutput, 'Niciun rezultat') !== false) {
        echo "ℹ️  'No results' message found\n";
    }
    
    // Check for error messages
    if (strpos($webOutput, 'Eroare') !== false || 
        strpos($webOutput, 'Error') !== false) {
        echo "⚠️  Error message detected in output\n";
    }
}

// Step 4: Check for JavaScript errors or issues
echo "\n🔧 STEP 4: Checking for JavaScript/frontend issues\n";

// Look for JavaScript errors in the output
if (strpos($webOutput, 'JavaScript') !== false || 
    strpos($webOutput, 'script') !== false) {
    echo "ℹ️  JavaScript code found in output\n";
}

// Check for pagination
if (strpos($webOutput, 'pagination') !== false || 
    strpos($webOutput, 'page-') !== false) {
    echo "ℹ️  Pagination detected - results might be on multiple pages\n";
}

// Step 5: Compare with direct backend call
echo "\n🔧 STEP 5: Comparing with direct backend call\n";

try {
    require_once 'config/config.php';
    require_once 'services/DosarService.php';
    
    $dosarService = new DosarService();
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => ''
    ];
    
    $backendResults = $dosarService->cautareAvansata($searchParams);
    echo "📊 Direct backend results: " . count($backendResults) . "\n";
    
    $backendFoundExpectedCase = false;
    foreach ($backendResults as $dosar) {
        if ($dosar->numar === $expectedCase && $dosar->institutie === $expectedInstitution) {
            $backendFoundExpectedCase = true;
            break;
        }
    }
    
    echo "🎯 Expected case found in backend: " . ($backendFoundExpectedCase ? '✅ YES' : '❌ NO') . "\n";
    
    // Compare results
    if ($backendFoundExpectedCase && !$foundExpectedCase) {
        echo "\n❌ DISCREPANCY CONFIRMED: Backend finds case but web interface doesn't show it\n";
        echo "This indicates a frontend display or processing issue\n";
    } elseif (!$backendFoundExpectedCase && !$foundExpectedCase) {
        echo "\n✅ CONSISTENT: Both backend and frontend don't find the case\n";
    } elseif ($foundExpectedCase) {
        echo "\n✅ SUCCESS: Case found in web interface\n";
    }
    
} catch (Exception $e) {
    echo "❌ Backend comparison error: " . $e->getMessage() . "\n";
}

// Step 6: Save output for detailed analysis
echo "\n🔧 STEP 6: Saving web output for analysis\n";

$outputFile = 'web_interface_output_' . date('Y-m-d_H-i-s') . '.html';
file_put_contents($outputFile, $webOutput);
echo "💾 Web interface output saved to: $outputFile\n";
echo "📏 File size: " . number_format(strlen($webOutput)) . " bytes\n";

// Extract key sections for analysis
$sections = [
    'head' => '/<head[^>]*>(.*?)<\/head>/s',
    'search_form' => '/<form[^>]*search[^>]*>(.*?)<\/form>/s',
    'results_section' => '/<div[^>]*results[^>]*>(.*?)<\/div>/s',
    'table_section' => '/<table[^>]*>(.*?)<\/table>/s'
];

foreach ($sections as $sectionName => $pattern) {
    if (preg_match($pattern, $webOutput, $matches)) {
        echo "✅ $sectionName section found\n";
    } else {
        echo "❌ $sectionName section not found\n";
    }
}

echo "\n🏁 Live web interface test completed.\n";
echo "📋 Summary:\n";
echo "  - Backend finds case: " . ($backendFoundExpectedCase ? 'YES' : 'NO') . "\n";
echo "  - Web interface shows case: " . ($foundExpectedCase ? 'YES' : 'NO') . "\n";
echo "  - Output saved to: $outputFile\n";
?>
