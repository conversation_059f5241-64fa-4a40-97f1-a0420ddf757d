<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modificare detectată în dosar</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #28a745;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .alert-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .alert-box .icon {
            color: #28a745;
            font-size: 20px;
            margin-right: 10px;
        }
        .alert-box h3 {
            margin: 0 0 10px 0;
            color: #155724;
            font-size: 18px;
        }
        .change-details {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .change-details h3 {
            margin: 0 0 15px 0;
            color: #007bff;
            font-size: 18px;
        }
        .detail-row {
            margin-bottom: 10px;
            display: flex;
            flex-wrap: wrap;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
            min-width: 140px;
            margin-right: 10px;
        }
        .detail-value {
            color: #333;
            flex: 1;
        }
        .case-info {
            background-color: #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .case-info h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 16px;
        }
        .action-button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
        .action-button:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .footer p {
            margin: 5px 0;
            font-size: 14px;
            color: #6c757d;
        }
        .footer .portal-name {
            font-weight: 600;
            color: #007bff;
        }
        .change-type {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .change-type.status { background-color: #fff3cd; color: #856404; }
        .change-type.hearing_date { background-color: #d1ecf1; color: #0c5460; }
        .change-type.solution { background-color: #d4edda; color: #155724; }
        .change-type.other { background-color: #e2e3e5; color: #383d41; }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .header {
                padding: 20px 15px;
            }
            .content {
                padding: 20px 15px;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>{{ portal_name }}</h1>
            <p>Modificare detectată în dosar</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                Bună ziua <strong>{{ user_name }}</strong>,
            </div>
            
            <div class="alert-box">
                <span class="icon">🔔</span>
                <h3>Modificare detectată!</h3>
                <p>S-a detectat o modificare în dosarul <strong>{{ case_number }}</strong> de la <strong>{{ institution_name }}</strong>.</p>
            </div>
            
            <div class="change-details">
                <h3>📝 Detalii modificare</h3>
                <div class="detail-row">
                    <span class="detail-label">Tipul modificării:</span>
                    <span class="detail-value">
                        <span class="change-type {{ change_type }}">
                            {% if change_type == 'hearing_date' %}
                                Dată ședință
                            {% elseif change_type == 'status' %}
                                Status dosar
                            {% elseif change_type == 'stage' %}
                                Stadiu procesual
                            {% elseif change_type == 'solution' %}
                                Soluție
                            {% elseif change_type == 'parties' %}
                                Părți
                            {% elseif change_type == 'judge' %}
                                Judecător
                            {% else %}
                                Altă modificare
                            {% endif %}
                        </span>
                    </span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Descrierea:</span>
                    <span class="detail-value">{{ change_description }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Data detectării:</span>
                    <span class="detail-value">{{ detection_date }}</span>
                </div>
                
                {% if change_details %}
                    {% if change_details.old_status and change_details.new_status %}
                    <div class="detail-row">
                        <span class="detail-label">Status anterior:</span>
                        <span class="detail-value">{{ change_details.old_status }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status nou:</span>
                        <span class="detail-value"><strong>{{ change_details.new_status }}</strong></span>
                    </div>
                    {% endif %}
                    
                    {% if change_details.old_date and change_details.new_date %}
                    <div class="detail-row">
                        <span class="detail-label">Data anterioară:</span>
                        <span class="detail-value">{{ change_details.old_date }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Data nouă:</span>
                        <span class="detail-value"><strong>{{ change_details.new_date }}</strong></span>
                    </div>
                    {% endif %}
                    
                    {% if change_details.solutie %}
                    <div class="detail-row">
                        <span class="detail-label">Soluția:</span>
                        <span class="detail-value"><strong>{{ change_details.solutie }}</strong></span>
                    </div>
                    {% endif %}
                {% endif %}
            </div>
            
            <div class="case-info">
                <h4>📋 Informații dosar</h4>
                <div class="detail-row">
                    <span class="detail-label">Numărul dosarului:</span>
                    <span class="detail-value">{{ case_number }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Instanța:</span>
                    <span class="detail-value">{{ institution_name }}</span>
                </div>
                {% if case_object %}
                <div class="detail-row">
                    <span class="detail-label">Obiectul dosarului:</span>
                    <span class="detail-value">{{ case_object }}</span>
                </div>
                {% endif %}
            </div>
            
            <div style="text-align: center;">
                <a href="{{ base_url }}/detalii_dosar.php?numar={{ case_number }}&institutie={{ institution_name|url_encode }}" class="action-button">
                    Vezi detaliile complete ale dosarului
                </a>
            </div>
            
            <p style="margin-top: 30px; font-size: 14px; color: #6c757d;">
                Pentru a gestiona dosarele monitorizate, accesați: 
                <a href="{{ base_url }}/monitor.php" style="color: #007bff;">
                    {{ base_url }}/monitor.php
                </a>
            </p>
        </div>
        
        <div class="footer">
            <p class="portal-name">{{ portal_name }}</p>
            <p>&copy; {{ "now"|date("Y") }} Portal Judiciar România. Toate drepturile rezervate.</p>
            <p>
                <a href="{{ base_url }}" style="color: #007bff; text-decoration: none;">{{ base_url }}</a>
            </p>
        </div>
    </div>
</body>
</html>
