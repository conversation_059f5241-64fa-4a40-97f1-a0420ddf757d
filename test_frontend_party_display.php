<?php
/**
 * Frontend Party Display Investigation
 * Test to verify if all parties are being rendered in the frontend
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test case with known high party count
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Frontend Party Display Investigation</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.code { background: #f1f3f4; padding: 10px; font-family: monospace; white-space: pre-wrap; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.party-row { border-bottom: 1px solid #eee; padding: 5px; }
.party-row:nth-child(even) { background-color: #f9f9f9; }
</style></head><body>";

echo "<h1>🔍 Frontend Party Display Investigation</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Objective:</strong> Verify if all parties are rendered in the frontend</p>";
echo "<hr>";

try {
    // Get case details using DosarService
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar || empty($dosar->parti)) {
        echo "<div class='error'>";
        echo "<h3>❌ No Case Data Found</h3>";
        echo "<p>Could not retrieve case data or no parties found.</p>";
        echo "</div>";
        exit;
    }
    
    $totalParties = count($dosar->parti);
    
    echo "<div class='section'>";
    echo "<h2>📊 Backend Data Analysis</h2>";
    echo "<p><strong>Total Parties in Backend:</strong> {$totalParties}</p>";
    
    if ($totalParties >= 340) {
        echo "<div class='success'>";
        echo "<p>✅ <strong>BACKEND CONFIRMED:</strong> {$totalParties} parties available</p>";
        echo "</div>";
    } elseif ($totalParties == 100) {
        echo "<div class='warning'>";
        echo "<p>⚠️ <strong>BACKEND ISSUE:</strong> Only 100 parties (SOAP API limit)</p>";
        echo "</div>";
    } else {
        echo "<div class='warning'>";
        echo "<p>⚠️ <strong>UNEXPECTED COUNT:</strong> {$totalParties} parties</p>";
        echo "</div>";
    }
    echo "</div>";
    
    // Simulate the exact frontend rendering logic
    echo "<div class='section'>";
    echo "<h2>🎨 Frontend Rendering Simulation</h2>";
    echo "<p>Simulating the exact same rendering logic as detalii_dosar.php:</p>";
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped' id='tabelParti'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>Index</th>";
    echo "<th>Nume</th>";
    echo "<th>Calitate</th>";
    echo "<th>Source</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    // Initialize loop counter exactly like in detalii_dosar.php
    $loop_index = 0;
    $totalPartiCount = count($dosar->parti);
    $renderedCount = 0;
    
    foreach ($dosar->parti as $parteIndex => $parte) {
        $loop_index++;
        $renderedCount++;
        
        echo "<tr class='parte-row' data-index='{$loop_index}' data-party-id='{$parteIndex}'>";
        echo "<td><span class='badge bg-secondary'>{$loop_index}</span></td>";
        echo "<td class='nume-parte'>" . htmlspecialchars($parte['nume']) . "</td>";
        echo "<td class='calitate-parte'>" . htmlspecialchars($parte['calitate'] ?? '-') . "</td>";
        echo "<td><span class='badge bg-info'>" . htmlspecialchars($parte['source'] ?? 'unknown') . "</span></td>";
        echo "</tr>";
        
        // Add a break every 50 parties for readability
        if ($renderedCount % 50 == 0) {
            echo "<tr><td colspan='4' class='text-center bg-light'>";
            echo "<em>--- Rendered {$renderedCount} of {$totalPartiCount} parties ---</em>";
            echo "</td></tr>";
        }
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<p><strong>Rendering Summary:</strong></p>";
    echo "<ul>";
    echo "<li>Total parties in backend: {$totalPartiCount}</li>";
    echo "<li>Parties rendered in table: {$renderedCount}</li>";
    echo "<li>Loop completed successfully: " . ($renderedCount == $totalPartiCount ? "✅ YES" : "❌ NO") . "</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    // JavaScript verification
    echo "<div class='section'>";
    echo "<h2>🔧 JavaScript Verification</h2>";
    echo "<p>Running JavaScript to count actual DOM elements:</p>";
    echo "<div id='jsResults'></div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Exception Occurred:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('🔍 Frontend Party Display Investigation - JavaScript Analysis');";
echo "    ";
echo "    // Count table rows";
echo "    const table = document.getElementById('tabelParti');";
echo "    const tbody = table ? table.querySelector('tbody') : null;";
echo "    const allRows = tbody ? tbody.querySelectorAll('tr') : [];";
echo "    const partyRows = tbody ? tbody.querySelectorAll('tr.parte-row') : [];";
echo "    ";
echo "    console.log('Table analysis:', {";
echo "        tableFound: !!table,";
echo "        tbodyFound: !!tbody,";
echo "        totalRows: allRows.length,";
echo "        partyRows: partyRows.length";
echo "    });";
echo "    ";
echo "    // Display results";
echo "    const resultsDiv = document.getElementById('jsResults');";
echo "    if (resultsDiv) {";
echo "        let html = '<div class=\"alert alert-info\">';";
echo "        html += '<h5>JavaScript DOM Analysis:</h5>';";
echo "        html += '<ul>';";
echo "        html += '<li><strong>Table found:</strong> ' + (table ? 'YES' : 'NO') + '</li>';";
echo "        html += '<li><strong>Total rows in DOM:</strong> ' + allRows.length + '</li>';";
echo "        html += '<li><strong>Party rows (.parte-row):</strong> ' + partyRows.length + '</li>';";
echo "        html += '</ul>';";
echo "        ";
echo "        if (partyRows.length > 0) {";
echo "            html += '<p><strong>First party:</strong> ' + (partyRows[0].querySelector('.nume-parte')?.textContent || 'N/A') + '</p>';";
echo "            html += '<p><strong>Last party:</strong> ' + (partyRows[partyRows.length-1].querySelector('.nume-parte')?.textContent || 'N/A') + '</p>';";
echo "        }";
echo "        ";
echo "        html += '</div>';";
echo "        resultsDiv.innerHTML = html;";
echo "    }";
echo "    ";
echo "    // Check for any hidden rows";
echo "    const hiddenRows = Array.from(partyRows).filter(row => ";
echo "        window.getComputedStyle(row).display === 'none' || ";
echo "        row.style.display === 'none'";
echo "    );";
echo "    ";
echo "    if (hiddenRows.length > 0) {";
echo "        console.warn('Found ' + hiddenRows.length + ' hidden party rows');";
echo "        const warningDiv = document.createElement('div');";
echo "        warningDiv.className = 'alert alert-warning mt-2';";
echo "        warningDiv.innerHTML = '<strong>⚠️ Warning:</strong> Found ' + hiddenRows.length + ' hidden party rows in the DOM';";
echo "        resultsDiv.appendChild(warningDiv);";
echo "    }";
echo "});";
echo "</script>";

echo "</body></html>";
?>
