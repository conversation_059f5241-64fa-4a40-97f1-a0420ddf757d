<?php
/**
 * SOAP API Analysis Tool for Romanian Judicial Portal
 * Comprehensive analysis of CautareSedinte operation and WSDL structure
 */

require_once 'includes/config.php';
require_once 'services/SedinteService.php';

// Set content type for proper display
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOAP API Analysis - Portal Judiciar</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .section-header {
            background: linear-gradient(135deg, #007bff, #2c3e50);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        .data-field {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 0.5rem;
            margin: 0.25rem 0;
        }
        .enhancement-item {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
            padding: 0.5rem;
            margin: 0.25rem 0;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-cogs text-primary"></i>
                    SOAP API Analysis - Portal Judiciar
                </h1>
            </div>
        </div>

        <?php
        try {
            // Initialize SOAP service
            $sedinteService = new SedinteService();
            
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            
            // 1. SOAP Configuration Analysis
            echo '<div class="section-header">';
            echo '<h3><i class="fas fa-server"></i> 1. SOAP Configuration</h3>';
            echo '</div>';
            
            echo '<div class="card mb-4">';
            echo '<div class="card-body">';
            echo '<h5>Endpoint Configuration</h5>';
            echo '<div class="code-block">';
            echo "WSDL URL: " . SOAP_WSDL . "\n";
            echo "Endpoint: " . SOAP_ENDPOINT . "\n";
            echo "Namespace: " . SOAP_NAMESPACE . "\n";
            echo "SOAP Version: 1.2\n";
            echo "Timeout: 30 seconds\n";
            echo "Cache: Disabled (WSDL_CACHE_NONE)\n";
            echo '</div>';
            echo '</div>';
            echo '</div>';
            
            // 2. Available SOAP Methods
            echo '<div class="section-header">';
            echo '<h3><i class="fas fa-list"></i> 2. Available SOAP Methods</h3>';
            echo '</div>';
            
            echo '<div class="card mb-4">';
            echo '<div class="card-body">';
            $methods = $sedinteService->getAvailableMethods();
            if (!empty($methods)) {
                echo '<h5>WSDL Functions (' . count($methods) . ' total)</h5>';
                echo '<div class="code-block">';
                foreach ($methods as $method) {
                    echo htmlspecialchars($method) . "\n";
                }
                echo '</div>';
            } else {
                echo '<div class="alert alert-warning">Could not retrieve SOAP methods</div>';
            }
            echo '</div>';
            echo '</div>';
            
            // 3. SOAP Data Types
            echo '<div class="section-header">';
            echo '<h3><i class="fas fa-database"></i> 3. SOAP Data Types</h3>';
            echo '</div>';
            
            echo '<div class="card mb-4">';
            echo '<div class="card-body">';
            $types = $sedinteService->getAvailableTypes();
            if (!empty($types)) {
                echo '<h5>WSDL Types (' . count($types) . ' total)</h5>';
                echo '<div class="code-block">';
                foreach ($types as $type) {
                    echo htmlspecialchars($type) . "\n";
                }
                echo '</div>';
            } else {
                echo '<div class="alert alert-warning">Could not retrieve SOAP types</div>';
            }
            echo '</div>';
            echo '</div>';
            
            echo '</div>'; // End first column
            echo '<div class="col-md-6">';
            
            // 4. CautareSedinte Operation Analysis
            echo '<div class="section-header">';
            echo '<h3><i class="fas fa-search"></i> 4. CautareSedinte Operation</h3>';
            echo '</div>';
            
            echo '<div class="card mb-4">';
            echo '<div class="card-body">';
            echo '<h5>Parameter Structure</h5>';
            echo '<div class="code-block">';
            echo "Operation: CautareSedinte\n";
            echo "Parameters:\n";
            echo "  - dataSedinta: string (YYYY-MM-DDTHH:mm:ss format)\n";
            echo "  - institutie: string|null (institution code or null for all)\n\n";
            echo "Example Request:\n";
            echo "{\n";
            echo '  "dataSedinta": "2024-01-15T00:00:00",'."\n";
            echo '  "institutie": "TribunalulBUCURESTI"'."\n";
            echo "}\n\n";
            echo "Null Institution Handling:\n";
            echo "- Use null (not empty string) for all institutions\n";
            echo "- Empty string may cause API errors\n";
            echo '</div>';
            echo '</div>';
            echo '</div>';
            
            // 5. Response Structure Analysis
            echo '<div class="section-header">';
            echo '<h3><i class="fas fa-code"></i> 5. Response Structure</h3>';
            echo '</div>';
            
            echo '<div class="card mb-4">';
            echo '<div class="card-body">';
            echo '<h5>CautareSedinteResult Structure</h5>';
            echo '<div class="code-block">';
            echo "Response Root: CautareSedinteResult\n";
            echo "├── Sedinta[] (array of session objects)\n";
            echo "    ├── departament: string (court section)\n";
            echo "    ├── complet: string (judge composition)\n";
            echo "    ├── data: string (session date)\n";
            echo "    ├── ora: string (session time)\n";
            echo "    └── dosare: object\n";
            echo "        └── SedintaDosar[] (array of case objects)\n";
            echo "            ├── numar: string (case number)\n";
            echo "            └── institutie: string (institution code)\n";
            echo '</div>';
            
            echo '<h5 class="mt-3">Data Field Details</h5>';
            echo '<div class="data-field">';
            echo '<strong>departament:</strong> Court section (e.g., "Civil", "Penal", "Comercial")';
            echo '</div>';
            echo '<div class="data-field">';
            echo '<strong>complet:</strong> Judge composition (e.g., "Complet 1", "Judecător unic")';
            echo '</div>';
            echo '<div class="data-field">';
            echo '<strong>data:</strong> Session date in DD.MM.YYYY format';
            echo '</div>';
            echo '<div class="data-field">';
            echo '<strong>ora:</strong> Session time in HH:mm format';
            echo '</div>';
            echo '<div class="data-field">';
            echo '<strong>dosare.SedintaDosar[]:</strong> Array of scheduled cases for this session';
            echo '</div>';
            echo '<div class="data-field">';
            echo '<strong>numar:</strong> Case number (e.g., "1234/2024")';
            echo '</div>';
            echo '<div class="data-field">';
            echo '<strong>institutie:</strong> Institution code for the case';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            
            echo '</div>'; // End second column
            echo '</div>'; // End row
            
            // 6. Current Implementation Review
            echo '<div class="row mt-4">';
            echo '<div class="col-12">';
            echo '<div class="section-header">';
            echo '<h3><i class="fas fa-cog"></i> 6. Current Implementation Review</h3>';
            echo '</div>';
            
            echo '<div class="card mb-4">';
            echo '<div class="card-body">';
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            echo '<h5>Parameter Processing</h5>';
            echo '<ul class="list-group list-group-flush">';
            echo '<li class="list-group-item">✅ Date format conversion (DD.MM.YYYY → YYYY-MM-DDTHH:mm:ss)</li>';
            echo '<li class="list-group-item">✅ Institution parameter null handling</li>';
            echo '<li class="list-group-item">✅ Input validation with Romanian date format</li>';
            echo '<li class="list-group-item">✅ Error handling with retry logic (3 attempts)</li>';
            echo '</ul>';
            echo '</div>';
            echo '<div class="col-md-6">';
            echo '<h5>Response Processing</h5>';
            echo '<ul class="list-group list-group-flush">';
            echo '<li class="list-group-item">✅ Array normalization (single element → array)</li>';
            echo '<li class="list-group-item">✅ Null coalescing for missing fields</li>';
            echo '<li class="list-group-item">✅ Nested dosare array processing</li>';
            echo '<li class="list-group-item">✅ Institution mapping for display</li>';
            echo '</ul>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            
            echo '</div>';
            echo '</div>';
            
            // 7. Enhancement Opportunities
            echo '<div class="row mt-4">';
            echo '<div class="col-12">';
            echo '<div class="section-header">';
            echo '<h3><i class="fas fa-lightbulb"></i> 7. Enhancement Opportunities</h3>';
            echo '</div>';
            
            echo '<div class="card mb-4">';
            echo '<div class="card-body">';
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            echo '<h5>Unused SOAP Response Fields</h5>';
            echo '<div class="enhancement-item">';
            echo '<strong>Judge Information:</strong> Extract individual judge names and roles from complet field';
            echo '</div>';
            echo '<div class="enhancement-item">';
            echo '<strong>Court Clerk Data:</strong> Add grefier (court clerk) information if available in response';
            echo '</div>';
            echo '<div class="enhancement-item">';
            echo '<strong>Session Types:</strong> Identify session categories (civil, penal, comercial)';
            echo '</div>';
            echo '<div class="enhancement-item">';
            echo '<strong>Metadata Fields:</strong> Creation/modification timestamps if available';
            echo '</div>';
            echo '</div>';
            echo '<div class="col-md-6">';
            echo '<h5>Display Enhancements</h5>';
            echo '<div class="enhancement-item">';
            echo '<strong>Case Details:</strong> Add case party information and case status';
            echo '</div>';
            echo '<div class="enhancement-item">';
            echo '<strong>Session Status:</strong> Show if session is confirmed, postponed, or cancelled';
            echo '</div>';
            echo '<div class="enhancement-item">';
            echo '<strong>Room Information:</strong> Display courtroom number if available';
            echo '</div>';
            echo '<div class="enhancement-item">';
            echo '<strong>Duration Estimates:</strong> Show estimated session duration';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            
            echo '</div>';
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">';
            echo '<h4><i class="fas fa-exclamation-triangle"></i> Error</h4>';
            echo '<p>Could not analyze SOAP API: ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
        ?>

        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center">
                    <a href="sedinte.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Back to Sessions Search
                    </a>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-home"></i> Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
