<?php
/**
 * Test SOAP Request Parameters
 * Investigate if there are optional parameters that can control result limits
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';

echo "<h1>SOAP Request Parameters Investigation</h1>";
echo "<p><strong>Purpose:</strong> Test if additional parameters can control result limits or enable pagination</p>";
echo "<hr>";

try {
    // Create SOAP client
    $options = [
        'soap_version' => SOAP_1_2,
        'trace' => true,
        'exceptions' => true,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'connection_timeout' => 15
    ];
    
    $client = new SoapClient(SOAP_WSDL, $options);
    
    echo "<h2>1. Standard Request (Baseline)</h2>";
    
    // Standard request for comparison
    $standardParams = [
        'numarDosar' => '130/98/2022',
        'obiectDosar' => null,
        'numeParte' => null,
        'institutie' => 'TribunalulIALOMITA',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $startTime = microtime(true);
    $response = $client->CautareDosare2($standardParams);
    $endTime = microtime(true);
    
    $standardPartiesCount = 0;
    if (isset($response->CautareDosare2Result->Dosar->parti->DosarParte)) {
        $parti = $response->CautareDosare2Result->Dosar->parti->DosarParte;
        if (!is_array($parti)) {
            $parti = [$parti];
        }
        $standardPartiesCount = count($parti);
    }
    
    echo "<p><strong>Standard request result:</strong> {$standardPartiesCount} parties</p>";
    echo "<p><strong>Response time:</strong> " . round(($endTime - $startTime) * 1000, 2) . "ms</p>";
    
    echo "<h2>2. Testing Additional Parameters</h2>";
    
    // Test various parameter combinations that might affect limits
    $parameterTests = [
        [
            'name' => 'With maxResults parameter',
            'params' => array_merge($standardParams, ['maxResults' => 200]),
            'description' => 'Adding maxResults=200 to see if it increases limit'
        ],
        [
            'name' => 'With limit parameter',
            'params' => array_merge($standardParams, ['limit' => 200]),
            'description' => 'Adding limit=200 to see if it increases limit'
        ],
        [
            'name' => 'With pageSize parameter',
            'params' => array_merge($standardParams, ['pageSize' => 200]),
            'description' => 'Adding pageSize=200 to see if it affects pagination'
        ],
        [
            'name' => 'With top parameter',
            'params' => array_merge($standardParams, ['top' => 200]),
            'description' => 'Adding top=200 (found in WSDL) to see if it affects limit'
        ],
        [
            'name' => 'With count parameter',
            'params' => array_merge($standardParams, ['count' => 200]),
            'description' => 'Adding count=200 to see if it affects result count'
        ],
        [
            'name' => 'With offset parameter',
            'params' => array_merge($standardParams, ['offset' => 0]),
            'description' => 'Adding offset=0 to see if pagination is supported'
        ],
        [
            'name' => 'With page parameter',
            'params' => array_merge($standardParams, ['page' => 1]),
            'description' => 'Adding page=1 to see if pagination is supported'
        ]
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>Test Name</th>";
    echo "<th>Description</th>";
    echo "<th>Parties Count</th>";
    echo "<th>Response Time</th>";
    echo "<th>Status</th>";
    echo "<th>Notes</th>";
    echo "</tr>";
    
    foreach ($parameterTests as $test) {
        $startTime = microtime(true);
        
        try {
            $response = $client->CautareDosare2($test['params']);
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            $partiesCount = 0;
            if (isset($response->CautareDosare2Result->Dosar->parti->DosarParte)) {
                $parti = $response->CautareDosare2Result->Dosar->parti->DosarParte;
                if (!is_array($parti)) {
                    $parti = [$parti];
                }
                $partiesCount = count($parti);
            }
            
            $status = "✓ Success";
            $notes = "";
            
            if ($partiesCount == $standardPartiesCount) {
                $notes = "Same as standard request";
            } elseif ($partiesCount > $standardPartiesCount) {
                $notes = "<strong style='color: green;'>Increased parties count!</strong>";
            } elseif ($partiesCount < $standardPartiesCount) {
                $notes = "<strong style='color: orange;'>Decreased parties count</strong>";
            }
            
            echo "<tr>";
            echo "<td><strong>{$test['name']}</strong></td>";
            echo "<td>{$test['description']}</td>";
            echo "<td style='text-align: center;'>{$partiesCount}</td>";
            echo "<td>{$responseTime}ms</td>";
            echo "<td style='color: green;'>{$status}</td>";
            echo "<td>{$notes}</td>";
            echo "</tr>";
            
        } catch (SoapFault $e) {
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            $status = "✗ SOAP Fault";
            $notes = htmlspecialchars($e->getMessage());
            
            echo "<tr>";
            echo "<td><strong>{$test['name']}</strong></td>";
            echo "<td>{$test['description']}</td>";
            echo "<td style='text-align: center;'>-</td>";
            echo "<td>{$responseTime}ms</td>";
            echo "<td style='color: red;'>{$status}</td>";
            echo "<td style='color: red;'>{$notes}</td>";
            echo "</tr>";
            
        } catch (Exception $e) {
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            $status = "✗ Error";
            $notes = htmlspecialchars($e->getMessage());
            
            echo "<tr>";
            echo "<td><strong>{$test['name']}</strong></td>";
            echo "<td>{$test['description']}</td>";
            echo "<td style='text-align: center;'>-</td>";
            echo "<td>{$responseTime}ms</td>";
            echo "<td style='color: red;'>{$status}</td>";
            echo "<td style='color: red;'>{$notes}</td>";
            echo "</tr>";
        }
        
        // Small delay between requests
        usleep(300000); // 0.3 second delay
    }
    
    echo "</table>";
    
    echo "<h2>3. Raw SOAP Request Analysis</h2>";
    
    // Show the last SOAP request to understand the structure
    $lastRequest = $client->__getLastRequest();
    echo "<h3>Last SOAP Request:</h3>";
    echo "<textarea style='width: 100%; height: 200px;'>" . htmlspecialchars($lastRequest) . "</textarea>";
    
    echo "<h2>4. Conclusion</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff;'>";
    echo "<h3>Parameter Testing Results:</h3>";
    echo "<p>Based on the tests above, we can determine:</p>";
    echo "<ul>";
    echo "<li>Whether additional parameters are accepted by the API</li>";
    echo "<li>Whether any parameters can increase the result limit</li>";
    echo "<li>Whether pagination parameters are supported</li>";
    echo "<li>The exact SOAP request structure being sent</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24;'>Exception:</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Analysis completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
