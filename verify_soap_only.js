/**
 * Script de Verificare SOAP API Exclusiv
 * 
 * Acest script verifică că părțile sunt extrase EXCLUSIV din SOAP API
 * și NU din textul deciziei pentru dosarul specific.
 * 
 * Utilizare: Rulează în consola browser pe pagina de detalii dosar
 */

(function() {
    'use strict';
    
    console.group('🎯 Verificare SOAP API Exclusiv');
    
    // Obține toate rândurile de părți
    const partyRows = document.querySelectorAll('.parte-row');
    const totalParties = partyRows.length;
    
    console.log(`📊 Total părți găsite: ${totalParties}`);
    
    if (totalParties === 0) {
        console.error('❌ Nu au fost găsite părți! Verifică dacă pagina s-a încărcat corect.');
        console.groupEnd();
        return;
    }
    
    // Verifică sursele
    const soapApiParties = document.querySelectorAll('.parte-row[data-source="soap_api"]');
    const decisionTextParties = document.querySelectorAll('.parte-row[data-source="decision_text"]');
    const unknownSourceParties = document.querySelectorAll('.parte-row[data-source="unknown"]');
    
    console.log(`🔍 Distribuția Surselor:`);
    console.log(`  ✅ SOAP API: ${soapApiParties.length}`);
    console.log(`  ❌ Text Decizie: ${decisionTextParties.length} (ar trebui să fie 0)`);
    console.log(`  ❓ Necunoscut: ${unknownSourceParties.length}`);
    
    // Rezultatele verificării
    const allFromSoapApi = soapApiParties.length === totalParties;
    const noneFromDecisionText = decisionTextParties.length === 0;
    const noneFromUnknown = unknownSourceParties.length === 0;
    
    console.log(`\n📋 Rezultatele Verificării:`);
    console.log(`  ${allFromSoapApi ? '✅' : '❌'} Toate părțile din SOAP API: ${allFromSoapApi}`);
    console.log(`  ${noneFromDecisionText ? '✅' : '❌'} NICIO parte din textul deciziei: ${noneFromDecisionText}`);
    console.log(`  ${noneFromUnknown ? '✅' : '❌'} NICIO sursă necunoscută: ${noneFromUnknown}`);
    
    // Verifică conținutul coloanei "Informații suplimentare"
    const infoColumns = document.querySelectorAll('.informatii-suplimentare');
    let apiOfficialCount = 0;
    let decisionExtractionCount = 0;
    
    infoColumns.forEach((col, index) => {
        const text = col.textContent.trim();
        if (text.includes('API oficial')) {
            apiOfficialCount++;
        }
        if (text.includes('Extras din decizie')) {
            decisionExtractionCount++;
        }
    });
    
    console.log(`\n🔍 Analiza Coloanei "Informații suplimentare":`);
    console.log(`  ✅ Indicatori "API oficial": ${apiOfficialCount}`);
    console.log(`  ❌ Indicatori "Extras din decizie": ${decisionExtractionCount} (ar trebui să fie 0)`);
    
    // Verifică fragmente de text legal în numele părților
    const legalTextPatterns = [
        /obligaţia de/i,
        /pune în vedere/i,
        /fixează termen/i,
        /desemnează/i,
        /în temeiul/i,
        /administrator judiciar/i,
        /buletinul procedurilor/i,
        /executorie/i,
        /pronunţată/i,
        /apelul/i,
        /eventualele/i,
        /secţiei/i,
        /civilă/i,
        /revisal/i,
        /ancpi/i,
        /ocpi/i,
        /afp sector/i,
        /ditl sector/i
    ];
    
    let partiesWithLegalText = 0;
    const problematicParties = [];
    
    partyRows.forEach((row, index) => {
        const partyName = row.getAttribute('data-nume') || '';
        const hasLegalText = legalTextPatterns.some(pattern => pattern.test(partyName));
        
        if (hasLegalText) {
            partiesWithLegalText++;
            problematicParties.push({
                index: index + 1,
                name: partyName,
                source: row.getAttribute('data-source')
            });
        }
    });
    
    console.log(`\n🔍 Analiza Fragmentelor de Text Legal:`);
    console.log(`  ${partiesWithLegalText === 0 ? '✅' : '❌'} Părți cu fragmente de text legal: ${partiesWithLegalText} (ar trebui să fie 0)`);
    
    if (problematicParties.length > 0) {
        console.log(`  ⚠️  Părți problematice găsite:`);
        problematicParties.forEach(party => {
            console.log(`    - Partea #${party.index}: "${party.name}" (sursă: ${party.source})`);
        });
    }
    
    // Evaluarea generală
    const isPerfect = allFromSoapApi && noneFromDecisionText && noneFromUnknown && 
                     apiOfficialCount === totalParties && decisionExtractionCount === 0 && 
                     partiesWithLegalText === 0;
    
    console.log(`\n🎯 Evaluarea Generală:`);
    if (isPerfect) {
        console.log(`✅ PERFECT! Toate cerințele sunt îndeplinite:`);
        console.log(`   - Toate ${totalParties} părțile sunt din SOAP API`);
        console.log(`   - NICIO extragere din textul deciziei`);
        console.log(`   - Nume curate ale părților fără text legal`);
        console.log(`   - Atribuire corectă a sursei în interfață`);
        console.log(`   - SOAP API EXCLUSIV funcționează perfect!`);
    } else {
        console.log(`❌ Probleme găsite! Cerințele nu sunt complet îndeplinite:`);
        if (!allFromSoapApi) console.log(`   - Nu toate părțile sunt din SOAP API`);
        if (!noneFromDecisionText) console.log(`   - Unele părți sunt din textul deciziei`);
        if (!noneFromUnknown) console.log(`   - Unele părți au sursă necunoscută`);
        if (apiOfficialCount !== totalParties) console.log(`   - Lipsesc indicatori "API oficial"`);
        if (decisionExtractionCount > 0) console.log(`   - Găsite indicatori "Extras din decizie"`);
        if (partiesWithLegalText > 0) console.log(`   - Găsite fragmente de text legal în numele părților`);
    }
    
    // Informații suplimentare de debug
    if (window.location.search.includes('debug=1')) {
        console.log(`\n🐛 Informații Mod Debug:`);
        console.log(`   - Modul debug este activat`);
        console.log(`   - Verifică comentariile HTML pentru detaliile procesării backend`);
        console.log(`   - Verifică tab-ul Network din browser pentru răspunsurile API`);
        console.log(`   - Verifică log-urile de erori ale serverului pentru deciziile de extragere`);
    }
    
    // Exportă rezultatele pentru analiză ulterioară
    window.soapOnlyVerificationResults = {
        totalParties,
        soapApiParties: soapApiParties.length,
        decisionTextParties: decisionTextParties.length,
        unknownSourceParties: unknownSourceParties.length,
        apiOfficialCount,
        decisionExtractionCount,
        partiesWithLegalText,
        problematicParties,
        isPerfect,
        allFromSoapApi,
        noneFromDecisionText,
        noneFromUnknown,
        testTimestamp: new Date().toISOString()
    };
    
    console.log(`\n📊 Rezultate exportate în: window.soapOnlyVerificationResults`);
    
    // Afișează un mesaj final clar
    if (isPerfect) {
        console.log(`\n🎉 SUCCES COMPLET! SOAP API EXCLUSIV funcționează perfect!`);
    } else {
        console.log(`\n⚠️  ATENȚIE! Încă există probleme care trebuie rezolvate.`);
    }
    
    console.groupEnd();
    
    // Returnează rezumatul pentru utilizare imediată
    return {
        success: isPerfect,
        totalParties,
        soapApiParties: soapApiParties.length,
        decisionTextParties: decisionTextParties.length,
        issues: !isPerfect ? 'Verifică consola pentru detalii' : 'Niciuna',
        message: isPerfect ? 'SOAP API EXCLUSIV funcționează perfect!' : 'Încă există probleme'
    };
})();
