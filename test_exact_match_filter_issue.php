<?php
// Test the exact match filter issue with literal asterisk case
require_once 'bootstrap.php';

use App\Services\DosarService;

echo "<h1>Testing Exact Match Filter Issue</h1>";

$searchTerm = "14096/3/2024*";

try {
    $dosarService = new DosarService();
    
    // Get the actual backend results
    echo "<h2>1. Backend Results Analysis</h2>";
    $backendResults = $dosarService->cautareAvansata(['numarDosar' => $searchTerm]);
    echo "<p>Backend count: " . count($backendResults) . "</p>";
    
    foreach ($backendResults as $index => $dosar) {
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
        echo "<strong>Backend Result #" . ($index + 1) . ":</strong><br>";
        echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
        echo "Institution: " . ($dosar->instanta ?? 'N/A') . "<br>";
        echo "Object: " . substr($dosar->obiect ?? 'N/A', 0, 100) . "...<br>";
        echo "</div>";
    }
    
    // Test the search type detection
    echo "<h2>2. Search Type Detection</h2>";
    
    // Copy the detectSearchType function from index.php
    function detectSearchType($term) {
        $cleanTerm = trim($term, '"\'');
        
        if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
            return 'numarDosar';
        }
        
        if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
            return 'numarDosar';
        }
        
        if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
            return 'numarDosar';
        }
        
        if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
            return 'numarDosar';
        }
        
        return 'numeParte';
    }
    
    $detectedType = detectSearchType($searchTerm);
    echo "<p><strong>Search term:</strong> '$searchTerm'</p>";
    echo "<p><strong>Detected type:</strong> '$detectedType'</p>";
    
    if ($detectedType === 'numarDosar') {
        echo "<p style='color: green;'>✓ Search term correctly detected as case number</p>";
    } else {
        echo "<p style='color: red;'>✗ Search term incorrectly detected as: $detectedType</p>";
    }
    
    // Test each case number individually
    echo "<h2>3. Individual Case Number Analysis</h2>";
    
    foreach ($backendResults as $index => $dosar) {
        $caseNumber = $dosar->numar ?? '';
        $hasAsterisk = strpos($caseNumber, '*') !== false;
        
        echo "<div style='background: " . ($hasAsterisk ? "#fff3cd" : "#f8f9fa") . "; padding: 10px; margin: 5px 0; border: 1px solid " . ($hasAsterisk ? "#ffc107" : "#dee2e6") . ";'>";
        echo "<strong>Case #" . ($index + 1) . ":</strong> $caseNumber<br>";
        echo "<strong>Has asterisk:</strong> " . ($hasAsterisk ? "YES" : "NO") . "<br>";
        echo "<strong>Search type (if assigned):</strong> " . ($dosar->searchType ?? 'NOT SET') . "<br>";
        
        // Simulate how this would be handled by the exact match filter
        if ($detectedType === 'numarDosar') {
            echo "<strong>Exact match filter decision:</strong> <span style='color: green;'>SHOULD BE INCLUDED (case number search)</span><br>";
        } else {
            echo "<strong>Exact match filter decision:</strong> <span style='color: red;'>WOULD BE EXCLUDED (not case number search)</span><br>";
        }
        echo "</div>";
    }
    
    // Test the actual search results structure
    echo "<h2>4. Search Results Structure Test</h2>";
    
    // Simulate the search results structure like in index.php
    $searchResults = [
        [
            'term' => $searchTerm,
            'type' => $detectedType,
            'results' => $backendResults,
            'count' => count($backendResults),
            'error' => null
        ]
    ];
    
    echo "<p>Search results structure:</p>";
    echo "<pre>" . print_r($searchResults, true) . "</pre>";
    
    // Simulate assigning searchType to dosar objects
    foreach ($searchResults as $termResult) {
        foreach ($termResult['results'] as $dosar) {
            $dosar->searchTerm = $termResult['term'];
            $dosar->searchType = $termResult['type'];
        }
    }
    
    echo "<h2>5. Final Analysis</h2>";
    
    $literalAsteriskCase = null;
    foreach ($backendResults as $dosar) {
        if (strpos($dosar->numar, '*') !== false) {
            $literalAsteriskCase = $dosar;
            break;
        }
    }
    
    if ($literalAsteriskCase) {
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<h3>Literal Asterisk Case Found:</h3>";
        echo "<strong>Case Number:</strong> " . ($literalAsteriskCase->numar ?? 'N/A') . "<br>";
        echo "<strong>Institution:</strong> " . ($literalAsteriskCase->instanta ?? 'N/A') . "<br>";
        echo "<strong>Search Term:</strong> " . ($literalAsteriskCase->searchTerm ?? 'N/A') . "<br>";
        echo "<strong>Search Type:</strong> " . ($literalAsteriskCase->searchType ?? 'N/A') . "<br>";
        echo "<strong>Object:</strong> " . substr($literalAsteriskCase->obiect ?? 'N/A', 0, 200) . "...<br>";
        echo "</div>";
        
        if ($literalAsteriskCase->searchType === 'numarDosar') {
            echo "<p style='color: green; font-weight: bold;'>✓ The literal asterisk case should be included by the exact match filter!</p>";
            echo "<p>If it's not showing up, the issue is likely:</p>";
            echo "<ul>";
            echo "<li>The exact match filter is enabled by default</li>";
            echo "<li>There's a JavaScript error preventing proper display</li>";
            echo "<li>The filter logic has a bug for cases with literal asterisks</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>✗ The literal asterisk case has wrong search type: " . ($literalAsteriskCase->searchType ?? 'N/A') . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>No case with literal asterisk found in results</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr><h2>Next Steps</h2>";
echo "<p>If the literal asterisk case has the correct searchType='numarDosar', then the issue is in the JavaScript exact match filter logic.</p>";
echo "<p>Check if the exact match filter is enabled by default or if there's a bug in the filter application.</p>";
?>
