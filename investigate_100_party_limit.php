<?php
/**
 * Comprehensive Investigation of 100-Party Limitation
 * Romanian Judicial Portal - SOAP API Deep Analysis
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test case with known high party count
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<!DOCTYPE html>";
echo "<html><head><title>100-Party Limitation Investigation</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.code { background: #f1f3f4; padding: 10px; font-family: monospace; white-space: pre-wrap; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>🔍 100-Party Limitation Investigation</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Objective:</strong> Identify the exact source of the 100-party limitation</p>";
echo "<hr>";

try {
    // STEP 1: Direct SOAP API Call Analysis
    echo "<div class='section'>";
    echo "<h2>📡 Step 1: Direct SOAP API Call Analysis</h2>";
    
    $soapClient = new SoapClient(SOAP_WSDL, [
        'soap_version' => SOAP_1_2,
        'trace' => true,
        'exceptions' => true,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'connection_timeout' => 30,
        'features' => SOAP_SINGLE_ELEMENT_ARRAYS
    ]);
    
    $params = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "<p><strong>SOAP Parameters:</strong></p>";
    echo "<div class='code'>" . json_encode($params, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
    
    $response = $soapClient->CautareDosare2($params);
    
    // Analyze raw SOAP response
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        foreach ($dosare as $dosar) {
            if ($dosar->numar === $numarDosar) {
                echo "<p><strong>✅ Case Found in SOAP Response</strong></p>";
                
                // Analyze parties structure in raw SOAP response
                if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
                    $rawParties = $dosar->parti->DosarParte;
                    if (!is_array($rawParties)) {
                        $rawParties = [$rawParties];
                    }
                    
                    $rawPartyCount = count($rawParties);
                    echo "<p><strong>Raw SOAP Parties Count:</strong> {$rawPartyCount}</p>";
                    
                    if ($rawPartyCount == 100) {
                        echo "<div class='warning'>";
                        echo "<p>⚠️ <strong>CRITICAL FINDING:</strong> Exactly 100 parties in raw SOAP response!</p>";
                        echo "<p>This indicates the limitation is at the SOAP API level, not in our processing.</p>";
                        echo "</div>";
                    }
                    
                    // Show first and last few parties to verify structure
                    echo "<h3>First 5 Parties from Raw SOAP:</h3>";
                    echo "<table><tr><th>#</th><th>Name</th><th>Quality</th></tr>";
                    for ($i = 0; $i < min(5, $rawPartyCount); $i++) {
                        $party = $rawParties[$i];
                        echo "<tr><td>" . ($i + 1) . "</td>";
                        echo "<td>" . htmlspecialchars($party->nume ?? 'N/A') . "</td>";
                        echo "<td>" . htmlspecialchars($party->calitateParte ?? 'N/A') . "</td></tr>";
                    }
                    echo "</table>";
                    
                    if ($rawPartyCount > 5) {
                        echo "<h3>Last 5 Parties from Raw SOAP:</h3>";
                        echo "<table><tr><th>#</th><th>Name</th><th>Quality</th></tr>";
                        for ($i = max(0, $rawPartyCount - 5); $i < $rawPartyCount; $i++) {
                            $party = $rawParties[$i];
                            echo "<tr><td>" . ($i + 1) . "</td>";
                            echo "<td>" . htmlspecialchars($party->nume ?? 'N/A') . "</td>";
                            echo "<td>" . htmlspecialchars($party->calitateParte ?? 'N/A') . "</td></tr>";
                        }
                        echo "</table>";
                    }
                } else {
                    echo "<p><strong>❌ No parties found in raw SOAP response</strong></p>";
                }
                break;
            }
        }
    }
    echo "</div>";
    
    // STEP 2: DosarService Processing Analysis
    echo "<div class='section'>";
    echo "<h2>⚙️ Step 2: DosarService Processing Analysis</h2>";
    
    $dosarService = new DosarService();
    $processedDosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!empty($processedDosar) && isset($processedDosar->parti)) {
        $processedPartyCount = count($processedDosar->parti);
        echo "<p><strong>Processed Parties Count:</strong> {$processedPartyCount}</p>";
        
        // Compare with raw SOAP count
        if (isset($rawPartyCount)) {
            if ($processedPartyCount > $rawPartyCount) {
                echo "<div class='success'>";
                echo "<p>✅ <strong>HYBRID EXTRACTION WORKING:</strong> Processed count ({$processedPartyCount}) > Raw SOAP count ({$rawPartyCount})</p>";
                echo "<p>Additional parties extracted from decision text: " . ($processedPartyCount - $rawPartyCount) . "</p>";
                echo "</div>";
            } elseif ($processedPartyCount == $rawPartyCount) {
                echo "<div class='warning'>";
                echo "<p>⚠️ <strong>NO ENHANCEMENT:</strong> Processed count equals raw SOAP count</p>";
                echo "<p>Decision text extraction may not be finding additional parties</p>";
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<p>❌ <strong>DATA LOSS:</strong> Processed count ({$processedPartyCount}) < Raw SOAP count ({$rawPartyCount})</p>";
                echo "<p>Deduplication or filtering may be removing valid parties</p>";
                echo "</div>";
            }
        }
        
        // Analyze party sources
        $soapSourceCount = 0;
        $decisionSourceCount = 0;
        
        foreach ($processedDosar->parti as $party) {
            if (isset($party['source'])) {
                if ($party['source'] === 'soap_api') {
                    $soapSourceCount++;
                } elseif ($party['source'] === 'decision_text') {
                    $decisionSourceCount++;
                }
            }
        }
        
        echo "<h3>Party Source Analysis:</h3>";
        echo "<table>";
        echo "<tr><th>Source</th><th>Count</th><th>Percentage</th></tr>";
        echo "<tr><td>SOAP API</td><td>{$soapSourceCount}</td><td>" . round(($soapSourceCount / $processedPartyCount) * 100, 1) . "%</td></tr>";
        echo "<tr><td>Decision Text</td><td>{$decisionSourceCount}</td><td>" . round(($decisionSourceCount / $processedPartyCount) * 100, 1) . "%</td></tr>";
        echo "<tr><td>Unknown/Other</td><td>" . ($processedPartyCount - $soapSourceCount - $decisionSourceCount) . "</td><td>" . round((($processedPartyCount - $soapSourceCount - $decisionSourceCount) / $processedPartyCount) * 100, 1) . "%</td></tr>";
        echo "</table>";
        
    } else {
        echo "<p><strong>❌ No processed case data returned</strong></p>";
    }
    echo "</div>";
    
    // STEP 3: Decision Text Analysis
    echo "<div class='section'>";
    echo "<h2>📄 Step 3: Decision Text Analysis</h2>";
    
    if (isset($dosar) && isset($dosar->continutDecizii)) {
        $decisionText = $dosar->continutDecizii;
        $textLength = strlen($decisionText);
        echo "<p><strong>Decision Text Length:</strong> {$textLength} characters</p>";
        
        if ($textLength > 0) {
            // Look for party-related patterns
            $patterns = [
                'party_mentions' => '/\b[A-ZĂÂÎȘȚŢ][a-zăâîșțţ]+\s+[A-ZĂÂÎȘȚŢ][a-zăâîșțţ]+/u',
                'quality_mentions' => '/reclamant|pârât|intervenient|creditor|debitor/i',
                'dispune_sections' => '/Dispune/i'
            ];
            
            echo "<h3>Decision Text Pattern Analysis:</h3>";
            echo "<table>";
            echo "<tr><th>Pattern</th><th>Matches Found</th></tr>";
            
            foreach ($patterns as $name => $pattern) {
                preg_match_all($pattern, $decisionText, $matches);
                $matchCount = count($matches[0]);
                echo "<tr><td>" . ucfirst(str_replace('_', ' ', $name)) . "</td><td>{$matchCount}</td></tr>";
            }
            echo "</table>";
            
            // Show a sample of the decision text
            echo "<h3>Decision Text Sample (first 500 characters):</h3>";
            echo "<div class='code'>" . htmlspecialchars(substr($decisionText, 0, 500)) . "...</div>";
        } else {
            echo "<p><strong>⚠️ Decision text is empty or not available</strong></p>";
        }
    } else {
        echo "<p><strong>⚠️ No decision text found in SOAP response</strong></p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Exception Occurred:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "</body></html>";
?>
