<?php
/**
 * Portal Judiciar - SMTP Configuration Fixer
 * 
 * Script pentru actualizarea rapidă a configurației SMTP
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

$message = '';
$messageType = '';

// Procesăm formularul de actualizare
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_config'])) {
    $newUsername = trim($_POST['smtp_username']);
    $newPassword = trim($_POST['smtp_password']);
    
    if (!empty($newUsername) && !empty($newPassword)) {
        // Citim fișierul constants.php
        $constantsFile = 'src/Config/constants.php';
        $content = file_get_contents($constantsFile);
        
        if ($content !== false) {
            // Înlocuim username-ul
            $content = preg_replace(
                "/define\('SMTP_USERNAME',\s*'[^']*'\);/",
                "define('SMTP_USERNAME', '" . addslashes($newUsername) . "');",
                $content
            );
            
            // Înlocuim parola
            $content = preg_replace(
                "/define\('SMTP_PASSWORD',\s*'[^']*'\);/",
                "define('SMTP_PASSWORD', '" . addslashes($newPassword) . "');",
                $content
            );
            
            // Salvăm fișierul
            if (file_put_contents($constantsFile, $content)) {
                $message = 'Configurația SMTP a fost actualizată cu succes! Reîncarcă pagina pentru a vedea noile valori.';
                $messageType = 'success';
            } else {
                $message = 'Eroare la salvarea fișierului de configurație.';
                $messageType = 'danger';
            }
        } else {
            $message = 'Eroare la citirea fișierului de configurație.';
            $messageType = 'danger';
        }
    } else {
        $message = 'Te rog să completezi toate câmpurile.';
        $messageType = 'warning';
    }
}

// Verificăm formatul parolei curente
$currentPassword = SMTP_PASSWORD;
$isAppPassword = preg_match('/^[a-z]{16}$/', strtolower(str_replace(' ', '', $currentPassword)));
$isPlaceholder = $currentPassword === 'your-app-password';

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix SMTP Config - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .fix-container {
            max-width: 700px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .fix-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }
        .current-config {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        .password-status {
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        .status-good {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-bad {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .gmail-instructions {
            background: linear-gradient(135deg, #4285f4, #34a853);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .gmail-instructions h5 {
            color: white;
        }
        .gmail-instructions a {
            color: #fff;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="text-center mb-4">
            <h1 class="display-5">
                <i class="fas fa-wrench me-2"></i>
                SMTP Configuration Fixer
            </h1>
            <p class="lead">Actualizează rapid configurația SMTP</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check' : ($messageType === 'danger' ? 'times' : 'exclamation-triangle'); ?> me-2"></i>
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Current Configuration -->
        <div class="fix-section">
            <h3><i class="fas fa-info-circle me-2"></i>Configurația Curentă</h3>
            <div class="current-config">
SMTP_HOST: <?php echo htmlspecialchars(SMTP_HOST); ?>
SMTP_PORT: <?php echo htmlspecialchars(SMTP_PORT); ?>
SMTP_USERNAME: <?php echo htmlspecialchars(SMTP_USERNAME); ?>
SMTP_PASSWORD: <?php echo str_repeat('*', strlen(SMTP_PASSWORD)); ?> (<?php echo strlen(SMTP_PASSWORD); ?> caractere)
CONTACT_EMAIL: <?php echo htmlspecialchars(CONTACT_EMAIL); ?>
            </div>

            <!-- Password Analysis -->
            <?php if ($isPlaceholder): ?>
                <div class="password-status status-bad">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Parolă neconfigurata!</strong><br>
                    Încă folosești valoarea placeholder "your-app-password".
                </div>
            <?php elseif ($isAppPassword): ?>
                <div class="password-status status-good">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Parolă de aplicație detectată!</strong><br>
                    Formatul pare corect (16 caractere lowercase).
                </div>
            <?php else: ?>
                <div class="password-status status-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Posibilă parolă regulată!</strong><br>
                    Gmail necesită o parolă de aplicație pentru SMTP, nu parola contului.
                </div>
            <?php endif; ?>
        </div>

        <!-- Gmail Instructions -->
        <?php if (!$isAppPassword): ?>
        <div class="gmail-instructions">
            <h5><i class="fab fa-google me-2"></i>Cum să creezi o parolă de aplicație Gmail:</h5>
            <ol>
                <li><strong>Accesează:</strong> <a href="https://myaccount.google.com/security" target="_blank">Google Account Security</a></li>
                <li><strong>Activează "2-Step Verification"</strong> dacă nu este deja activă</li>
                <li><strong>Caută "App passwords"</strong> în pagina Security</li>
                <li><strong>Selectează "Mail"</strong> și "Windows Computer"</li>
                <li><strong>Copiază parola generată</strong> (16 caractere, ex: abcdefghijklmnop)</li>
            </ol>
        </div>
        <?php endif; ?>

        <!-- Update Form -->
        <div class="fix-section">
            <h3><i class="fas fa-edit me-2"></i>Actualizează Configurația</h3>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="smtp_username" class="form-label">
                        <i class="fas fa-user me-2"></i>Email (SMTP Username):
                    </label>
                    <input type="email" 
                           class="form-control" 
                           id="smtp_username" 
                           name="smtp_username" 
                           value="<?php echo htmlspecialchars(SMTP_USERNAME); ?>" 
                           required>
                    <div class="form-text">Adresa ta de email Gmail</div>
                </div>

                <div class="mb-3">
                    <label for="smtp_password" class="form-label">
                        <i class="fas fa-key me-2"></i>Parolă (SMTP Password):
                    </label>
                    <input type="password" 
                           class="form-control" 
                           id="smtp_password" 
                           name="smtp_password" 
                           value="<?php echo htmlspecialchars(SMTP_PASSWORD); ?>" 
                           required>
                    <div class="form-text">
                        Pentru Gmail: parola de aplicație de 16 caractere (ex: abcdefghijklmnop)<br>
                        <strong>NU</strong> parola contului Gmail!
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-block">
                    <button type="submit" name="update_config" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Actualizează Configurația
                    </button>
                    <a href="gmail_setup_wizard.php" class="btn btn-info">
                        <i class="fas fa-magic me-2"></i>
                        Wizard Complet
                    </a>
                </div>
            </form>
        </div>

        <!-- Test Buttons -->
        <div class="fix-section">
            <h3><i class="fas fa-vial me-2"></i>Testează Configurația</h3>
            <p>După actualizarea configurației, testează funcționalitatea:</p>
            
            <div class="d-grid gap-2 d-md-block">
                <a href="test_smtp.php" class="btn btn-success">
                    <i class="fas fa-envelope me-2"></i>
                    Test SMTP
                </a>
                <a href="debug_contact.php" class="btn btn-secondary">
                    <i class="fas fa-bug me-2"></i>
                    Debug Complet
                </a>
                <a href="contact.php" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>
                    Testează Formularul
                </a>
            </div>
        </div>

        <!-- Common Issues -->
        <div class="fix-section">
            <h3><i class="fas fa-question-circle me-2"></i>Probleme Comune</h3>
            
            <div class="accordion" id="issuesAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue1">
                            "Could not authenticate" - Eroare de autentificare
                        </button>
                    </h2>
                    <div id="issue1" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                        <div class="accordion-body">
                            <strong>Cauze posibile:</strong>
                            <ul>
                                <li>Folosești parola contului în loc de parola de aplicație</li>
                                <li>Nu ai activat 2-Factor Authentication</li>
                                <li>Parola de aplicație este incorectă</li>
                            </ul>
                            <strong>Soluție:</strong> Creează o parolă de aplicație nouă și actualizează configurația.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue2">
                            "Connection timed out" - Timeout de conexiune
                        </button>
                    </h2>
                    <div id="issue2" class="accordion-collapse collapse" data-bs-parent="#issuesAccordion">
                        <div class="accordion-body">
                            <strong>Cauze posibile:</strong>
                            <ul>
                                <li>Firewall blochează portul 587</li>
                                <li>Furnizorul de hosting blochează SMTP</li>
                                <li>Probleme de rețea</li>
                            </ul>
                            <strong>Soluție:</strong> Încearcă portul 465 sau contactează furnizorul de hosting.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
