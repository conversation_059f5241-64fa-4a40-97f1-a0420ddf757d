<?php
/**
 * Test the enhanced party search through the web interface
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "=== TESTING WEB INTERFACE PARTY SEARCH ===" . PHP_EOL;
echo "Testing search for 'Saragea Tudorita' through web interface logic" . PHP_EOL;
echo PHP_EOL;

$searchTerm = 'Saragea Tudorita';

try {
    // Simulate the web interface search logic
    $dosarService = new DosarService();
    
    $searchParams = [
        'numarDosar' => '',
        'institutie' => '',
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => ''
    ];
    
    // Clean up empty parameters (as done in web interface)
    foreach ($searchParams as $key => $value) {
        if ($value === '') {
            $searchParams[$key] = null;
        }
    }
    
    echo "Search parameters:" . PHP_EOL;
    foreach ($searchParams as $key => $value) {
        echo "  $key: " . ($value ?? 'null') . PHP_EOL;
    }
    echo PHP_EOL;
    
    $results = $dosarService->cautareAvansata($searchParams);
    
    echo "Found " . count($results) . " results" . PHP_EOL;
    echo PHP_EOL;
    
    $expectedCases = [
        'CurteadeApelBUCURESTI - 130/98/2022/a3',
        'CurteadeApelBUCURESTI - 130/98/2022', 
        'CurteadeApelSUCEAVA - 2177/40/2019',
        'TribunalulIALOMITA - 130/98/2022'
    ];
    
    $foundExpected = 0;
    
    foreach ($results as $index => $dosar) {
        $institutie = $dosar->institutie ?? 'Unknown';
        $numar = $dosar->numar ?? 'Unknown';
        $caseIdentifier = "$institutie - $numar";
        
        echo "Result " . ($index + 1) . ": $caseIdentifier" . PHP_EOL;
        
        // Check if this is one of the expected cases
        foreach ($expectedCases as $expectedCase) {
            if (stripos($caseIdentifier, str_replace('**', '', $expectedCase)) !== false) {
                echo "  ✅ EXPECTED CASE!" . PHP_EOL;
                $foundExpected++;
                break;
            }
        }
        
        // Show some matching parties for verification
        $parti = $dosar->parti ?? [];
        $matchCount = 0;
        foreach ($parti as $party) {
            $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
            if (!empty($partyName) && 
                (stripos($partyName, 'SARAGEA') !== false || 
                 stripos($partyName, 'TUDORITA') !== false ||
                 stripos($partyName, 'TUDOREL') !== false)) {
                $matchCount++;
            }
        }
        echo "  Parties with matching components: $matchCount" . PHP_EOL;
        echo PHP_EOL;
    }
    
    echo "=== FINAL RESULT ===" . PHP_EOL;
    echo "Expected cases found: $foundExpected out of " . count($expectedCases) . PHP_EOL;
    
    if ($foundExpected >= 4) {
        echo "🎉 SUCCESS: Enhanced party search is working correctly!" . PHP_EOL;
        echo "The search now returns cases with flexible name component matching." . PHP_EOL;
    } else {
        echo "⚠️ Some expected cases may still be missing." . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
