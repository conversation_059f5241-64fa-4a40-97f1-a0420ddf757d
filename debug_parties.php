<?php
/**
 * Debug script to investigate missing parties issue
 * Case: 130/98/2022 from Tribunalul IALOMIȚA
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test parameters
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<h1>Debug: Missing Parties Investigation</h1>";
echo "<p><strong>Case Number:</strong> {$numarDosar}</p>";
echo "<p><strong>Institution:</strong> {$institutie}</p>";
echo "<hr>";

try {
    // Initialize service
    $dosarService = new DosarService();

    echo "<h2>Step 1: Testing SOAP API Call with Debug</h2>";

    // Get case details with debug information
    $result = $dosarService->getDetaliiDosarWithDebug($numarDosar, $institutie, true);

    if (empty($result['dosar']) || empty((array)$result['dosar'])) {
        echo "<p style='color: red;'>ERROR: No case data returned from SOAP API</p>";
        if (isset($result['debug']['error'])) {
            echo "<p style='color: red;'>Debug Error: " . htmlspecialchars($result['debug']['error']) . "</p>";
        }
        exit;
    }

    $dosar = $result['dosar'];
    $debug = $result['debug'];

    echo "<p style='color: green;'>SUCCESS: Case data retrieved</p>";

    echo "<h2>Step 2: Case Basic Information</h2>";
    echo "<ul>";
    echo "<li><strong>Number:</strong> " . htmlspecialchars($dosar->numar ?? 'N/A') . "</li>";
    echo "<li><strong>Institution:</strong> " . htmlspecialchars($dosar->institutie ?? 'N/A') . "</li>";
    echo "<li><strong>Object:</strong> " . htmlspecialchars($dosar->obiect ?? 'N/A') . "</li>";
    echo "<li><strong>Date:</strong> " . htmlspecialchars($dosar->data ?? 'N/A') . "</li>";
    echo "</ul>";

    echo "<h2>Step 3: Parties Analysis</h2>";

    if (empty($dosar->parti)) {
        echo "<p style='color: red;'>ERROR: No parties found in processed data</p>";
    } else {
        echo "<p style='color: green;'>SUCCESS: Found " . count($dosar->parti) . " parties in processed data</p>";

        echo "<h3>Parties List:</h3>";
        echo "<ol>";
        foreach ($dosar->parti as $index => $parte) {
            echo "<li>";
            echo "<strong>Name:</strong> " . htmlspecialchars($parte['nume'] ?? 'N/A') . "<br>";
            echo "<strong>Quality:</strong> " . htmlspecialchars($parte['calitate'] ?? 'N/A');
            echo "</li>";
        }
        echo "</ol>";
    }

    echo "<h2>Step 4: Raw SOAP Response Analysis</h2>";

    if (isset($debug['raw_dosar'])) {
        $rawDosar = $debug['raw_dosar'];

        echo "<h3>Raw Parties Structure:</h3>";
        if (isset($rawDosar->parti)) {
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
            echo "Raw parties object:\n";
            print_r($rawDosar->parti);
            echo "</pre>";

            // Detailed analysis of parties structure
            echo "<h3>Detailed Parties Structure Analysis:</h3>";
            if (isset($rawDosar->parti->DosarParte)) {
                $rawParti = $rawDosar->parti->DosarParte;
                echo "<p><strong>DosarParte structure type:</strong> " . (is_array($rawParti) ? 'Array' : 'Object') . "</p>";

                if (is_array($rawParti)) {
                    echo "<p><strong>Number of parties in array:</strong> " . count($rawParti) . "</p>";
                    foreach ($rawParti as $index => $parte) {
                        echo "<h4>Party " . ($index + 1) . ":</h4>";
                        echo "<pre style='background: #f0f8ff; padding: 8px; border: 1px solid #ccc;'>";
                        print_r($parte);
                        echo "</pre>";
                    }
                } else {
                    echo "<p><strong>Single party object:</strong></p>";
                    echo "<pre style='background: #f0f8ff; padding: 8px; border: 1px solid #ccc;'>";
                    print_r($rawParti);
                    echo "</pre>";
                }
            } else {
                echo "<p style='color: red;'>No DosarParte found in raw parties data</p>";
            }
        } else {
            echo "<p style='color: red;'>No parties found in raw SOAP response</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>EXCEPTION: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
