<?php

namespace App\Services;

use App\Config\Database;
use App\Services\EmailTemplateEngine;
use Exception;

/**
 * Notification Manager Service
 * 
 * Handles email notification queuing, processing, and delivery
 * for case monitoring system.
 */
class NotificationManager
{
    private $emailTemplateEngine;

    public function __construct()
    {
        $this->emailTemplateEngine = new EmailTemplateEngine();
    }

    /**
     * Queue notification for case added to monitoring
     * 
     * @param int $userId User ID
     * @param int $monitoredCaseId Monitored case ID
     * @param int $changeId Change ID
     * @return int Notification queue ID
     */
    public function queueCaseAddedNotification(int $userId, int $monitoredCaseId, int $changeId): int
    {
        // Get case details
        $case = Database::fetchOne(
            "SELECT * FROM monitored_cases WHERE id = ?",
            [$monitoredCaseId]
        );

        if (!$case) {
            throw new Exception("Monitored case not found");
        }

        // Get user details
        $user = Database::fetchOne(
            "SELECT * FROM users WHERE id = ?",
            [$userId]
        );

        if (!$user) {
            throw new Exception("User not found");
        }

        // Generate email content
        $subject = "Dosar {$case['case_number']} - Adăugat în monitorizare";
        $emailData = [
            'user_name' => $user['first_name'] . ' ' . $user['last_name'],
            'case_number' => $case['case_number'],
            'institution_name' => $case['institution_name'],
            'case_object' => $case['case_object'],
            'monitoring_reason' => $case['monitoring_reason'],
            'notification_frequency' => $case['notification_frequency']
        ];

        $textBody = $this->emailTemplateEngine->renderCaseAddedText($emailData);
        $htmlBody = $this->emailTemplateEngine->renderCaseAddedHtml($emailData);

        // Queue notification
        return Database::insert('notification_queue', [
            'user_id' => $userId,
            'monitored_case_id' => $monitoredCaseId,
            'case_change_id' => $changeId,
            'notification_type' => 'immediate',
            'email_subject' => $subject,
            'email_body' => $textBody,
            'email_html_body' => $htmlBody,
            'priority' => 1,
            'status' => 'pending',
            'scheduled_for' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Queue notification for case change
     * 
     * @param int $userId User ID
     * @param int $monitoredCaseId Monitored case ID
     * @param int $changeId Change ID
     * @return int Notification queue ID
     */
    public function queueChangeNotification(int $userId, int $monitoredCaseId, int $changeId): int
    {
        // Get case and change details
        $sql = "SELECT mc.*, cc.change_type, cc.change_description, cc.change_details
                FROM monitored_cases mc
                JOIN case_changes cc ON cc.monitored_case_id = mc.id
                WHERE mc.id = ? AND cc.id = ?";
        
        $result = Database::fetchOne($sql, [$monitoredCaseId, $changeId]);
        
        if (!$result) {
            throw new Exception("Case or change not found");
        }

        // Get user details
        $user = Database::fetchOne(
            "SELECT * FROM users WHERE id = ?",
            [$userId]
        );

        if (!$user) {
            throw new Exception("User not found");
        }

        // Generate email content
        $subject = "Dosar {$result['case_number']} - {$result['change_description']}";
        $emailData = [
            'user_name' => $user['first_name'] . ' ' . $user['last_name'],
            'case_number' => $result['case_number'],
            'institution_name' => $result['institution_name'],
            'case_object' => $result['case_object'],
            'change_type' => $result['change_type'],
            'change_description' => $result['change_description'],
            'change_details' => json_decode($result['change_details'], true)
        ];

        $textBody = $this->emailTemplateEngine->renderChangeNotificationText($emailData);
        $htmlBody = $this->emailTemplateEngine->renderChangeNotificationHtml($emailData);

        // Queue notification
        return Database::insert('notification_queue', [
            'user_id' => $userId,
            'monitored_case_id' => $monitoredCaseId,
            'case_change_id' => $changeId,
            'notification_type' => 'immediate',
            'email_subject' => $subject,
            'email_body' => $textBody,
            'email_html_body' => $htmlBody,
            'priority' => 1,
            'status' => 'pending',
            'scheduled_for' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Queue daily digest notifications
     * 
     * @return int Number of notifications queued
     */
    public function queueDailyDigestNotifications(): int
    {
        // Get users who want daily digest
        $users = Database::fetchAll(
            "SELECT DISTINCT u.* FROM users u
             JOIN monitored_cases mc ON mc.user_id = u.id
             WHERE u.is_active = 1 
             AND u.email_verified = 1
             AND mc.is_active = 1
             AND mc.notification_frequency = 'daily'
             AND JSON_EXTRACT(u.notification_preferences, '$.daily_digest') = true"
        );

        $queued = 0;
        $scheduledFor = date('Y-m-d 08:00:00'); // 8 AM

        foreach ($users as $user) {
            try {
                // Get user's cases with recent changes
                $cases = $this->getUserCasesForDigest($user['id']);
                
                if (empty($cases)) {
                    continue; // No changes to report
                }

                // Generate digest content
                $subject = "Raport zilnic - Dosarele monitorizate (" . count($cases) . " dosare)";
                $emailData = [
                    'user_name' => $user['first_name'] . ' ' . $user['last_name'],
                    'cases' => $cases,
                    'total_cases' => count($cases),
                    'date' => date('d.m.Y')
                ];

                $textBody = $this->emailTemplateEngine->renderDailyDigestText($emailData);
                $htmlBody = $this->emailTemplateEngine->renderDailyDigestHtml($emailData);

                // Queue notification
                Database::insert('notification_queue', [
                    'user_id' => $user['id'],
                    'monitored_case_id' => 0, // Digest covers multiple cases
                    'case_change_id' => null,
                    'notification_type' => 'daily_digest',
                    'email_subject' => $subject,
                    'email_body' => $textBody,
                    'email_html_body' => $htmlBody,
                    'priority' => 5,
                    'status' => 'pending',
                    'scheduled_for' => $scheduledFor
                ]);

                $queued++;

            } catch (Exception $e) {
                error_log("Failed to queue daily digest for user {$user['id']}: " . $e->getMessage());
            }
        }

        return $queued;
    }

    /**
     * Queue weekly summary notifications
     * 
     * @return int Number of notifications queued
     */
    public function queueWeeklySummaryNotifications(): int
    {
        // Get users who want weekly summary
        $users = Database::fetchAll(
            "SELECT DISTINCT u.* FROM users u
             JOIN monitored_cases mc ON mc.user_id = u.id
             WHERE u.is_active = 1 
             AND u.email_verified = 1
             AND mc.is_active = 1
             AND mc.notification_frequency = 'weekly'
             AND JSON_EXTRACT(u.notification_preferences, '$.weekly_summary') = true"
        );

        $queued = 0;
        $scheduledFor = date('Y-m-d 08:00:00'); // Monday 8 AM

        foreach ($users as $user) {
            try {
                // Get user's cases with changes from last week
                $cases = $this->getUserCasesForWeeklySummary($user['id']);
                
                if (empty($cases)) {
                    continue; // No changes to report
                }

                // Generate summary content
                $subject = "Raport săptămânal - Dosarele monitorizate (" . count($cases) . " dosare)";
                $emailData = [
                    'user_name' => $user['first_name'] . ' ' . $user['last_name'],
                    'cases' => $cases,
                    'total_cases' => count($cases),
                    'week_start' => date('d.m.Y', strtotime('-7 days')),
                    'week_end' => date('d.m.Y')
                ];

                $textBody = $this->emailTemplateEngine->renderWeeklySummaryText($emailData);
                $htmlBody = $this->emailTemplateEngine->renderWeeklySummaryHtml($emailData);

                // Queue notification
                Database::insert('notification_queue', [
                    'user_id' => $user['id'],
                    'monitored_case_id' => 0, // Summary covers multiple cases
                    'case_change_id' => null,
                    'notification_type' => 'weekly_summary',
                    'email_subject' => $subject,
                    'email_body' => $textBody,
                    'email_html_body' => $htmlBody,
                    'priority' => 7,
                    'status' => 'pending',
                    'scheduled_for' => $scheduledFor
                ]);

                $queued++;

            } catch (Exception $e) {
                error_log("Failed to queue weekly summary for user {$user['id']}: " . $e->getMessage());
            }
        }

        return $queued;
    }

    /**
     * Process pending notifications
     * 
     * @param int $limit Maximum number of notifications to process
     * @return array Processing summary
     */
    public function processPendingNotifications(int $limit = NOTIFICATION_BATCH_SIZE): array
    {
        $summary = [
            'processed' => 0,
            'sent' => 0,
            'failed' => 0,
            'errors' => []
        ];

        // Get pending notifications
        $notifications = Database::fetchAll(
            "SELECT nq.*, u.email, u.first_name, u.last_name
             FROM notification_queue nq
             JOIN users u ON u.id = nq.user_id
             WHERE nq.status = 'pending' 
             AND nq.scheduled_for <= NOW()
             AND nq.attempts < nq.max_attempts
             ORDER BY nq.priority ASC, nq.scheduled_for ASC
             LIMIT ?",
            [$limit]
        );

        foreach ($notifications as $notification) {
            $summary['processed']++;
            
            try {
                // Mark as processing
                Database::update('notification_queue',
                    ['status' => 'processing', 'attempts' => $notification['attempts'] + 1],
                    ['id' => $notification['id']]
                );

                // Send email
                $sent = $this->sendEmail(
                    $notification['email'],
                    $notification['email_subject'],
                    $notification['email_body'],
                    $notification['email_html_body']
                );

                if ($sent) {
                    // Mark as sent
                    Database::update('notification_queue',
                        ['status' => 'sent', 'sent_at' => date('Y-m-d H:i:s')],
                        ['id' => $notification['id']]
                    );
                    $summary['sent']++;
                } else {
                    throw new Exception("Email sending failed");
                }

            } catch (Exception $e) {
                $summary['failed']++;
                $summary['errors'][] = "Notification {$notification['id']}: " . $e->getMessage();

                // Update failure status
                $status = ($notification['attempts'] + 1 >= $notification['max_attempts']) ? 'failed' : 'pending';
                Database::update('notification_queue',
                    [
                        'status' => $status,
                        'error_message' => $e->getMessage(),
                        'scheduled_for' => date('Y-m-d H:i:s', strtotime('+' . (($notification['attempts'] + 1) * 5) . ' minutes'))
                    ],
                    ['id' => $notification['id']]
                );
            }
        }

        return $summary;
    }

    /**
     * Get user cases for daily digest
     * 
     * @param int $userId User ID
     * @return array Cases with recent changes
     */
    private function getUserCasesForDigest(int $userId): array
    {
        return Database::fetchAll(
            "SELECT mc.*, 
                    COUNT(cc.id) as changes_count,
                    GROUP_CONCAT(cc.change_description SEPARATOR '; ') as recent_changes
             FROM monitored_cases mc
             LEFT JOIN case_changes cc ON cc.monitored_case_id = mc.id 
                 AND cc.detected_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
             WHERE mc.user_id = ? AND mc.is_active = 1
             GROUP BY mc.id
             HAVING changes_count > 0
             ORDER BY changes_count DESC, mc.case_number",
            [$userId]
        );
    }

    /**
     * Get user cases for weekly summary
     * 
     * @param int $userId User ID
     * @return array Cases with changes from last week
     */
    private function getUserCasesForWeeklySummary(int $userId): array
    {
        return Database::fetchAll(
            "SELECT mc.*, 
                    COUNT(cc.id) as changes_count,
                    GROUP_CONCAT(cc.change_description SEPARATOR '; ') as recent_changes
             FROM monitored_cases mc
             LEFT JOIN case_changes cc ON cc.monitored_case_id = mc.id 
                 AND cc.detected_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             WHERE mc.user_id = ? AND mc.is_active = 1
             GROUP BY mc.id
             HAVING changes_count > 0
             ORDER BY changes_count DESC, mc.case_number",
            [$userId]
        );
    }

    /**
     * Send email using PHP mail function
     * 
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $textBody Plain text body
     * @param string $htmlBody HTML body
     * @return bool Success status
     */
    private function sendEmail(string $to, string $subject, string $textBody, string $htmlBody = null): bool
    {
        $headers = [
            'From: ' . CONTACT_NAME . ' <' . CONTACT_EMAIL . '>',
            'Reply-To: ' . CONTACT_EMAIL,
            'X-Mailer: Portal Judiciar Monitoring System',
            'MIME-Version: 1.0'
        ];

        if ($htmlBody) {
            $boundary = uniqid('boundary_');
            $headers[] = 'Content-Type: multipart/alternative; boundary="' . $boundary . '"';
            
            $body = "--{$boundary}\r\n";
            $body .= "Content-Type: text/plain; charset=UTF-8\r\n";
            $body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
            $body .= $textBody . "\r\n\r\n";
            
            $body .= "--{$boundary}\r\n";
            $body .= "Content-Type: text/html; charset=UTF-8\r\n";
            $body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
            $body .= $htmlBody . "\r\n\r\n";
            
            $body .= "--{$boundary}--";
        } else {
            $headers[] = 'Content-Type: text/plain; charset=UTF-8';
            $body = $textBody;
        }

        return mail($to, $subject, $body, implode("\r\n", $headers));
    }

    /**
     * Process notification queue for cron job
     *
     * @param int $limit Maximum number of notifications to process
     * @return int Number of notifications processed
     */
    public function processQueue(int $limit = 50): int
    {
        $summary = $this->processPendingNotifications($limit);
        return $summary['processed'];
    }

    /**
     * Queue a case change notification
     *
     * @param int $userId User ID
     * @param string $caseNumber Case number
     * @param string $institutionCode Institution code
     * @return bool Success status
     */
    public function queueCaseChangeNotification(int $userId, string $caseNumber, string $institutionCode): bool
    {
        try {
            // Get the monitored case ID
            $monitoredCase = Database::fetchOne(
                "SELECT id FROM monitored_cases WHERE user_id = ? AND case_number = ? AND institution_code = ?",
                [$userId, $caseNumber, $institutionCode]
            );

            if (!$monitoredCase) {
                return false;
            }

            // Get the latest case change
            $latestChange = Database::fetchOne(
                "SELECT id FROM case_changes WHERE monitored_case_id = ? ORDER BY created_at DESC LIMIT 1",
                [$monitoredCase['id']]
            );

            if (!$latestChange) {
                return false;
            }

            // Generate email content
            $emailData = [
                'user_id' => $userId,
                'case_number' => $caseNumber,
                'institution_code' => $institutionCode,
                'change_id' => $latestChange['id']
            ];

            $subject = "Modificare detectată - Dosarul {$caseNumber}";
            $textBody = $this->emailTemplateEngine->renderChangeNotificationText($emailData);
            $htmlBody = $this->emailTemplateEngine->renderChangeNotificationHtml($emailData);

            // Queue the notification
            Database::insert('notification_queue', [
                'user_id' => $userId,
                'monitored_case_id' => $monitoredCase['id'],
                'case_change_id' => $latestChange['id'],
                'notification_type' => 'case_change',
                'email_subject' => $subject,
                'email_body' => $textBody,
                'email_html_body' => $htmlBody,
                'priority' => 1, // High priority for immediate notifications
                'status' => 'pending',
                'scheduled_for' => date('Y-m-d H:i:s')
            ]);

            return true;

        } catch (Exception $e) {
            error_log("Failed to queue case change notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up old notification logs
     *
     * @param int $daysToKeep Number of days to keep logs
     * @return int Number of records deleted
     */
    public function cleanupOldLogs(int $daysToKeep = 90): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));

        // Clean up old notification queue entries that are completed or failed
        $deletedQueue = Database::execute(
            "DELETE FROM notification_queue WHERE created_at < ? AND status IN ('sent', 'failed')",
            [$cutoffDate]
        );

        // Clean up old notification logs
        $deletedLogs = Database::execute(
            "DELETE FROM notification_logs WHERE created_at < ?",
            [$cutoffDate]
        );

        return $deletedQueue + $deletedLogs;
    }
}
