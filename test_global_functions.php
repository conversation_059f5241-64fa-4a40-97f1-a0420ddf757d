<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <title>Test Funcții Globale - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 15px 0; background: #f8f9fa; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
        
        /* CSS pentru filtrele avansate */
        #advancedFilters {
            overflow: hidden;
            transition: all 0.3s ease;
            max-height: 0;
            opacity: 0;
            padding: 0;
            margin-top: 0;
            border: none;
            background-color: transparent;
        }

        #advancedFilters.show {
            max-height: 1000px;
            opacity: 1;
            padding: 1.25rem;
            margin-top: 1.25rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: rgba(0, 123, 255, 0.02);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Test Funcții Globale Portal Judiciar</h1>
        
        <div class="test-section">
            <h3>Test Butoane cu onclick</h3>
            
            <!-- Test buttons with onclick handlers -->
            <div class="mb-3">
                <button type="button" class="btn btn-primary me-2" onclick="window.expandAllResults()">
                    <i class="fas fa-expand-alt me-1"></i>
                    Test Expandează toate
                </button>
                <button type="button" class="btn btn-secondary me-2" onclick="window.collapseAllResults()">
                    <i class="fas fa-compress-alt me-1"></i>
                    Test Restrânge toate
                </button>
                <button type="button" class="btn btn-info me-2" onclick="window.toggleTermResults(1)">
                    <i class="fas fa-toggle-on me-1"></i>
                    Test Toggle Term 1
                </button>
                <button type="button" class="btn btn-success" onclick="window.toggleAdvancedFilters(event)">
                    <i class="fas fa-filter me-1"></i>
                    Test Toggle Filtre
                </button>
            </div>
            
            <!-- Advanced Filters Section -->
            <div id="advancedFilters" class="mt-4">
                <h6 class="mb-3">
                    <i class="fas fa-filter me-2"></i>
                    Filtre avansate de test
                </h6>
                <p>Această secțiune ar trebui să se afișeze/ascundă când apăsați butonul de toggle.</p>
            </div>
            
            <!-- Test content for toggle -->
            <div id="termContent1" style="display: none; border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
                <p>Conținut de test pentru toggle term 1</p>
                <p>Această secțiune ar trebui să se afișeze/ascundă când apăsați butonul Toggle Term 1.</p>
            </div>
            
            <div id="testResult" class="mt-3"></div>
            
            <div class="mt-3">
                <button onclick="checkGlobalFunctions()" class="btn btn-warning">Verifică Funcții Globale</button>
                <button onclick="testConsoleOutput()" class="btn btn-dark">Test Console Output</button>
            </div>
        </div>
    </div>

    <script>
        // Simulăm funcțiile globale pentru test
        window.showNotification = function(message, type = 'info') {
            const result = document.getElementById('testResult');
            const alertClass = type === 'success' ? 'success' : type === 'danger' ? 'error' : type === 'warning' ? 'warning' : 'success';
            result.innerHTML = `<div class="${alertClass}">📢 ${message}</div>`;
            console.log(`Notification: ${message} (${type})`);
        };

        // Simulăm funcțiile de expand/collapse
        window.expandAllResults = function() {
            console.log('expandAllResults called');
            const content = document.getElementById('termContent1');
            if (content) {
                content.style.display = 'block';
                showNotification('Toate secțiunile au fost expandate.', 'success');
            }
        };

        window.collapseAllResults = function() {
            console.log('collapseAllResults called');
            const content = document.getElementById('termContent1');
            if (content) {
                content.style.display = 'none';
                showNotification('Toate secțiunile au fost restrânse.', 'info');
            }
        };

        window.toggleTermResults = function(index) {
            console.log('toggleTermResults called with index:', index);
            const content = document.getElementById('termContent' + index);
            if (content) {
                if (content.style.display === 'none' || content.style.display === '') {
                    content.style.display = 'block';
                    showNotification(`Secțiunea ${index} a fost expandată.`, 'success');
                } else {
                    content.style.display = 'none';
                    showNotification(`Secțiunea ${index} a fost restrânsă.`, 'info');
                }
            }
        };

        window.toggleAdvancedFilters = function(event) {
            console.log('toggleAdvancedFilters called');
            if (event) event.preventDefault();
            
            const filtersSection = document.getElementById('advancedFilters');
            if (filtersSection) {
                const isVisible = filtersSection.classList.contains('show');
                if (isVisible) {
                    filtersSection.classList.remove('show');
                    showNotification('Filtrele avansate au fost ascunse.', 'info');
                } else {
                    filtersSection.classList.add('show');
                    showNotification('Filtrele avansate au fost afișate.', 'success');
                }
            }
        };

        function checkGlobalFunctions() {
            let html = '<div class="mt-2"><strong>Status Funcții Globale:</strong><br>';
            
            const functions = [
                'expandAllResults',
                'collapseAllResults', 
                'toggleTermResults',
                'toggleAdvancedFilters',
                'showNotification'
            ];
            
            functions.forEach(func => {
                const exists = typeof window[func] === 'function';
                html += `${func}: ${exists ? '✅ Disponibilă' : '❌ Lipsește'}<br>`;
            });
            
            html += '</div>';
            document.getElementById('testResult').innerHTML = html;
        }

        function testConsoleOutput() {
            console.log('=== Test Console Output ===');
            console.log('Testing global functions...');
            
            try {
                console.log('1. Testing expandAllResults...');
                if (typeof window.expandAllResults === 'function') {
                    window.expandAllResults();
                } else {
                    console.error('expandAllResults not found');
                }
                
                console.log('2. Testing collapseAllResults...');
                if (typeof window.collapseAllResults === 'function') {
                    window.collapseAllResults();
                } else {
                    console.error('collapseAllResults not found');
                }
                
                console.log('3. Testing toggleTermResults...');
                if (typeof window.toggleTermResults === 'function') {
                    window.toggleTermResults(1);
                } else {
                    console.error('toggleTermResults not found');
                }
                
                console.log('4. Testing toggleAdvancedFilters...');
                if (typeof window.toggleAdvancedFilters === 'function') {
                    window.toggleAdvancedFilters();
                } else {
                    console.error('toggleAdvancedFilters not found');
                }
                
                showNotification('Test console output completat. Verificați consola pentru detalii.', 'success');
            } catch (error) {
                console.error('Error during console test:', error);
                showNotification('Eroare în timpul testului console.', 'danger');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            checkGlobalFunctions();
        });
    </script>
</body>
</html>
