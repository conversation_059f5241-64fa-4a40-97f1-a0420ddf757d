<?php
/**
 * Simple test to trigger debug output and see what's happening
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🐛 Debug Output Test</h1>\n";

try {
    $dosarService = new DosarService();
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    echo "<p>Testing case: {$numarDosar} from {$institutie}</p>\n";
    
    // Clear any previous error log entries
    error_log("=== DEBUG TEST START ===");
    
    // Get case details - this should trigger our debug logging
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    error_log("=== DEBUG TEST END ===");
    
    if ($dosar && !empty($dosar->numar)) {
        $totalParties = count($dosar->parti ?? []);
        echo "<p><strong>Result:</strong> {$totalParties} parties found</p>\n";
        
        // Count by source
        $soapCount = 0;
        $decisionCount = 0;
        foreach ($dosar->parti as $parte) {
            if ($parte->source === 'soap_api') $soapCount++;
            if ($parte->source === 'decision_text') $decisionCount++;
        }
        
        echo "<p><strong>SOAP API parties:</strong> {$soapCount}</p>\n";
        echo "<p><strong>Decision text parties:</strong> {$decisionCount}</p>\n";
        
        if ($decisionCount > 0) {
            echo "<p style='color: green;'>✅ Decision text extraction is working!</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Decision text extraction found 0 parties</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Case not found</p>\n";
    }
    
    echo "<p><strong>Check the error log for debug information.</strong></p>\n";
    
    // Try to read recent error log entries if possible
    $errorLogPath = ini_get('error_log');
    if ($errorLogPath && file_exists($errorLogPath)) {
        echo "<h3>Recent Error Log Entries:</h3>\n";
        $logContent = file_get_contents($errorLogPath);
        $lines = explode("\n", $logContent);
        $recentLines = array_slice($lines, -20); // Last 20 lines
        
        echo "<div style='background: #f0f0f0; padding: 10px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;'>\n";
        foreach ($recentLines as $line) {
            if (strpos($line, 'DEBUG:') !== false) {
                echo "<div style='color: blue;'>" . htmlspecialchars($line) . "</div>\n";
            } elseif (strpos($line, 'ERROR') !== false) {
                echo "<div style='color: red;'>" . htmlspecialchars($line) . "</div>\n";
            } else {
                echo "<div>" . htmlspecialchars($line) . "</div>\n";
            }
        }
        echo "</div>\n";
    } else {
        echo "<p>Error log path: " . ($errorLogPath ?: 'Not configured') . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
