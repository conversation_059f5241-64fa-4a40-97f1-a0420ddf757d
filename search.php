<?php
/**
 * Pagina de procesare a căutării și afișare a rezultatelor
 */

// CRITICAL FIX: Verificăm IMEDIAT dacă este o cerere de export
// TREBUIE să fie PRIMUL lucru pentru a evita contaminarea cu HTML
if (isset($_GET['export']) && isset($_GET['all_results']) && $_GET['all_results'] === '1') {
    // Includere DOAR fișierele necesare pentru export (fără header HTML)
    require_once 'includes/config.php';
    require_once 'includes/functions.php';
    require_once 'src/Services/DosarService.php';

    // Procesăm exportul în funcție de tip și ieșim IMEDIAT
    if ($_GET['export'] === 'xlsx') {
        handleExcelExportOnly();
    } else {
        handleCsvExportOnly(); // Păstrăm compatibilitatea cu exportul TXT existent
    }
    exit; // STOP - nu mai executăm nimic altceva
}

// Includere fișiere necesare pentru afișarea normală a paginii
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

use App\Helpers\SEOHelper;
use App\Helpers\BreadcrumbHelper;

// Generăm breadcrumbs pentru structured data
$searchTerm = isset($_GET['search_term']) ? trim($_GET['search_term']) : '';
$breadcrumbs = [
    ['name' => 'Acasă', 'url' => 'http://localhost/just/'],
    ['name' => 'Căutare', 'url' => 'http://localhost/just/search.php']
];

if (!empty($searchTerm)) {
    $breadcrumbs[] = ['name' => 'Rezultate pentru: ' . htmlspecialchars($searchTerm), 'url' => ''];
}
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <?php
    // SEO Meta Tags optimizate
    echo SEOHelper::renderMetaTags('search', ['search_term' => $searchTerm]);
    echo SEOHelper::renderStructuredData('search', $breadcrumbs);
    echo BreadcrumbHelper::renderBreadcrumbCSS();
    ?>

    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/responsive.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/buttons.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/footer.min.css?v=1.0">

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>

<?php
/**
 * Normalizează numărul de dosar pentru căutarea SOAP API
 * Elimină asterisk-urile și gestionează sufixele pentru compatibilitate cu API-ul
 *
 * @param string $caseNumber Numărul de dosar original
 * @return array Array cu numărul normalizat și informații despre wildcard/suffix
 */
function normalizeCaseNumber($caseNumber) {
    $cleanNumber = trim($caseNumber, '"\'');
    $hasWildcard = false;
    $hasSuffix = false;
    $originalSuffix = '';
    $normalizedNumber = $cleanNumber;

    // Detectează și elimină asterisk wildcard
    if (preg_match('/^(.+)\*$/', $cleanNumber, $matches)) {
        $hasWildcard = true;
        $normalizedNumber = $matches[1];
    }

    // Detectează sufixe suplimentare (ex: /a17, /a1, etc.)
    // FIXED: Doar sufixe care conțin cel puțin o literă (nu doar cifre)
    if (preg_match('/^(\d+\/\d+(?:\/\d+)?)\/([a-zA-Z][a-zA-Z0-9]*|[0-9]*[a-zA-Z][a-zA-Z0-9]*)$/', $normalizedNumber, $matches)) {
        $hasSuffix = true;
        $originalSuffix = $matches[2];
        $normalizedNumber = $matches[1]; // Păstrăm doar partea standard
    }

    // Elimină prefixele (nr., dosar, număr)
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(.+)$/i', $normalizedNumber, $matches)) {
        $normalizedNumber = $matches[1];
    }

    return [
        'normalized' => $normalizedNumber,
        'original' => $cleanNumber,
        'hasWildcard' => $hasWildcard,
        'hasSuffix' => $hasSuffix,
        'suffix' => $originalSuffix
    ];
}

/**
 * Filtrează rezultatele după pattern-ul numărului de dosar (client-side)
 * Suportă wildcard-uri cu asterisk și sufixe suplimentare
 */
function filterResultsByCaseNumberPattern($results, $caseNumberInfo) {
    if (empty($results) || empty($caseNumberInfo)) {
        return $results;
    }

    $filteredResults = [];
    $originalPattern = $caseNumberInfo['original'];
    $hasWildcard = $caseNumberInfo['hasWildcard'];
    $hasSuffix = $caseNumberInfo['hasSuffix'];

    foreach ($results as $dosar) {
        $caseNumber = $dosar->numar ?? '';
        $shouldInclude = false;

        if ($hasWildcard) {
            // Pentru wildcard (ex: "14096/3/2024*"), verificăm dacă numărul dosarului începe cu partea fără asterisk
            $basePattern = str_replace('*', '', $originalPattern);
            if (strpos($caseNumber, $basePattern) === 0) {
                $shouldInclude = true;
            }
        } elseif ($hasSuffix) {
            // Pentru sufixe (ex: "2333/105/2024/a17"), verificăm match exact
            if ($caseNumber === $originalPattern) {
                $shouldInclude = true;
            }
        } else {
            // Pentru cazuri normale, verificăm match exact
            if ($caseNumber === $originalPattern) {
                $shouldInclude = true;
            }
        }

        if ($shouldInclude) {
            $filteredResults[] = $dosar;
        }
    }

    return $filteredResults;
}
?>

/**
 * Găsește partea care se potrivește cu criteriile de căutare
 *
 * @param array $parti Lista părților din dosar
 * @param string $searchTerm Termenul de căutare pentru nume parte
 * @return array|null Partea care se potrivește sau null dacă nu se găsește
 */
function findMatchingParty($parti, $searchTerm) {
    if (empty($parti) || empty($searchTerm)) {
        return null;
    }

    // Normalizăm termenul de căutare (lowercase, fără spații suplimentare)
    $normalizedSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');

    // Căutăm o potrivire exactă sau parțială
    foreach ($parti as $parte) {
        if (empty($parte['nume'])) {
            continue;
        }

        $normalizedParteName = mb_strtolower(trim($parte['nume']), 'UTF-8');

        // Verificăm dacă numele părții conține termenul de căutare
        if (mb_strpos($normalizedParteName, $normalizedSearchTerm, 0, 'UTF-8') !== false) {
            return $parte;
        }
    }

    return null;
}

/**
 * Obține partea relevantă pentru afișare în rezultatele căutării
 *
 * @param array $parti Lista părților din dosar
 * @param string $searchTerm Termenul de căutare pentru nume parte
 * @return array|null Partea pentru afișare
 */
function getRelevantParty($parti, $searchTerm = '') {
    if (empty($parti)) {
        return null;
    }

    // Dacă avem un termen de căutare pentru nume parte, încercăm să găsim o potrivire
    if (!empty($searchTerm)) {
        $matchingParty = findMatchingParty($parti, $searchTerm);
        if ($matchingParty) {
            return $matchingParty;
        }
    }

    // Fallback: returnăm prima parte
    return $parti[0];
}

/**
 * Cache pentru părțile relevante pentru a evita recalcularea
 */
$relevantPartyCache = [];

/**
 * Pre-calculează părțile relevante pentru toate dosarele pentru a optimiza performanța
 *
 * @param array $results Lista dosarelor
 * @param string $searchTerm Termenul de căutare pentru nume parte
 * @return array Array asociativ cu părțile relevante pentru fiecare dosar
 */
function preCalculateRelevantParties($results, $searchTerm = '') {
    global $relevantPartyCache;
    $relevantParties = [];

    foreach ($results as $dosar) {
        $dosarId = $dosar->numar . '_' . $dosar->institutie;

        // Verificăm cache-ul pentru a evita recalcularea
        if (isset($relevantPartyCache[$dosarId])) {
            $relevantParties[$dosarId] = $relevantPartyCache[$dosarId];
            continue;
        }

        $relevantParty = getRelevantParty($dosar->parti ?? [], $searchTerm);
        $relevantParties[$dosarId] = $relevantParty;
        $relevantPartyCache[$dosarId] = $relevantParty;
    }

    return $relevantParties;
}

/**
 * DEDICATED CSV EXPORT FUNCTION - NO HTML CONTAMINATION
 * Această funcție gestionează DOAR exportul CSV fără a include conținut HTML
 */
function handleCsvExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API - FIXED PARAMETER MAPPING
        $searchParams = [
            'numarDosar' => $_GET['numarDosar'] ?? '',
            'numeParte' => $_GET['numeParte'] ?? '',
            'institutie' => $_GET['institutie'] ?? '',
            'dataStart' => $_GET['dataStart'] ?? '',
            'dataStop' => $_GET['dataStop'] ?? '',
            'obiectDosar' => $_GET['obiectDosar'] ?? '',
            'categorieInstanta' => $_GET['categorieInstanta'] ?? '',
            'categorieCaz' => $_GET['categorieCaz'] ?? '',
            'dataUltimaModificareStart' => $_GET['dataUltimaModificareStart'] ?? '',
            'dataUltimaModificareStop' => $_GET['dataUltimaModificareStop'] ?? '',
            '_maxResults' => 10000  // Limită mare pentru toate rezultatele
        ];

        // Inițializăm serviciul de dosare
        $dosarService = new \App\Services\DosarService();

        // Obținem TOATE rezultatele pentru export folosind căutarea avansată
        $allResults = $dosarService->cautareAvansata($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            http_response_code(404);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există rezultate pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Generăm numele fișierului în format TXT
        $filename = 'Rezultate_Cautare_' . date('Y-m-d_H-i-s') . '.txt';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Setăm header-ele pentru TXT CURAT
        header('Content-Type: text/plain; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Content-Transfer-Encoding: 8bit');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('X-Content-Type-Options: nosniff');
        header('Content-Description: File Transfer');

        // Generăm DOAR conținutul TXT
        generateCleanTxtContent($allResults, $searchParams['numeParte'] ?? '');

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea CSV: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * DEDICATED EXCEL EXPORT FUNCTION - TRUE XLSX FORMAT
 * Această funcție gestionează DOAR exportul Excel fără a include conținut HTML
 */
function handleExcelExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API - FIXED PARAMETER MAPPING
        $searchParams = [
            'numarDosar' => $_GET['numarDosar'] ?? '',
            'numeParte' => $_GET['numeParte'] ?? '',
            'institutie' => $_GET['institutie'] ?? '',
            'dataStart' => $_GET['dataStart'] ?? '',
            'dataStop' => $_GET['dataStop'] ?? '',
            'obiectDosar' => $_GET['obiectDosar'] ?? '',
            'categorieInstanta' => $_GET['categorieInstanta'] ?? '',
            'categorieCaz' => $_GET['categorieCaz'] ?? '',
            'dataUltimaModificareStart' => $_GET['dataUltimaModificareStart'] ?? '',
            'dataUltimaModificareStop' => $_GET['dataUltimaModificareStop'] ?? '',
            '_maxResults' => 10000  // Limită mare pentru toate rezultatele
        ];

        // Inițializăm serviciul de dosare
        $dosarService = new \App\Services\DosarService();

        // Obținem TOATE rezultatele pentru export folosind căutarea avansată
        $allResults = $dosarService->cautareAvansata($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            http_response_code(404);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există rezultate pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Generăm numele fișierului în format Excel
        $filename = 'Rezultate_Cautare_' . date('Y-m-d_H-i-s') . '.xlsx';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul Excel
        generateExcelFile($allResults, $filename, $searchParams['numeParte'] ?? '');

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea Excel: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}
?>

<!-- Stiluri pentru loading overlay pentru căutare -->
<style>
/* Loading overlay pentru căutare */
.search-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.9);
    z-index: 9998;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.search-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.search-loading-content {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 90%;
    border-top: 4px solid #007bff;
}

.search-loading-spinner {
    width: 50px;
    height: 50px;
    margin: 0 auto 1rem;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: searchLoadingSpin 1s linear infinite;
}

.search-loading-message {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

.search-loading-submessage {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

@keyframes searchLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design pentru mobile */
@media (max-width: 767.98px) {
    .search-loading-content {
        padding: 1.5rem;
        max-width: 300px;
    }

    .search-loading-spinner {
        width: 40px;
        height: 40px;
    }

    .search-loading-message {
        font-size: 0.9rem;
    }

    .search-loading-submessage {
        font-size: 0.8rem;
    }
}

/* Asigură că conținutul principal este ascuns inițial */
.search-main-content {
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.search-main-content.loaded {
    opacity: 1;
}

/* ===== JUDICIAL NAVIGATION MENU STYLES ===== */

/* Main navigation container */
.judicial-navbar {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.15);
    border-bottom: 3px solid #2c3e50;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.judicial-navbar .container-fluid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Navbar content layout */
.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
    position: relative;
}

/* Brand/Logo section */
.navbar-brand {
    flex-shrink: 0;
}

.brand-link {
    display: flex;
    align-items: center;
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

.brand-link:hover,
.brand-link:focus {
    color: #f8f9fa;
    text-decoration: none;
    transform: translateY(-1px);
}

.brand-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    color: #ffffff;
    transition: transform 0.3s ease;
}

.brand-link:hover .brand-icon {
    transform: scale(1.1);
}

.brand-text {
    font-family: 'Open Sans', sans-serif;
    letter-spacing: 0.5px;
}

/* Main navigation links container */
.navbar-nav {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex: 1;
    justify-content: center;
}

.nav-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Navigation links */
.nav-link {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    min-height: 44px;
    min-width: 44px;
    justify-content: center;
}

.nav-link:hover,
.nav-link:focus {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: #f8f9fa;
    border-radius: 2px;
}

.nav-icon {
    font-size: 1rem;
    margin-right: 0.5rem;
    transition: transform 0.3s ease;
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    font-family: 'Roboto', sans-serif;
    white-space: nowrap;
}

/* External link styling */
.external-link {
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.external-link:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background-color: rgba(255, 255, 255, 0.15);
}

/* Admin navigation section */
.admin-nav {
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    padding-left: 1.5rem;
    margin-left: 1rem;
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.hamburger-line {
    width: 24px;
    height: 3px;
    background-color: #ffffff;
    margin: 2px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile navigation menu */
.mobile-nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.mobile-nav-menu.show {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.mobile-nav-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: 44px;
}

.mobile-nav-link:hover,
.mobile-nav-link:focus {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transform: translateX(4px);
}

.mobile-nav-link.active {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.2);
}

.mobile-nav-link i {
    font-size: 1.1rem;
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.mobile-nav-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 0.5rem 0;
}

/* Responsive Design */
@media (max-width: 992px) {
    .navbar-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .admin-nav {
        border-left: none;
        padding-left: 0;
        margin-left: 0;
    }
}

@media (max-width: 768px) {
    .judicial-navbar .container-fluid {
        padding: 0 0.75rem;
    }

    .navbar-content {
        min-height: 56px;
    }

    .brand-link {
        font-size: 1.1rem;
    }

    .brand-icon {
        font-size: 1.3rem;
        margin-right: 0.5rem;
    }

    .mobile-nav-content {
        padding: 0.75rem;
    }

    .mobile-nav-link {
        font-size: 0.95rem;
        padding: 0.625rem 0.75rem;
    }
}

@media (max-width: 576px) {
    .judicial-navbar .container-fluid {
        padding: 0 0.5rem;
    }

    .brand-text {
        display: none;
    }

    .brand-icon {
        margin-right: 0;
    }

    .mobile-menu-toggle {
        width: 40px;
        height: 40px;
    }

    .hamburger-line {
        width: 20px;
    }
}

/* Focus styles for accessibility */
.nav-link:focus,
.mobile-nav-link:focus,
.brand-link:focus,
.mobile-menu-toggle:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .judicial-navbar {
        background: #000000;
        border-bottom-color: #ffffff;
    }

    .nav-link,
    .mobile-nav-link,
    .brand-link {
        color: #ffffff;
    }

    .nav-link:hover,
    .mobile-nav-link:hover,
    .brand-link:hover {
        background-color: #333333;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .judicial-navbar,
    .nav-link,
    .mobile-nav-link,
    .brand-link,
    .mobile-menu-toggle,
    .hamburger-line,
    .mobile-nav-menu {
        transition: none;
    }

    .brand-link:hover .brand-icon,
    .nav-link:hover .nav-icon {
        transform: none;
    }
}
</style>

<!-- Loading overlay pentru căutare -->
<div id="searchLoadingOverlay" class="search-loading-overlay" role="status" aria-live="polite" aria-label="Se caută dosarele">
    <div class="search-loading-content">
        <div class="search-loading-spinner" aria-hidden="true"></div>
        <p class="search-loading-message">Se caută dosarele...</p>
        <p class="search-loading-submessage">Vă rugăm să așteptați</p>
    </div>
</div>

<!-- Stiluri pentru sortare și responsive design -->
<style>
    /* Variabile CSS pentru scheme de culori și dimensiuni */
    :root {
        /* Culori principale */
        --primary-color: #007bff;
        --secondary-color: #6c757d;
        --success-color: #28a745;
        --info-color: #17a2b8;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --light-color: #f8f9fa;
        --dark-color: #343a40;

        /* Dimensiuni */
        --touch-target-size: 44px;
        --border-radius: 4px;
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 3rem;

        /* Tranziții */
        --transition-speed: 0.3s;

        /* Lățimi optimizate pentru layout-ul tabelului - 10 coloane cu Acțiuni vizibile */
        --container-max-width-desktop: 1400px;
        --container-max-width-tablet: 1200px;
        --table-min-width-desktop: 1200px;
        --table-min-width-tablet: 1100px;
    }

    /* Stiluri de bază responsive */
    body {
        font-size: 16px;
        line-height: 1.5;
    }

    .container {
        width: 100%;
        padding-right: var(--spacing-md);
        padding-left: var(--spacing-md);
    }

    /* Expandarea containerelor pentru tabelul larg */
    .container-fluid {
        max-width: none !important;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Container principal optimizat pentru desktop cu Acțiuni vizibile */
    @media (min-width: 1200px) {
        .container,
        .container-lg,
        .container-md,
        .container-sm,
        .container-xl {
            max-width: var(--container-max-width-desktop) !important;
        }

        .container-fluid {
            padding-left: 1.5rem; /* Redus pentru a maximiza spațiul pentru tabel */
            padding-right: 1.5rem;
        }
    }

    /* Container pentru tablete */
    @media (min-width: 768px) and (max-width: 1199px) {
        .container,
        .container-lg,
        .container-md,
        .container-sm {
            max-width: var(--container-max-width-tablet) !important;
        }
    }

    /* Stiluri pentru card-uri */
    .card {
        border-radius: var(--border-radius);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: box-shadow var(--transition-speed) ease;
    }

    .card:hover {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    /* Stiluri pentru butoane */
    .btn {
        min-height: var(--touch-target-size);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed) ease;
    }

    .btn-sm {
        min-height: 38px;
        min-width: 38px;
        padding: 0.25rem 0.5rem;
    }

    /* ===== JUDICIAL PORTAL TABLE HEADER STYLING ===== */

    /* Base table styling with judicial design */
    .table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        max-width: 100%;
        background-color: #ffffff;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    }

    /* Judicial header styling with blue color scheme - COMPACT VERSION */
    .table thead th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 3px solid #007bff;
        border-top: 1px solid #2c3e50;
        border-left: none;
        border-right: 1px solid #dee2e6;
        color: #2c3e50;
        font-weight: 700;
        font-size: 0.8rem;
        padding: 0.6rem 0.5rem;
        vertical-align: middle;
        position: relative;
        /* COMPACT TEXT WRAPPING for full header visibility with minimal height */
        white-space: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        line-height: 1.1;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        font-family: 'Open Sans', sans-serif;
        height: auto;
        min-height: 2.5rem;
        max-height: 4rem;
    }

    .table thead th:first-child {
        border-left: 1px solid #2c3e50;
        border-top-left-radius: 6px;
    }

    .table thead th:last-child {
        border-right: 1px solid #2c3e50;
        border-top-right-radius: 6px;
    }

    /* Judicial sortable header styling */
    th.sortable {
        cursor: pointer;
        user-select: none;
        transition: all 0.3s ease;
        position: relative;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    /* Enhanced hover effect with judicial colors */
    th.sortable:hover {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: #ffffff;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
    }

    th.sortable:hover a,
    th.sortable:hover .header-link {
        color: #ffffff !important;
    }

    /* Judicial header link styling */
    th.sortable a,
    .header-link {
        color: #2c3e50 !important;
        text-decoration: none !important;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 0;
        font-weight: 700;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    /* Header text styling with judicial typography and COMPACT text wrapping */
    .header-text,
    th.sortable a span:first-child {
        flex: 1;
        text-align: left;
        margin-right: 0.5rem;
        line-height: 1.1;
        font-family: 'Open Sans', sans-serif;
        font-size: 0.8rem;
        /* COMPACT TEXT WRAPPING for full header text visibility with minimal height */
        white-space: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        overflow: visible;
        text-overflow: unset;
        display: block;
        max-height: 3rem;
        overflow: hidden;
    }

    /* Active sort state with judicial blue */
    th.sortable[data-direction] {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: #ffffff;
        box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
        border-bottom: 3px solid #2c3e50;
    }

    th.sortable[data-direction] a,
    th.sortable[data-direction] .header-link {
        color: #ffffff !important;
    }

    /* Enhanced sort icon styling */
    .sort-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        margin-left: 0.75rem;
        flex-shrink: 0;
        opacity: 0.7;
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        padding: 2px;
    }

    th.sortable:hover .sort-icon {
        opacity: 1;
        background-color: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }

    th.sortable[data-direction] .sort-icon {
        opacity: 1;
        color: #ffffff;
        background-color: rgba(255, 255, 255, 0.2);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    /* Non-sortable header styling with judicial theme and COMPACT text wrapping */
    .table thead th:not(.sortable) {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #2c3e50;
        font-weight: 700;
        text-align: center;
        padding: 0.6rem 0.5rem;
        border-right: 1px solid #dee2e6;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        font-family: 'Open Sans', sans-serif;
        font-size: 0.8rem;
        /* COMPACT TEXT WRAPPING for full header text visibility with minimal height */
        white-space: normal;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        line-height: 1.1;
        height: auto;
        min-height: 2.5rem;
        max-height: 4rem;
    }

    /* ===== RESPONSIVE DESIGN ===== */

    /* ===== RESPONSIVE TABLE LAYOUT - NO HORIZONTAL SCROLLING ===== */

    /* Desktop (≥1200px) - COMPACT header design for full visibility without scrolling */
    @media (min-width: 1200px) {
        .table thead th {
            font-size: 0.8rem;
            padding: 0.6rem 0.5rem;
            /* COMPACT TEXT WRAPPING for desktop header visibility */
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.1;
            min-height: 2.5rem;
            max-height: 4rem;
        }

        /* Ensure COMPACT header text wrapping on desktop */
        .header-text,
        th.sortable a span:first-child {
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
            font-size: 0.8rem;
            line-height: 1.1;
            max-height: 3rem;
        }

        /* Fixed table layout with percentage-based column widths */
        #tabelRezultate {
            table-layout: fixed;
            width: 100%;
        }

        #tabelRezultate th:nth-child(1),  /* Număr dosar - OPTIMIZED for full case number visibility */
        #tabelRezultate td:nth-child(1) {
            width: 13%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(2),  /* Instanță - INCREASED for full court names */
        #tabelRezultate td:nth-child(2) {
            width: 15%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(3),  /* Obiect - MAINTAINED as largest for case descriptions */
        #tabelRezultate td:nth-child(3) {
            width: 20%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(4),  /* Stadiu - INCREASED for procedural status terms */
        #tabelRezultate td:nth-child(4) {
            width: 11%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(5),  /* Data - OPTIMIZED for date formats */
        #tabelRezultate td:nth-child(5) {
            width: 8%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(6),  /* Categorie - INCREASED for case category names */
        #tabelRezultate td:nth-child(6) {
            width: 10%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(7),  /* Data mod. - OPTIMIZED for date formats */
        #tabelRezultate td:nth-child(7) {
            width: 8%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(8),  /* Nume - INCREASED for full party names (high importance) */
        #tabelRezultate td:nth-child(8) {
            width: 14%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(9),  /* Calitate - INCREASED for party role descriptions */
        #tabelRezultate td:nth-child(9) {
            width: 9%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(10), /* Acțiuni - MAINTAINED for action buttons */
        #tabelRezultate td:nth-child(10) {
            width: 7%;
            min-width: 0;
            text-align: center;
        }
    }

    /* Tablet (768px-1199px) - COMPACT design for no horizontal scrolling */
    @media (min-width: 768px) and (max-width: 1199px) {
        .table thead th {
            font-size: 0.75rem;
            padding: 0.5rem 0.4rem;
            /* COMPACT TEXT WRAPPING for tablet header visibility */
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.1;
            min-height: 2.2rem;
            max-height: 3.5rem;
        }

        .sort-icon {
            width: 12px;
            height: 12px;
        }

        /* Ensure COMPACT header text wrapping on tablets */
        .header-text,
        th.sortable a span:first-child {
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
            font-size: 0.75rem;
            line-height: 1.1;
            max-height: 2.5rem;
        }

        /* Fixed table layout with adjusted percentages for tablets */
        #tabelRezultate {
            table-layout: fixed;
            width: 100%;
        }

        #tabelRezultate th:nth-child(1),  /* Număr dosar - OPTIMIZED for full case number visibility */
        #tabelRezultate td:nth-child(1) {
            width: 11%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(2),  /* Instanță - INCREASED for full court names */
        #tabelRezultate td:nth-child(2) {
            width: 14%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(3),  /* Obiect - MAINTAINED as largest for case descriptions */
        #tabelRezultate td:nth-child(3) {
            width: 22%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(4),  /* Stadiu - INCREASED for procedural status terms */
        #tabelRezultate td:nth-child(4) {
            width: 11%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(5),  /* Data - OPTIMIZED for date formats */
        #tabelRezultate td:nth-child(5) {
            width: 8%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(6),  /* Categorie - INCREASED for case category names */
        #tabelRezultate td:nth-child(6) {
            width: 9%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(7),  /* Data mod. - OPTIMIZED for date formats */
        #tabelRezultate td:nth-child(7) {
            width: 8%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(8),  /* Nume - INCREASED for full party names (high importance) */
        #tabelRezultate td:nth-child(8) {
            width: 13%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(9),  /* Calitate - INCREASED for party role descriptions */
        #tabelRezultate td:nth-child(9) {
            width: 8%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(10), /* Acțiuni - MAINTAINED for action buttons */
        #tabelRezultate td:nth-child(10) {
            width: 8%;
            min-width: 0;
            text-align: center;
        }
    }

    /* Mobile Large (576px-767px) - ULTRA COMPACT but readable layout */
    @media (min-width: 576px) and (max-width: 767px) {
        .table thead th {
            font-size: 0.7rem;
            padding: 0.4rem 0.3rem;
            /* ULTRA COMPACT TEXT WRAPPING for mobile large header visibility */
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.0;
            min-height: 2rem;
            max-height: 3rem;
        }

        .sort-icon {
            width: 10px;
            height: 10px;
        }

        /* Ultra compact text wrapping on mobile large */
        .header-text,
        th.sortable a span:first-child {
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-size: 0.7rem;
            line-height: 1.0;
            max-height: 2.2rem;
        }

        /* Fixed table layout with compact percentages */
        #tabelRezultate {
            table-layout: fixed;
            width: 100%;
        }

        #tabelRezultate th:nth-child(1),  /* Număr dosar - INCREASED for full visibility */
        #tabelRezultate td:nth-child(1) {
            width: 10%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(2),  /* Instanță */
        #tabelRezultate td:nth-child(2) {
            width: 11%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(3),  /* Obiect */
        #tabelRezultate td:nth-child(3) {
            width: 25%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(4),  /* Stadiu */
        #tabelRezultate td:nth-child(4) {
            width: 9%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(5),  /* Data */
        #tabelRezultate td:nth-child(5) {
            width: 7%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(6),  /* Categorie */
        #tabelRezultate td:nth-child(6) {
            width: 8%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(7),  /* Data mod. */
        #tabelRezultate td:nth-child(7) {
            width: 7%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(8),  /* Nume */
        #tabelRezultate td:nth-child(8) {
            width: 13%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(9),  /* Calitate */
        #tabelRezultate td:nth-child(9) {
            width: 6%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(10), /* Acțiuni */
        #tabelRezultate td:nth-child(10) {
            width: 8%;
            min-width: 0;
            text-align: center;
        }
    }

    /* Mobile Small (≤575px) - MINIMAL compact layout */
    @media (max-width: 575px) {
        .table thead th {
            font-size: 0.65rem;
            padding: 0.3rem 0.2rem;
            /* MINIMAL TEXT WRAPPING for mobile small header visibility */
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.0;
            min-height: 1.8rem;
            max-height: 2.5rem;
        }

        .sort-icon {
            width: 8px;
            height: 8px;
        }

        /* Minimal text wrapping on small mobile */
        .header-text,
        th.sortable a span:first-child {
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
            line-height: 1.0;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-size: 0.65rem;
            max-height: 2rem;
        }

        /* Fixed table layout with ultra compact percentages */
        #tabelRezultate {
            table-layout: fixed;
            width: 100%;
        }

        #tabelRezultate th:nth-child(1),  /* Număr dosar - INCREASED for full visibility */
        #tabelRezultate td:nth-child(1) {
            width: 9%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(2),  /* Instanță */
        #tabelRezultate td:nth-child(2) {
            width: 10%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(3),  /* Obiect */
        #tabelRezultate td:nth-child(3) {
            width: 27%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(4),  /* Stadiu */
        #tabelRezultate td:nth-child(4) {
            width: 8%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(5),  /* Data */
        #tabelRezultate td:nth-child(5) {
            width: 6%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(6),  /* Categorie */
        #tabelRezultate td:nth-child(6) {
            width: 7%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(7),  /* Data mod. */
        #tabelRezultate td:nth-child(7) {
            width: 6%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(8),  /* Nume */
        #tabelRezultate td:nth-child(8) {
            width: 14%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(9),  /* Calitate */
        #tabelRezultate td:nth-child(9) {
            width: 5%;
            min-width: 0;
        }

        #tabelRezultate th:nth-child(10), /* Acțiuni */
        #tabelRezultate td:nth-child(10) {
            width: 6%;
            min-width: 0;
            text-align: center;
        }
    }

    /* ===== TABLE LAYOUT OPTIMIZATION ===== */

    /* Ensure table fits within viewport */
    .table-responsive {
        border-radius: 6px;
        border: 1px solid #dee2e6;
        overflow-x: auto;
        max-width: 100%;
    }

    /* Judicial Portal Table Body Styling */
    .table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-top: 1px solid #e9ecef;
        border-right: 1px solid #f1f3f4;
        word-wrap: break-word;
        hyphens: auto;
        font-family: 'Roboto', sans-serif;
        font-size: 0.9rem;
        line-height: 1.5;
        color: #2c3e50;
        background-color: #ffffff;
        transition: background-color 0.2s ease;
    }

    .table tbody td:first-child {
        border-left: 1px solid #e9ecef;
    }

    .table tbody td:last-child {
        border-right: 1px solid #e9ecef;
    }

    /* Enhanced table row hover effect */
    .table tbody tr:hover td {
        background-color: rgba(0, 123, 255, 0.05);
        border-color: rgba(0, 123, 255, 0.1);
    }

    /* Striped rows with judicial colors */
    .table-striped tbody tr:nth-of-type(odd) td {
        background-color: rgba(248, 249, 250, 0.5);
    }

    .table-striped tbody tr:nth-of-type(odd):hover td {
        background-color: rgba(0, 123, 255, 0.08);
    }

    /* ===== ENHANCED TEXT WRAPPING AND OVERFLOW HANDLING ===== */

    /* Ensure proper text wrapping in all table cells */
    .table td,
    .table th {
        white-space: normal;
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: clip;
    }

    /* Specific handling for different content types */
    .table td:nth-child(1),  /* Număr dosar - keep on one line if possible */
    .table td:nth-child(5),  /* Data - keep dates compact */
    .table td:nth-child(7) { /* Data modificare - keep dates compact */
        word-break: keep-all;
        overflow-wrap: normal;
    }

    .table td:nth-child(3),  /* Obiect - allow full wrapping */
    .table td:nth-child(8),  /* Nume - allow full wrapping */
    .table td:nth-child(2) { /* Instanță - allow wrapping */
        word-break: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    /* Header text wrapping */
    .header-text {
        word-break: keep-all;
        overflow-wrap: normal;
        hyphens: none;
    }

    /* Responsive text adjustments */
    @media (max-width: 767px) {
        .table td,
        .table th {
            line-height: 1.3;
            font-size: 0.85rem;
        }

        .header-text {
            word-break: break-word;
            overflow-wrap: break-word;
            line-height: 1.2;
        }
    }

    @media (max-width: 575px) {
        .table td,
        .table th {
            line-height: 1.2;
            font-size: 0.8rem;
            padding: 0.5rem 0.25rem;
        }

        .header-text {
            font-size: 0.7rem;
            line-height: 1.1;
        }
    }

    /* Table container with judicial styling */
    .table-responsive {
        border-radius: 8px;
        border: 2px solid #2c3e50;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        background-color: #ffffff;
    }

    /* Focus styles for accessibility */
    th.sortable a:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }

    /* Print styles */
    @media print {
        .table thead th {
            background: #f8f9fa !important;
            color: #000000 !important;
            border: 1px solid #000000 !important;
        }

        th.sortable[data-direction] {
            background: #e9ecef !important;
        }
    }

    /* ===== CLEAN HEADER COMPONENTS - EXACT DEMO MATCH ===== */

    /* Header link styling - exact match to demo */
    .header-link {
        color: #495057 !important;
        text-decoration: none !important;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 0;
        font-weight: 600;
        font-size: 0.875rem;
    }

    /* Header text styling - exact match to demo */
    .header-text {
        flex: 1;
        text-align: left;
        margin-right: 0.5rem;
        line-height: 1.3;
    }

    /* Ensure consistent styling for all header links */
    th.sortable a {
        color: #495057 !important;
    }

    th.sortable[data-direction] a {
        color: #ffffff !important;
    }

    /* Focus styles for accessibility - exact match to demo */
    th.sortable a:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }

    /* Stiluri pentru tabelul de rezultate - Layout expandabil */
    .table-responsive {
        margin-bottom: var(--spacing-lg);
        width: 100%;
        /* Comportament diferit pe baza dimensiunii ecranului */
    }

    /* ===== TABLE CONTAINER RESPONSIVE BEHAVIOR ===== */

    /* Remove horizontal scrolling for all screen sizes */
    .table-responsive {
        overflow-x: hidden; /* No horizontal scrolling needed */
        overflow-y: visible;
        width: 100%;
        max-width: 100%;
    }

    /* Ensure table container fits viewport */
    .table-container {
        width: 100%;
        max-width: 100vw;
        overflow: hidden;
    }

    /* Stiluri pentru conținut complet afișat */
    .full-content {
        /* Stiluri pentru conținut care nu mai necesită tooltip-uri */
        word-break: break-word;
        overflow-wrap: break-word;
    }

    /* Judicial Portal Justice Icon Button Styling */
    .justice-icon-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px;
        border-radius: 8px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: 2px solid #2c3e50;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 3px 6px rgba(0, 123, 255, 0.25);
        color: white;
        text-decoration: none;
        font-family: 'Open Sans', sans-serif;
    }

    .justice-icon-btn:hover {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        border-color: #007bff;
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(44, 62, 80, 0.3);
        color: #f8f9fa;
        text-decoration: none;
    }

    .justice-icon-btn:active {
        transform: translateY(-1px);
        box-shadow: 0 3px 6px rgba(0, 123, 255, 0.25);
    }

    .justice-icon-btn:focus {
        outline: 3px solid rgba(0, 123, 255, 0.5);
        outline-offset: 2px;
    }

    /* Iconița SVG animată */
    .justice-icon {
        width: 24px;
        height: 24px;
        fill: white;
        transition: transform 0.3s ease;
    }

    .justice-icon-btn:hover .justice-icon {
        transform: scale(1.1);
    }

    /* Animația de balansare pentru ciocan */
    @keyframes gavel-swing {
        0%, 100% { transform: rotate(0deg); }
        25% { transform: rotate(-5deg); }
        75% { transform: rotate(5deg); }
    }

    .justice-icon-btn:hover .gavel-head {
        animation: gavel-swing 0.6s ease-in-out;
    }

    /* Tooltip pentru accesibilitate */
    .justice-icon-btn[data-tooltip]:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 6px 10px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        margin-bottom: 5px;
        pointer-events: none;
    }

    .justice-icon-btn[data-tooltip]:hover::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.9);
        z-index: 1001;
        margin-bottom: 1px;
        pointer-events: none;
    }

    /* Judicial Portal Responsive Design for Table Navigation */

    /* Large devices (desktops, 1200px and up) */
    @media (min-width: 1200px) {
        .justice-icon-btn {
            width: 48px;
            height: 48px;
        }

        .excel-export-btn {
            padding: 0.75rem 1.5rem;
            font-size: 0.95rem;
        }

        .page-link {
            padding: 0.875rem 1.25rem;
            font-size: 0.95rem;
        }
    }

    /* Medium devices (tablets, 768px-1199px) */
    @media (min-width: 768px) and (max-width: 1199px) {
        .justice-icon-btn {
            width: 42px;
            height: 42px;
        }

        .excel-export-btn {
            padding: 0.625rem 1.25rem;
            font-size: 0.9rem;
        }

        .excel-export-btn .export-count {
            font-size: 0.7em;
        }

        .page-link {
            padding: 0.625rem 1rem;
            font-size: 0.9rem;
            min-width: 40px;
            min-height: 40px;
        }

        .pagination {
            gap: 0.375rem;
            padding: 0.75rem;
        }
    }

    /* Small devices (landscape phones, 576px-767px) */
    @media (min-width: 576px) and (max-width: 767px) {
        .justice-icon-btn {
            width: 40px;
            height: 40px;
        }

        .excel-export-btn {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }

        .excel-export-btn .export-count {
            display: none; /* Hide count on smaller screens */
        }

        .page-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.85rem;
            min-width: 36px;
            min-height: 36px;
        }

        .pagination {
            gap: 0.25rem;
            padding: 0.5rem;
        }
    }

    /* Extra small devices (portrait phones, less than 576px) */
    @media (max-width: 575px) {
        .justice-icon-btn {
            width: 36px;
            height: 36px;
            border-width: 1px;
        }

        .justice-icon {
            width: 18px;
            height: 18px;
        }

        .excel-export-btn {
            width: 100%;
            padding: 0.75rem;
            font-size: 0.8rem;
            margin-bottom: 1rem;
        }

        .excel-export-btn .export-count {
            display: none;
        }

        .page-link {
            padding: 0.5rem;
            font-size: 0.8rem;
            min-width: 32px;
            min-height: 32px;
        }

        .pagination {
            gap: 0.125rem;
            padding: 0.5rem;
            margin: 1rem 0;
        }

        /* Compact pagination for mobile */
        .pagination .page-item:not(.active):not(:first-child):not(:last-child):not(.disabled) {
            display: none;
        }

        .pagination .page-item.active,
        .pagination .page-item:first-child,
        .pagination .page-item:last-child {
            display: inline-flex;
        }
    }

    /* ===== ACCESSIBILITY AND FOCUS STATES ===== */

    /* Enhanced focus styles for table navigation elements */
    .table thead th.sortable:focus,
    .table thead th.sortable a:focus {
        outline: 3px solid rgba(0, 123, 255, 0.6);
        outline-offset: 2px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: #ffffff;
        box-shadow: 0 0 0 2px #ffffff, 0 0 0 5px rgba(0, 123, 255, 0.3);
    }

    /* Keyboard navigation support */
    .table thead th.sortable[tabindex]:focus {
        position: relative;
        z-index: 10;
    }

    /* High contrast mode support for table navigation */
    @media (prefers-contrast: high) {
        .table thead th {
            background: #000000;
            color: #ffffff;
            border-color: #ffffff;
        }

        .table thead th.sortable:hover {
            background: #333333;
        }

        .table thead th.sortable[data-direction] {
            background: #ffffff;
            color: #000000;
        }

        .justice-icon-btn,
        .excel-export-btn {
            background: #000000;
            color: #ffffff;
            border-color: #ffffff;
        }

        .page-link {
            background: #000000;
            color: #ffffff;
            border-color: #ffffff;
        }

        .page-item.active .page-link {
            background: #ffffff;
            color: #000000;
        }
    }

    /* Reduced motion support for table navigation */
    @media (prefers-reduced-motion: reduce) {
        .table thead th.sortable,
        .justice-icon-btn,
        .excel-export-btn,
        .page-link,
        .sort-icon {
            transition: none;
        }

        .table thead th.sortable:hover,
        .justice-icon-btn:hover,
        .excel-export-btn:hover {
            transform: none;
        }

        .sort-icon:hover {
            transform: none;
        }
    }

    /* Print styles for table navigation */
    @media print {
        .excel-export-btn,
        .pagination,
        .justice-icon-btn {
            display: none !important;
        }

        .table thead th {
            background: #f8f9fa !important;
            color: #000000 !important;
            border: 1px solid #000000 !important;
        }

        .table tbody td {
            border: 1px solid #000000 !important;
            background: #ffffff !important;
            color: #000000 !important;
        }
    }

    /* Loading states for table navigation elements */
    .table-loading .table thead th.sortable {
        pointer-events: none;
        opacity: 0.6;
    }

    .table-loading .excel-export-btn,
    .table-loading .pagination .page-link {
        pointer-events: none;
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Enhanced visual feedback for interactive elements */
    .table thead th.sortable:active {
        transform: translateY(1px);
        box-shadow: 0 1px 3px rgba(0, 123, 255, 0.3);
    }

    .justice-icon-btn:active,
    .excel-export-btn:active {
        transform: translateY(0);
    }

    .page-link:active {
        transform: translateY(1px);
        box-shadow: 0 1px 3px rgba(0, 123, 255, 0.3);
    }

    /* ===== STREAMLINED LAYOUT STYLES ===== */

    /* Streamlined card without header */
    .streamlined-card {
        border-radius: 8px;
        border: 2px solid #2c3e50;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        background-color: #ffffff;
        margin-top: 2rem;
    }

    /* Search summary now uses standard Bootstrap card styling */

    /* Responsive styles removed - using standard Bootstrap card */

    /* Ultra-streamlined layout styles removed - using standard Bootstrap card */

    /* Ultra-streamlined responsive styles removed */

    /* Ultra-streamlined visual hierarchy and print styles removed */

    /* Fallback pentru browsere mai vechi sau în caz de eroare la încărcarea SVG */
    .justice-icon-btn .fallback-icon {
        display: none;
        font-size: 16px;
        color: white;
    }

    .justice-icon-btn:not(.svg-loaded) .justice-icon {
        display: none;
    }

    .justice-icon-btn:not(.svg-loaded) .fallback-icon {
        display: inline-block;
    }

    /* Enhanced icon styling for meticulous inspection */
    .justice-icon {
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }

    .justice-icon-btn:hover .justice-icon {
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    /* Optimizare pentru performanță - preload pentru animații */
    .justice-icon-btn {
        will-change: transform;
    }

    .justice-icon {
        will-change: transform;
    }

    /* Judicial Portal Excel Export Button Styling */
    .excel-export-btn {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: 2px solid #2c3e50;
        border-radius: 6px;
        color: #ffffff;
        font-weight: 600;
        font-family: 'Open Sans', sans-serif;
        transition: all 0.3s ease;
        box-shadow: 0 3px 6px rgba(0, 123, 255, 0.25);
        white-space: nowrap;
        padding: 0.5rem 1rem;
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .excel-export-btn:hover {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        border-color: #007bff;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(44, 62, 80, 0.3);
        color: #f8f9fa;
    }

    .excel-export-btn:active {
        transform: translateY(0);
        box-shadow: 0 3px 6px rgba(0, 123, 255, 0.25);
    }

    .excel-export-btn:focus {
        outline: 3px solid rgba(0, 123, 255, 0.5);
        outline-offset: 2px;
    }

    .excel-export-btn:disabled {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        border-color: #495057;
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        opacity: 0.6;
    }

    .excel-export-btn .export-count {
        font-size: 0.75em;
        opacity: 0.9;
        margin-left: 0.25rem;
        font-weight: 500;
        text-transform: none;
        letter-spacing: normal;
    }

    .excel-export-btn i {
        margin-right: 0.5rem;
        font-size: 1.1em;
    }

    /* Loading overlay pentru export */
    .export-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .export-loading-content {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        max-width: 400px;
        width: 90%;
    }

    .export-loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #28a745;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .table th, .table td {
        vertical-align: middle;
        padding: 0.75rem;
    }







    /* Stiluri pentru celulele clickabile */
    .clickable-cell {
        cursor: pointer;
        position: relative;
        transition: background-color 0.2s ease;
    }

    .clickable-cell:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }

    .clickable-cell::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 8px 8px 0;
        border-color: transparent rgba(0, 123, 255, 0.3) transparent transparent;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .clickable-cell:hover::after {
        opacity: 1;
    }

    /* Stiluri pentru linkul numărului de dosar */
    .case-number-link {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
        display: inline-block;
        padding: 2px 4px;
        border-radius: 3px;
    }

    .case-number-link:hover {
        color: #0056b3;
        text-decoration: none;
        background-color: rgba(0, 123, 255, 0.1);
        transform: translateY(-1px);
    }

    .case-number-link:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
        background-color: rgba(0, 123, 255, 0.1);
    }

    .case-number-link:active {
        transform: translateY(0);
        background-color: rgba(0, 123, 255, 0.2);
    }

    /* Stiluri pentru secțiunea de sumar căutare - folosim stilurile standard Bootstrap card */

    /* Tooltip pentru celulele clickabile */
    .search-tooltip {
        background-color: rgba(0, 123, 255, 0.1);
        border-left: 3px solid rgba(0, 123, 255, 0.7);
        padding: 8px 12px;
        margin-bottom: 10px;
        border-radius: 4px;
        font-size: 0.9rem;
        color: #495057;
        transition: opacity 0.5s ease;
        opacity: 1;
    }

    /* Judicial Portal Pagination Styling */
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
        margin: 2rem 0;
        padding: 1rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 6px rgba(0, 123, 255, 0.1);
    }

    .page-link {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
        border-radius: 6px;
        border: 2px solid #dee2e6;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        color: #2c3e50;
        text-decoration: none;
        font-weight: 600;
        font-family: 'Open Sans', sans-serif;
        transition: all 0.3s ease;
        margin: 0 0.25rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .page-link:hover {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border-color: #2c3e50;
        color: #ffffff;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .page-link:focus {
        outline: 3px solid rgba(0, 123, 255, 0.5);
        outline-offset: 2px;
        z-index: 2;
    }

    .page-item.active .page-link {
        background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
        border-color: #007bff;
        color: #ffffff;
        box-shadow: 0 3px 6px rgba(44, 62, 80, 0.3);
        transform: translateY(-1px);
    }

    .page-item.disabled .page-link {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        border-color: #ced4da;
        color: #6c757d;
        cursor: not-allowed;
        opacity: 0.6;
        transform: none;
        box-shadow: none;
    }

    .page-item.disabled .page-link:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        border-color: #ced4da;
        color: #6c757d;
        transform: none;
        box-shadow: none;
    }

    /* Card view pentru rezultate pe mobile */
    .card-view {
        display: none;
    }

    /* Media Queries pentru Responsive Design */

    /* Extra small devices (portrait phones, less than 576px) */
    @media (max-width: 575.98px) {
        .card-header {
            padding: var(--spacing-sm) var(--spacing-md);
        }

        .card-body {
            padding: var(--spacing-md);
        }

        .btn {
            width: 100%;
            margin-bottom: var(--spacing-sm);
        }

        .card-header .btn {
            margin-top: var(--spacing-sm);
        }

        /* Ascunde tabelul și afișează card view pe ecrane foarte mici */
        .table-container {
            display: none;
        }

        .card-view {
            display: block;
        }

        .result-card {
            border: 1px solid rgba(0,0,0,.125);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            background-color: #fff;
        }

        .result-card-header {
            font-weight: bold;
            margin-bottom: var(--spacing-sm);
            font-size: 1.1rem;
        }

        .result-card-body {
            margin-bottom: var(--spacing-sm);
        }

        .result-card-item {
            display: flex;
            margin-bottom: var(--spacing-xs);
        }

        .result-card-label {
            font-weight: 600;
            min-width: 140px; /* Mărit pentru a acomoda noile etichete mai lungi */
        }

        /* Search summary folosește stilurile standard Bootstrap card */

        .result-card-actions {
            margin-top: var(--spacing-sm);
            text-align: right;
        }

        .result-card-actions .btn {
            width: auto;
        }

        /* Optimizare paginație pentru mobile */
        .pagination .page-item:not(.active):not(:first-child):not(:last-child) {
            display: none;
        }

        .pagination .page-item.active {
            display: inline-flex;
        }
    }

    /* Small devices (landscape phones, 576px and up) */
    @media (min-width: 576px) and (max-width: 767.98px) {
        .card-header h2 {
            font-size: 1.5rem;
        }

        .card-header .btn {
            margin-top: 0;
        }

        /* Optimizare paginație pentru tablete mici */
        .pagination .page-item:not(.active):not(:first-child):not(:last-child):not(.disabled) {
            display: none;
        }

        .pagination .page-item.active,
        .pagination .page-item:first-child,
        .pagination .page-item:last-child {
            display: inline-flex;
        }
    }

    /* Large devices (desktops, 992px and up) */
    @media (min-width: 992px) {
        .card-body {
            padding: var(--spacing-xl);
        }
    }

    /* Compact Results Information Styles */
    .results-info-compact {
        padding: 0.5rem 0.75rem;
        background-color: rgba(108, 117, 125, 0.05);
        border-left: 2px solid rgba(108, 117, 125, 0.2);
        border-radius: 0 3px 3px 0;
        margin: 0.75rem 0;
    }

    .results-info-compact .text-muted {
        color: #6c757d !important;
        font-size: 0.8rem;
        line-height: 1.4;
        margin: 0;
    }

    .results-info-compact i {
        font-size: 0.75rem;
        opacity: 0.6;
    }

    /* Mobile optimization for compact info */
    @media (max-width: 767.98px) {
        .results-info-compact {
            padding: 0.375rem 0.5rem;
            margin: 0.5rem 0;
        }

        .results-info-compact .text-muted {
            font-size: 0.75rem;
        }

        .results-info-compact i {
            font-size: 0.7rem;
        }

        /* Mobile-specific styles for case number links */
        .case-number-link {
            font-size: 0.9rem;
            padding: 4px 6px;
            min-height: 44px; /* Ensure touch-friendly target */
            display: inline-flex;
            align-items: center;
        }

        .result-card-header .case-number-link {
            font-size: 1rem;
            font-weight: 600;
        }

        /* Enhanced mobile styles for details button in card actions */
        .result-card-actions {
            text-align: center; /* Center the button */
            padding: 0.75rem 1rem; /* Add more padding around the button area */
        }

        .result-card-actions .justice-icon-btn {
            width: 80%; /* Take up more horizontal space */
            max-width: 280px; /* Reasonable maximum width */
            min-width: 200px; /* Minimum width for consistency */
            height: 48px; /* Ensure touch-friendly height */
            padding: 0.75rem 1.5rem; /* Generous padding for better touch target */
            border-radius: 8px; /* Slightly more rounded for modern look */
            font-size: 0.95rem; /* Slightly larger font */
            font-weight: 600; /* Make text more prominent */
            display: flex; /* Use flexbox for better alignment */
            align-items: center;
            justify-content: center;
            gap: 0.5rem; /* Space between icon and text if text is added */
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); /* Enhanced gradient */
            border: 2px solid #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25); /* More prominent shadow */
            transition: all 0.3s ease;
        }

        .result-card-actions .justice-icon-btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            border-color: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.35);
        }

        .result-card-actions .justice-icon-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
        }

        .result-card-actions .justice-icon-btn .justice-icon {
            width: 20px; /* Slightly larger icon */
            height: 20px;
            color: #ffffff;
        }

        /* Add a subtle text label for better UX on mobile */
        .result-card-actions .justice-icon-btn::after {
            content: "Vezi detalii";
            font-size: 0.85rem;
            font-weight: 600;
            color: #ffffff;
            margin-left: 0.5rem;
        }

        /* Override smaller screen styles for result-card-actions specifically */
        .result-card-actions .justice-icon-btn {
            width: 80% !important; /* Override the 36px width from smaller screens */
            height: 48px !important; /* Override the 36px height from smaller screens */
            min-width: 180px !important; /* Ensure minimum width even on very small screens */
        }

        /* Adjust for very small screens (less than 400px) */
        @media (max-width: 399px) {
            .result-card-actions .justice-icon-btn {
                width: 90% !important;
                min-width: 160px !important;
                font-size: 0.9rem;
                padding: 0.65rem 1rem;
            }

            .result-card-actions .justice-icon-btn::after {
                font-size: 0.8rem;
            }
        }
    }
</style>

<?php

// Inițializare variabile
$results = [];
$error = null;
$searchParams = [];
$totalResults = 0;
$currentPage = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$resultsPerPage = RESULTS_PER_PAGE;

// Creăm un director pentru loguri dacă nu există
$logDir = __DIR__ . '/logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Setăm timeout-uri pentru performanță
set_time_limit(60); // Limităm execuția la 60 secunde
ini_set('memory_limit', '256M'); // Limităm memoria la 256MB

// Inițializăm cronometrarea pentru monitorizarea performanței
$performanceStart = microtime(true);
$performanceLog = [];

/**
 * Funcție pentru logarea performanței
 */
function logPerformance($operation, $startTime = null) {
    global $performanceLog, $performanceStart;

    $currentTime = microtime(true);
    $totalTime = $currentTime - $performanceStart;
    $operationTime = $startTime ? ($currentTime - $startTime) : 0;

    $performanceLog[] = [
        'operation' => $operation,
        'time' => $operationTime,
        'total_time' => $totalTime,
        'memory' => memory_get_usage(true)
    ];

    // Logăm în fișier pentru depanare
    $logFile = __DIR__ . '/logs/performance.log';
    $logData = date('Y-m-d H:i:s') . " - {$operation}: " .
               number_format($operationTime, 4) . "s (Total: " .
               number_format($totalTime, 4) . "s, Memory: " .
               number_format(memory_get_usage(true) / 1024 / 1024, 2) . "MB)\n";
    file_put_contents($logFile, $logData, FILE_APPEND);
}

/**
 * Aplică sortarea server-side pe rezultatele căutării
 * @param array $results Rezultatele de sortat
 * @param string $sortBy Coloana după care se sortează
 * @param string $sortDirection Direcția de sortare (asc/desc)
 * @param string $numeParte Numele părții pentru calcularea părților relevante
 * @return array Rezultatele sortate
 */
function applySorting($results, $sortBy, $sortDirection, $numeParte = '') {
    if (empty($results) || !is_array($results)) {
        return $results;
    }

    // Pre-calculăm părțile relevante pentru sortarea după nume/calitate
    $relevantParties = [];
    if (in_array($sortBy, ['nume', 'calitate'])) {
        $relevantParties = preCalculateRelevantParties($results, $numeParte);
    }

    // Obținem lista instanțelor pentru sortarea după instanță
    $institutii = [];
    if ($sortBy === 'instanta') {
        require_once 'includes/functions.php';
        $institutii = getInstanteList();
    }

    // Sortăm rezultatele
    usort($results, function($a, $b) use ($sortBy, $sortDirection, $relevantParties, $institutii) {
        $valueA = getSortValue($a, $sortBy, $relevantParties, $institutii);
        $valueB = getSortValue($b, $sortBy, $relevantParties, $institutii);

        // Comparăm valorile
        $comparison = compareSortValues($valueA, $valueB, $sortBy);

        // Aplicăm direcția de sortare
        return $sortDirection === 'desc' ? -$comparison : $comparison;
    });

    return $results;
}

/**
 * Obține valoarea pentru sortare dintr-un rezultat
 * @param object $dosar Dosarul din care se extrage valoarea
 * @param string $sortBy Coloana pentru care se extrage valoarea
 * @param array $relevantParties Părțile relevante pre-calculate
 * @param array $institutii Lista instanțelor
 * @return string Valoarea pentru sortare
 */
function getSortValue($dosar, $sortBy, $relevantParties, $institutii) {
    switch ($sortBy) {
        case 'numar':
            return $dosar->numar ?? '';

        case 'instanta':
            $institutie = $dosar->institutie ?? '';
            return $institutii[$institutie] ?? $institutie;

        case 'obiect':
            return $dosar->obiect ?? '';

        case 'stadiuProcesual':
            return $dosar->stadiuProcesualNume ?? '';

        case 'data':
            return $dosar->data ?? '';

        case 'categorieCaz':
            return $dosar->categorieCazNume ?? '';

        case 'dataModificare':
            return $dosar->dataModificare ?? '';

        case 'nume':
            $dosarId = $dosar->numar . '_' . $dosar->institutie;
            $relevantParty = $relevantParties[$dosarId] ?? null;
            return $relevantParty ? $relevantParty['nume'] : '';

        case 'calitate':
            $dosarId = $dosar->numar . '_' . $dosar->institutie;
            $relevantParty = $relevantParties[$dosarId] ?? null;
            return $relevantParty ? $relevantParty['calitate'] : '';

        default:
            return '';
    }
}

/**
 * Compară două valori pentru sortare
 * @param string $a Prima valoare
 * @param string $b A doua valoare
 * @param string $sortBy Tipul de sortare pentru tratare specială
 * @return int Rezultatul comparației (-1, 0, 1)
 */
function compareSortValues($a, $b, $sortBy) {
    // Tratăm valorile goale - le punem la sfârșit
    if (empty($a) && !empty($b)) return 1;
    if (!empty($a) && empty($b)) return -1;
    if (empty($a) && empty($b)) return 0;

    // Tratare specială pentru date
    if (in_array($sortBy, ['data', 'dataModificare'])) {
        return compareDates($a, $b);
    }

    // Comparație standard pentru text
    return strcasecmp($a, $b);
}

/**
 * Compară două date pentru sortare
 * @param string $dateA Prima dată
 * @param string $dateB A doua dată
 * @return int Rezultatul comparației
 */
function compareDates($dateA, $dateB) {
    try {
        // Încercăm să parsăm datele
        $timestampA = strtotime($dateA);
        $timestampB = strtotime($dateB);

        if ($timestampA === false && $timestampB === false) {
            return 0;
        }
        if ($timestampA === false) return 1;
        if ($timestampB === false) return -1;

        return $timestampA - $timestampB;
    } catch (Exception $e) {
        // Fallback la comparația de string
        return strcasecmp($dateA, $dateB);
    }
}

/**
 * Construiește URL pentru sortare cu parametri curați
 * @param array $baseParams Parametrii de bază de căutare
 * @param string $sortColumn Coloana pentru sortare
 * @param string $currentSortBy Sortarea curentă
 * @param string $currentSortDirection Direcția curentă de sortare
 * @return string URL pentru sortare
 */
function buildSortUrl($baseParams, $sortColumn, $currentSortBy, $currentSortDirection) {
    // Determinăm noua direcție de sortare
    $newDirection = ($currentSortBy === $sortColumn && $currentSortDirection === 'asc') ? 'desc' : 'asc';

    // Construim parametrii pentru noul URL
    $sortParams = array_merge($baseParams, [
        'sortBy' => $sortColumn,
        'sortDirection' => $newDirection,
        'page' => 1 // Resetăm la prima pagină când schimbăm sortarea
    ]);

    // Eliminăm parametrii goi pentru URL-uri mai curate
    $sortParams = array_filter($sortParams, function($value) {
        return $value !== '' && $value !== null;
    });

    return '?' . http_build_query($sortParams);
}

// Logăm parametrii brut din URL pentru depanare
$logFile = $logDir . '/search_debug.log';
$logData = date('Y-m-d H:i:s') . " - URL parametri brut: " . json_encode($_GET, JSON_UNESCAPED_UNICODE) . "\n";
file_put_contents($logFile, $logData, FILE_APPEND);

// Procesare parametri de căutare - ENHANCED WITH DATE VALIDATION
$numarDosar = isset($_GET['numarDosar']) ? trim($_GET['numarDosar']) : '';
$numeParte = isset($_GET['numeParte']) ? trim($_GET['numeParte']) : '';
$obiectDosar = isset($_GET['obiectDosar']) ? trim($_GET['obiectDosar']) : '';
$institutie = isset($_GET['institutie']) ? trim($_GET['institutie']) : '';
$categorieInstanta = isset($_GET['categorieInstanta']) ? trim($_GET['categorieInstanta']) : '';
$categorieCaz = isset($_GET['categorieCaz']) ? trim($_GET['categorieCaz']) : '';

// ENHANCED: Normalizăm numărul de dosar pentru compatibilitate SOAP API
$caseNumberInfo = null;
if (!empty($numarDosar)) {
    $caseNumberInfo = normalizeCaseNumber($numarDosar);
    $numarDosar = $caseNumberInfo['normalized']; // Folosim numărul normalizat pentru SOAP API
}
$dataStart = isset($_GET['dataStart']) ? trim($_GET['dataStart']) : '';
$dataStop = isset($_GET['dataStop']) ? trim($_GET['dataStop']) : '';
$dataUltimaModificareStart = isset($_GET['dataUltimaModificareStart']) ? trim($_GET['dataUltimaModificareStart']) : '';
$dataUltimaModificareStop = isset($_GET['dataUltimaModificareStop']) ? trim($_GET['dataUltimaModificareStop']) : '';

// Procesare specială pentru instituție - convertim string gol la null pentru API
if ($institutie === '') {
    $institutie = null;
}

// ENHANCED DATE VALIDATION - Validăm formatele de dată românești
$dateValidationErrors = [];

function validateRomanianDate($dateString, $fieldName) {
    if (empty($dateString)) {
        return null; // Date goale sunt permise
    }

    // Verificăm formatul românesc ZZ.LL.AAAA
    if (!preg_match('/^\d{1,2}\.\d{1,2}\.\d{4}$/', $dateString)) {
        return "Formatul datei pentru '{$fieldName}' trebuie să fie ZZ.LL.AAAA (ex: 12.06.2023)";
    }

    // Parsăm componentele datei pentru validare detaliată
    $parts = explode('.', $dateString);
    $day = (int)$parts[0];
    $month = (int)$parts[1];
    $year = (int)$parts[2];

    // Verificăm limitele de bază
    if ($month < 1 || $month > 12) {
        return "Luna '{$month}' pentru '{$fieldName}' nu este validă (trebuie să fie între 1 și 12)";
    }

    if ($day < 1 || $day > 31) {
        return "Ziua '{$day}' pentru '{$fieldName}' nu este validă (trebuie să fie între 1 și 31)";
    }

    if ($year < 1900 || $year > 2100) {
        return "Anul '{$year}' pentru '{$fieldName}' nu este valid (trebuie să fie între 1900 și 2100)";
    }

    // Verificăm dacă data este validă folosind checkdate pentru validarea precisă a zilelor în lună
    // Această funcție gestionează automat anii bisecți și numărul corect de zile pentru fiecare lună
    if (!checkdate($month, $day, $year)) {
        // Oferim mesaje specifice pentru erori comune
        if ($month == 2 && $day > 28) {
            $isLeapYear = ($year % 4 == 0 && $year % 100 != 0) || ($year % 400 == 0);
            if ($isLeapYear && $day == 29) {
                // Anul este bisect și ziua 29 februarie este validă
                // Această verificare nu ar trebui să ajungă aici, dar o păstrăm pentru siguranță
            } else {
                $maxDays = $isLeapYear ? 29 : 28;
                return "Februarie {$year} are doar {$maxDays} de zile. Data '{$dateString}' pentru '{$fieldName}' nu este validă";
            }
        } elseif (in_array($month, [4, 6, 9, 11]) && $day > 30) {
            $monthNames = [4 => 'aprilie', 6 => 'iunie', 9 => 'septembrie', 11 => 'noiembrie'];
            return "Luna {$monthNames[$month]} are doar 30 de zile. Data '{$dateString}' pentru '{$fieldName}' nu este validă";
        } else {
            return "Data '{$dateString}' pentru '{$fieldName}' nu este validă";
        }
    }

    // Verificăm dacă data nu este prea departe în viitor
    $timestamp = mktime(0, 0, 0, $month, $day, $year);
    $maxFutureTime = strtotime('+5 years'); // Permitem până la 5 ani în viitor

    if ($timestamp > $maxFutureTime) {
        return "Data '{$dateString}' pentru '{$fieldName}' este prea departe în viitor (maximum 5 ani de la data curentă)";
    }

    // Verificăm dacă data nu este prea departe în trecut (pentru a evita date irelevante)
    $minPastTime = strtotime('1990-01-01'); // Permitem date de la 1990
    if ($timestamp < $minPastTime) {
        return "Data '{$dateString}' pentru '{$fieldName}' este prea departe în trecut (minimum 1 ianuarie 1990)";
    }

    return null; // Data este validă
}

// Validăm toate câmpurile de dată
if (!empty($dataStart)) {
    $error = validateRomanianDate($dataStart, 'Data început');
    if ($error) $dateValidationErrors[] = $error;
}

if (!empty($dataStop)) {
    $error = validateRomanianDate($dataStop, 'Data sfârșit');
    if ($error) $dateValidationErrors[] = $error;
}

if (!empty($dataUltimaModificareStart)) {
    $error = validateRomanianDate($dataUltimaModificareStart, 'Data modificării început');
    if ($error) $dateValidationErrors[] = $error;
}

if (!empty($dataUltimaModificareStop)) {
    $error = validateRomanianDate($dataUltimaModificareStop, 'Data modificării sfârșit');
    if ($error) $dateValidationErrors[] = $error;
}

// Validăm intervalele de date
if (!empty($dataStart) && !empty($dataStop)) {
    $startTimestamp = strtotime($dataStart);
    $stopTimestamp = strtotime($dataStop);

    if ($startTimestamp && $stopTimestamp && $startTimestamp > $stopTimestamp) {
        $dateValidationErrors[] = "Data de început ({$dataStart}) nu poate fi după data de sfârșit ({$dataStop})";
    }
}

if (!empty($dataUltimaModificareStart) && !empty($dataUltimaModificareStop)) {
    $startTimestamp = strtotime($dataUltimaModificareStart);
    $stopTimestamp = strtotime($dataUltimaModificareStop);

    if ($startTimestamp && $stopTimestamp && $startTimestamp > $stopTimestamp) {
        $dateValidationErrors[] = "Data modificării început ({$dataUltimaModificareStart}) nu poate fi după data modificării sfârșit ({$dataUltimaModificareStop})";
    }
}

// Procesare parametri de sortare
$sortBy = isset($_GET['sortBy']) ? trim($_GET['sortBy']) : '';
$sortDirection = isset($_GET['sortDirection']) ? trim($_GET['sortDirection']) : 'asc';

// Validare parametri de sortare
$validSortColumns = ['numar', 'instanta', 'obiect', 'stadiuProcesual', 'data', 'categorieCaz', 'dataModificare', 'nume', 'calitate'];
if (!in_array($sortBy, $validSortColumns)) {
    $sortBy = ''; // Reset la valoare implicită dacă coloana nu este validă
}

if (!in_array($sortDirection, ['asc', 'desc'])) {
    $sortDirection = 'asc'; // Reset la valoare implicită dacă direcția nu este validă
}

// Logăm parametrii procesați pentru depanare - ENHANCED LOGGING
$logData = date('Y-m-d H:i:s') . " - Parametri procesați: numeParte=" . $numeParte .
          ", numarDosar=" . $numarDosar .
          ", obiectDosar=" . $obiectDosar .
          ", institutie=" . ($institutie ?? 'null') .
          ", categorieInstanta=" . $categorieInstanta .
          ", categorieCaz=" . $categorieCaz .
          ", dataStart=" . $dataStart .
          ", dataStop=" . $dataStop .
          ", sortBy=" . $sortBy .
          ", sortDirection=" . $sortDirection . "\n";
file_put_contents($logFile, $logData, FILE_APPEND);

// Salvare parametri pentru paginare și sortare - FIXED: Păstrăm null pentru institutie
$searchParams = [
    'numarDosar' => $numarDosar,
    'numeParte' => $numeParte,
    'obiectDosar' => $obiectDosar,
    'institutie' => $institutie, // Păstrăm null dacă este null - important pentru SOAP API
    'categorieInstanta' => $categorieInstanta,
    'categorieCaz' => $categorieCaz,
    'dataStart' => $dataStart,
    'dataStop' => $dataStop,
    'dataUltimaModificareStart' => $dataUltimaModificareStart,
    'dataUltimaModificareStop' => $dataUltimaModificareStop,
    'sortBy' => $sortBy,
    'sortDirection' => $sortDirection
];

// Eliminăm doar valorile goale (string-uri vide), dar păstrăm null pentru institutie
$searchParams = array_filter($searchParams, function($value, $key) {
    // Pentru institutie, păstrăm și null (necesar pentru SOAP API)
    if ($key === 'institutie') {
        return true; // Păstrăm orice valoare pentru institutie
    }
    // Pentru restul, eliminăm doar string-urile goale
    return $value !== '' && $value !== null;
}, ARRAY_FILTER_USE_BOTH);

// Verificăm dacă avem erori de validare a datelor
if (!empty($dateValidationErrors)) {
    $logData = date('Y-m-d H:i:s') . " - Erori de validare date: " . implode('; ', $dateValidationErrors) . "\n";
    file_put_contents($logFile, $logData, FILE_APPEND);

    // Afișăm erorile de validare
    echo '<div class="container mt-4">';
    echo '<div class="alert alert-danger" role="alert">';
    echo '<h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Erori de validare</h4>';
    echo '<ul class="mb-0">';
    foreach ($dateValidationErrors as $error) {
        echo '<li>' . htmlspecialchars($error) . '</li>';
    }
    echo '</ul>';
    echo '<hr>';
    echo '<p class="mb-0">Vă rugăm să corectați erorile de mai sus și să încercați din nou.</p>';
    echo '</div>';
    echo '</div>';

    require_once 'includes/footer.php';
    exit;
}

// Verificare dacă s-a trimis cel puțin un criteriu de căutare - ENHANCED WITH ALL CRITERIA
// Includem și filtrele avansate în verificare
$hasSearchCriteria = !empty($numarDosar) || !empty($numeParte) || !empty($obiectDosar) ||
                    !empty($institutie) || !empty($categorieInstanta) || !empty($categorieCaz) ||
                    !empty($dataStart) || !empty($dataStop) ||
                    !empty($dataUltimaModificareStart) || !empty($dataUltimaModificareStop);

/**
 * Verifică dacă există criterii de căutare semnificative pentru afișare
 * @param array $searchParams Parametrii de căutare folosiți
 * @return bool True dacă există criterii semnificative, false altfel
 */
function hasMeaningfulSearchCriteria($searchParams) {
    // Verificăm dacă avem cel puțin un criteriu principal de căutare - ENHANCED WITH ALL CRITERIA
    $hasMainCriteria = !empty($searchParams['numarDosar']) ||
                       !empty($searchParams['numeParte']) ||
                       !empty($searchParams['obiectDosar']) ||
                       !empty($searchParams['institutie']) ||
                       !empty($searchParams['categorieInstanta']) ||
                       !empty($searchParams['categorieCaz']) ||
                       !empty($searchParams['dataStart']) ||
                       !empty($searchParams['dataStop']) ||
                       !empty($searchParams['dataUltimaModificareStart']) ||
                       !empty($searchParams['dataUltimaModificareStop']);

    return $hasMainCriteria;
}

/**
 * Extrage termenii principali de căutare pentru afișare în header
 * @param array $searchParams Parametrii de căutare folosiți
 * @return string Termenii principali de căutare
 */
function getPrimarySearchTerms($searchParams) {
    $primaryTerms = [];

    // Prioritatea termenilor: număr dosar > nume parte > obiect dosar > instanță
    if (!empty($searchParams['numarDosar'])) {
        $primaryTerms[] = htmlspecialchars($searchParams['numarDosar']);
    }

    if (!empty($searchParams['numeParte'])) {
        $primaryTerms[] = htmlspecialchars($searchParams['numeParte']);
    }

    if (!empty($searchParams['obiectDosar'])) {
        $primaryTerms[] = htmlspecialchars($searchParams['obiectDosar']);
    }

    // Pentru instanță, obținem numele complet
    if (!empty($searchParams['institutie']) && empty($primaryTerms)) {
        if (!isset($institutii)) {
            require_once 'includes/functions.php';
            $institutii = getInstanteList();
        }
        $numeInstanta = $institutii[$searchParams['institutie']] ?? $searchParams['institutie'];
        $primaryTerms[] = htmlspecialchars($numeInstanta);
    }

    // Returnăm primul termen (cel mai important) sau combinația primelor două
    if (count($primaryTerms) === 1) {
        return $primaryTerms[0];
    } elseif (count($primaryTerms) >= 2) {
        // Dacă avem mai mulți termeni, afișăm primii doi separați prin " • "
        return $primaryTerms[0] . ' • ' . $primaryTerms[1];
    }

    // Fallback pentru alte criterii
    if (!empty($searchParams['categorieInstanta'])) {
        return htmlspecialchars($searchParams['categorieInstanta']);
    }

    if (!empty($searchParams['dataStart']) || !empty($searchParams['dataStop'])) {
        if (!empty($searchParams['dataStart']) && !empty($searchParams['dataStop'])) {
            return htmlspecialchars($searchParams['dataStart']) . ' - ' . htmlspecialchars($searchParams['dataStop']);
        } elseif (!empty($searchParams['dataStart'])) {
            return 'din ' . htmlspecialchars($searchParams['dataStart']);
        } elseif (!empty($searchParams['dataStop'])) {
            return 'până în ' . htmlspecialchars($searchParams['dataStop']);
        }
    }

    return 'Căutare avansată';
}

/**
 * Generează un sumar al criteriilor de căutare folosite
 * @param array $searchParams Parametrii de căutare folosiți
 * @return string HTML cu sumarul criteriilor de căutare
 */
function generateSearchSummary($searchParams) {
    $criteria = [];

    // Identificăm termenii principali care sunt deja afișați în header
    $primaryTermsInHeader = [];
    if (!empty($searchParams['numarDosar'])) {
        $primaryTermsInHeader[] = 'numarDosar';
    }
    if (!empty($searchParams['numeParte'])) {
        $primaryTermsInHeader[] = 'numeParte';
    }
    if (!empty($searchParams['obiectDosar'])) {
        $primaryTermsInHeader[] = 'obiectDosar';
    }

    // Afișăm doar termenii care NU sunt deja în header (evităm duplicarea)
    // Verifică numărul dosarului - doar dacă nu este primul termen principal
    if (!empty($searchParams['numarDosar']) && !in_array('numarDosar', array_slice($primaryTermsInHeader, 0, 2))) {
        $criteria[] = '<strong>Număr dosar:</strong> ' . htmlspecialchars($searchParams['numarDosar']);
    }

    // Verifică numele părții - doar dacă nu este în primii doi termeni principali
    if (!empty($searchParams['numeParte']) && !in_array('numeParte', array_slice($primaryTermsInHeader, 0, 2))) {
        $criteria[] = '<strong>Nume parte:</strong> ' . htmlspecialchars($searchParams['numeParte']);
    }

    // Verifică obiectul dosarului - doar dacă nu este în primii doi termeni principali
    if (!empty($searchParams['obiectDosar']) && !in_array('obiectDosar', array_slice($primaryTermsInHeader, 0, 2))) {
        $criteria[] = '<strong>Obiect dosar:</strong> ' . htmlspecialchars($searchParams['obiectDosar']);
    }

    // Verifică instanța - doar dacă nu este afișată în header ca termen principal unic
    if (!empty($searchParams['institutie'])) {
        // Instanța apare în header doar când nu avem alți termeni principali
        $showInstitutionInHeader = empty($primaryTermsInHeader);

        if (!$showInstitutionInHeader) {
            // Obținem numele instanței pentru afișare
            if (!isset($institutii)) {
                require_once 'includes/functions.php';
                $institutii = getInstanteList();
            }
            $numeInstanta = $institutii[$searchParams['institutie']] ?? $searchParams['institutie'];
            $criteria[] = '<strong>Instanță:</strong> ' . htmlspecialchars($numeInstanta);
        }
    }

    // Verifică categoria instanței
    if (!empty($searchParams['categorieInstanta'])) {
        $criteria[] = '<strong>Categorie instanță:</strong> ' . htmlspecialchars($searchParams['categorieInstanta']);
    }

    // Verifică categoria cazului - NEW ADDITION
    if (!empty($searchParams['categorieCaz'])) {
        $criteria[] = '<strong>Categorie caz:</strong> ' . htmlspecialchars($searchParams['categorieCaz']);
    }

    // Verifică intervalul de date
    if (!empty($searchParams['dataStart']) || !empty($searchParams['dataStop'])) {
        $dateRange = '';
        if (!empty($searchParams['dataStart']) && !empty($searchParams['dataStop'])) {
            $dateRange = htmlspecialchars($searchParams['dataStart']) . ' - ' . htmlspecialchars($searchParams['dataStop']);
        } elseif (!empty($searchParams['dataStart'])) {
            $dateRange = 'de la ' . htmlspecialchars($searchParams['dataStart']);
        } elseif (!empty($searchParams['dataStop'])) {
            $dateRange = 'până la ' . htmlspecialchars($searchParams['dataStop']);
        }
        if ($dateRange) {
            $criteria[] = '<strong>Perioada:</strong> ' . $dateRange;
        }
    }

    // Verifică intervalul pentru data ultimei modificări
    if (!empty($searchParams['dataUltimaModificareStart']) || !empty($searchParams['dataUltimaModificareStop'])) {
        $modDateRange = '';
        if (!empty($searchParams['dataUltimaModificareStart']) && !empty($searchParams['dataUltimaModificareStop'])) {
            $modDateRange = htmlspecialchars($searchParams['dataUltimaModificareStart']) . ' - ' . htmlspecialchars($searchParams['dataUltimaModificareStop']);
        } elseif (!empty($searchParams['dataUltimaModificareStart'])) {
            $modDateRange = 'de la ' . htmlspecialchars($searchParams['dataUltimaModificareStart']);
        } elseif (!empty($searchParams['dataUltimaModificareStop'])) {
            $modDateRange = 'până la ' . htmlspecialchars($searchParams['dataUltimaModificareStop']);
        }
        if ($modDateRange) {
            $criteria[] = '<strong>Ultima modificare:</strong> ' . $modDateRange;
        }
    }

    // Adăugăm informații despre sortare dacă este activă
    if (!empty($searchParams['sortBy'])) {
        $sortLabels = [
            'numar' => 'Număr dosar',
            'instanta' => 'Instanță',
            'obiect' => 'Obiect',
            'stadiuProcesual' => 'Stadiu procesual',
            'data' => 'Data',
            'categorieCaz' => 'Categorie caz',
            'dataModificare' => 'Data ultimei modificări',
            'nume' => 'Nume',
            'calitate' => 'Calitate'
        ];

        $sortLabel = $sortLabels[$searchParams['sortBy']] ?? $searchParams['sortBy'];
        $sortDirection = $searchParams['sortDirection'] ?? 'asc';
        $directionLabel = $sortDirection === 'asc' ? 'crescător' : 'descrescător';

        $criteria[] = '<strong><i class="fas fa-sort mr-1"></i>Sortare:</strong> ' . $sortLabel . ' (' . $directionLabel . ')';
    }

    // Dacă nu avem criterii, returnăm null pentru a indica că nu trebuie afișată secțiunea
    if (empty($criteria)) {
        return null;
    }

    return implode(' • ', $criteria);
}

if ($hasSearchCriteria) {
    try {
        // Monitorizăm inițializarea serviciului
        $initStart = microtime(true);

        // Inițializare serviciu pentru căutare
        $dosarService = new \App\Services\DosarService();

        logPerformance('Service initialization', $initStart);

        // Logăm parametrii de căutare înainte de a face căutarea
        $logData = date('Y-m-d H:i:s') . " - Parametri trimiși către cautareAvansata: " . json_encode($searchParams, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // Adăugăm limitare pentru rezultate pentru a preveni timeout-urile
        $searchParams['_maxResults'] = 500; // Limităm la 500 de rezultate pentru performanță optimă

        // Monitorizăm căutarea
        $searchStart = microtime(true);

        // Căutare avansată cu toți parametrii
        $allResults = $dosarService->cautareAvansata($searchParams);

        logPerformance('SOAP API call', $searchStart);

        // Logăm rezultatele căutării
        $logData = date('Y-m-d H:i:s') . " - Rezultate primite: " . count($allResults) . " dosare\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // ENHANCED: Aplicăm filtrarea client-side pentru wildcard-uri și sufixe de numere dosar
        if (!empty($allResults) && $caseNumberInfo && ($caseNumberInfo['hasWildcard'] || $caseNumberInfo['hasSuffix'])) {
            $logData = date('Y-m-d H:i:s') . " - Aplicăm filtrarea client-side pentru pattern: '{$caseNumberInfo['original']}'\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            $originalCount = count($allResults);
            $allResults = filterResultsByCaseNumberPattern($allResults, $caseNumberInfo);
            $filteredCount = count($allResults);

            $logData = date('Y-m-d H:i:s') . " - Filtrarea pattern a redus rezultatele de la $originalCount la $filteredCount\n";
            file_put_contents($logFile, $logData, FILE_APPEND);
        }

        // Aplicăm sortarea server-side dacă este specificată
        if (!empty($sortBy) && !empty($allResults)) {
            $sortStart = microtime(true);
            $allResults = applySorting($allResults, $sortBy, $sortDirection, $numeParte);
            logPerformance('Server-side sorting', $sortStart);

            $logData = date('Y-m-d H:i:s') . " - Rezultate sortate după: {$sortBy} ({$sortDirection})\n";
            file_put_contents($logFile, $logData, FILE_APPEND);
        }

        // Dacă nu avem rezultate, logăm mai multe detalii pentru depanare
        if (empty($allResults)) {
            // Identificăm criteriile de căutare active pentru logging
            $activeCriteria = [];
            if (!empty($numarDosar)) $activeCriteria[] = "numarDosar='{$numarDosar}'";
            if (!empty($numeParte)) $activeCriteria[] = "numeParte='{$numeParte}'";
            if (!empty($obiectDosar)) $activeCriteria[] = "obiectDosar='{$obiectDosar}'";
            if (!empty($institutie)) $activeCriteria[] = "institutie='{$institutie}'";
            if (!empty($categorieInstanta)) $activeCriteria[] = "categorieInstanta='{$categorieInstanta}'";
            if (!empty($categorieCaz)) $activeCriteria[] = "categorieCaz='{$categorieCaz}'";
            if (!empty($dataStart)) $activeCriteria[] = "dataStart='{$dataStart}'";
            if (!empty($dataStop)) $activeCriteria[] = "dataStop='{$dataStop}'";

            $criteriaString = implode(', ', $activeCriteria);
            $logData = date('Y-m-d H:i:s') . " - ATENȚIE: Nu s-au găsit rezultate pentru căutarea cu criteriile: {$criteriaString}\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            // Încercăm o căutare simplificată pentru a testa conectivitatea SOAP
            if (!empty($dataStart) && !empty($dataStop)) {
                // Test cu doar intervalul de date
                $simplifiedParams = [
                    'numarDosar' => '',
                    'numeParte' => '',
                    'obiectDosar' => '',
                    'institutie' => null,
                    'dataStart' => $dataStart,
                    'dataStop' => $dataStop,
                    'dataUltimaModificareStart' => '',
                    'dataUltimaModificareStop' => '',
                    '_maxResults' => 10
                ];

                $logData = date('Y-m-d H:i:s') . " - Încercare căutare simplificată doar cu intervalul de date: {$dataStart} - {$dataStop}\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                try {
                    $testResults = $dosarService->cautareAvansata($simplifiedParams);
                    $logData = date('Y-m-d H:i:s') . " - Rezultate căutare simplificată cu date: " . count($testResults) . " dosare\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                } catch (Exception $e) {
                    $logData = date('Y-m-d H:i:s') . " - Eroare la căutarea simplificată cu date: " . $e->getMessage() . "\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                }
            } elseif (!empty($numeParte)) {
                // Test cu doar numele părții
                $simplifiedParams = [
                    'numarDosar' => '',
                    'numeParte' => $numeParte,
                    'obiectDosar' => '',
                    'institutie' => null,
                    'dataStart' => '',
                    'dataStop' => '',
                    'dataUltimaModificareStart' => '',
                    'dataUltimaModificareStop' => '',
                    '_maxResults' => 10
                ];

                $logData = date('Y-m-d H:i:s') . " - Încercare căutare simplificată doar cu numeParte='{$numeParte}'\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                try {
                    $testResults = $dosarService->cautareAvansata($simplifiedParams);
                    $logData = date('Y-m-d H:i:s') . " - Rezultate căutare simplificată cu nume: " . count($testResults) . " dosare\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                } catch (Exception $e) {
                    $logData = date('Y-m-d H:i:s') . " - Eroare la căutarea simplificată cu nume: " . $e->getMessage() . "\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                }
            }
        }

        // Calculare număr total de rezultate și paginare
        $totalResults = count($allResults);
        $totalPages = ceil($totalResults / $resultsPerPage);

        // Ajustare pagină curentă dacă este în afara limitelor
        if ($currentPage < 1) {
            $currentPage = 1;
        } elseif ($currentPage > $totalPages && $totalPages > 0) {
            $currentPage = $totalPages;
        }

        // Extragere rezultate pentru pagina curentă
        $startIndex = ($currentPage - 1) * $resultsPerPage;
        $results = array_slice($allResults, $startIndex, $resultsPerPage);

        // Pre-calculăm părțile relevante pentru toate rezultatele pentru a optimiza performanța
        $processingStart = microtime(true);
        $relevantParties = preCalculateRelevantParties($results, $numeParte);
        logPerformance('Result processing and party calculation', $processingStart);

    } catch (Exception $e) {
        $error = "Eroare la căutare: " . $e->getMessage();

        // Logăm eroarea pentru depanare
        $logData = date('Y-m-d H:i:s') . " - EROARE: " . $e->getMessage() . "\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // Logăm și performanța în caz de eroare
        logPerformance('Search failed with error');
    }
}

// Logăm performanța finală
logPerformance('Total page execution');

// OLD CONTAMINATED EXPORT FUNCTION REMOVED
// Clean export functions are implemented above

// OLD generateExcelFileContent FUNCTION REMOVED - was causing HTML contamination
// Clean CSV generation is now handled by generateCleanCsvContent() function

/**
 * Curăță textul pentru export CSV - UTF-8 ENCODING PRESERVED
 */
function cleanForExcel($text) {
    if (empty($text)) return '';

    // CRITICAL FIX: Păstrăm encoding-ul UTF-8 original fără conversii
    $text = (string) $text;

    // Enhanced encoding detection and handling for Romanian characters
    if (!mb_check_encoding($text, 'UTF-8')) {
        // Define encoding list prioritizing common Romanian encodings
        $encodingList = [
            'UTF-8',
            'ISO-8859-2',    // Latin-2 (Central European) - common for Romanian
            'Windows-1250',  // Windows Central European - common for Romanian
            'ISO-8859-1',    // Latin-1 (Western European)
            'Windows-1252',  // Windows Western European
            'ASCII'
        ];

        // Try to detect encoding with specific list
        $detectedEncoding = mb_detect_encoding($text, $encodingList, true);

        if ($detectedEncoding !== false) {
            // Successfully detected encoding, convert to UTF-8
            $text = mb_convert_encoding($text, 'UTF-8', $detectedEncoding);
        } else {
            // Fallback: assume Windows-1250 and suppress warnings
            $originalErrorReporting = error_reporting();
            error_reporting($originalErrorReporting & ~E_WARNING);

            try {
                $text = mb_convert_encoding($text, 'UTF-8', 'Windows-1250');
            } catch (Exception $e) {
                error_log("Romanian Portal CSV: Character encoding conversion failed for text: " . substr($text, 0, 50));
            }

            error_reporting($originalErrorReporting);
        }
    }

    // Eliminăm DOAR caracterele problematice pentru CSV, păstrând UTF-8
    $text = str_replace(["\r\n", "\r", "\n", "\t"], ' ', $text);  // Newlines și tabs
    $text = str_replace(['"'], '""', $text);                       // Escapăm ghilimelele
    $text = preg_replace('/\s+/u', ' ', $text);                    // Normalizăm spațiile (cu flag UTF-8)
    $text = trim($text);                                           // Eliminăm spațiile de la capete

    // Eliminăm HTML tags păstrând encoding-ul UTF-8
    $text = strip_tags($text);

    // Limităm lungimea folosind funcții UTF-8 aware
    if (mb_strlen($text, 'UTF-8') > 1000) {
        $text = mb_substr($text, 0, 997, 'UTF-8') . '...';
    }

    return $text;
}

/**
 * Formatează datele pentru CSV/Excel - STRICT DATE FORMATTING
 */
function formatDateForExcel($date) {
    if (empty($date)) return '';

    // Convertim la string și curățăm
    $date = trim((string) $date);
    if (empty($date)) return '';

    // Încercăm să formatăm data într-un format standard recunoscut de Excel
    try {
        // Încercăm mai multe formate de dată comune
        $formats = ['Y-m-d', 'Y-m-d H:i:s', 'd.m.Y', 'd/m/Y', 'd-m-Y'];

        foreach ($formats as $format) {
            $dateObj = DateTime::createFromFormat($format, $date);
            if ($dateObj !== false) {
                // Returnăm în format european standard pentru Excel
                return $dateObj->format('d.m.Y');
            }
        }

        // Dacă formatele specifice nu funcționează, încercăm parsing general
        $dateObj = new DateTime($date);
        return $dateObj->format('d.m.Y');

    } catch (Exception $e) {
        // Dacă data nu poate fi parsată, o curățăm ca text normal
        error_log("CSV Export: Data invalidă '{$date}': " . $e->getMessage());
        return cleanForExcel($date);
    }
}

/**
 * Obține numele instanței pentru export - UTF-8 ENCODING PRESERVED
 */
function getInstanteName($institutie) {
    if (empty($institutie)) return '';

    // CRITICAL FIX: Păstrăm encoding-ul UTF-8 original
    $institutie = trim((string) $institutie);
    if (empty($institutie)) return '';

    try {
        require_once 'includes/functions.php';
        $institutii = getInstanteList();

        // Returnăm numele complet sau codul original dacă nu găsim maparea
        $nume = $institutii[$institutie] ?? $institutie;

        // IMPORTANT: Nu aplicăm cleanForExcel() aici pentru a evita double-processing
        // cleanForExcel() va fi aplicat în generateExcelFileContent()
        return $nume;

    } catch (Exception $e) {
        error_log("CSV Export: Eroare la obținerea numelui instanței '{$institutie}': " . $e->getMessage());
        return $institutie;
    }
}

/**
 * Construiește URL-ul pentru detaliile dosarului - STRICT URL BUILDING
 */
function buildDetailUrl($numar, $institutie) {
    if (empty($numar) || empty($institutie)) return '';

    // Curățăm și validăm parametrii
    $numar = trim((string) $numar);
    $institutie = trim((string) $institutie);

    if (empty($numar) || empty($institutie)) return '';

    try {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = dirname($_SERVER['REQUEST_URI'] ?? '');

        // Construim URL-ul cu encoding proper
        $url = $protocol . '://' . $host . $path . '/detalii_dosar.php?numar=' . urlencode($numar) . '&institutie=' . urlencode($institutie);

        return $url;

    } catch (Exception $e) {
        error_log("CSV Export: Eroare la construirea URL-ului pentru dosarul '{$numar}': " . $e->getMessage());
        return '';
    }
}

/**
 * Validează și curăță URL-ul pentru export CSV
 */
function validateAndCleanUrl($url) {
    if (empty($url)) return '';

    // Curățăm URL-ul
    $url = trim((string) $url);
    if (empty($url)) return '';

    // Validăm că este un URL valid
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        error_log("CSV Export: URL invalid: '{$url}'");
        return '';
    }

    // Limităm lungimea URL-ului pentru CSV
    if (strlen($url) > 500) {
        error_log("CSV Export: URL prea lung: " . strlen($url) . " caractere");
        return substr($url, 0, 497) . '...';
    }

    return $url;
}

/**
 * Testează și validează encoding-ul UTF-8 pentru export
 */
function validateUtf8Encoding() {
    // Test cu caractere românești pentru validarea encoding-ului
    $testStrings = [
        'Număr dosar',
        'Instanță',
        'Data înregistrării',
        'Data ultimei modificări',
        'Nume parte relevantă',
        'Judecătoria București',
        'pretenții',
        'Pârât'
    ];

    foreach ($testStrings as $test) {
        if (!mb_check_encoding($test, 'UTF-8')) {
            error_log("CSV Export: String-ul '{$test}' nu este UTF-8 valid");
            return false;
        }
    }

    error_log("CSV Export: Validarea UTF-8 a trecut cu succes");
    return true;
}

/**
 * Generează DOAR conținut TXT curat - ZERO HTML contamination
 * Format simplu text pentru compatibilitate maximă
 */
function generateCleanTxtContent($results, $numeParte = '') {
    // Validare input
    if (!is_array($results) || empty($results)) {
        error_log("TXT Export: Nu există rezultate valide pentru export");
        return;
    }

    // Scriem direct în output fără BOM pentru format TXT simplu
    echo "REZULTATE CĂUTARE DOSARE JUDICIARE\n";
    echo "===================================\n";
    echo "Data export: " . date('d.m.Y H:i:s') . "\n";
    echo "Total rezultate: " . count($results) . "\n\n";

    // Pre-calculăm părțile relevante
    $relevantParties = preCalculateRelevantParties($results, $numeParte);

    $counter = 1;

    // Procesăm fiecare rezultat
    foreach ($results as $dosar) {
        if (!is_object($dosar) || empty($dosar->numar)) {
            continue; // Sărim dosarele invalide
        }

        $dosarId = $dosar->numar . '_' . $dosar->institutie;
        $relevantParty = $relevantParties[$dosarId] ?? null;

        // Afișăm datele într-un format text structurat
        echo "DOSAR #{$counter}\n";
        echo "----------------\n";
        echo "Număr dosar: " . cleanForTxt($dosar->numar ?? '') . "\n";
        echo "Instanță: " . cleanForTxt(getInstanteName($dosar->institutie ?? '')) . "\n";
        echo "Obiect: " . cleanForTxt($dosar->obiect ?? '') . "\n";
        echo "Stadiu procesual: " . cleanForTxt($dosar->stadiuProcesual ?? '') . "\n";
        echo "Data înregistrării: " . formatDateForTxt($dosar->dataInregistrarii ?? $dosar->data ?? '') . "\n";
        echo "Categorie caz: " . cleanForTxt($dosar->categorieCaz ?? '') . "\n";
        echo "Data ultimei modificări: " . formatDateForTxt($dosar->dataUltimaModificare ?? '') . "\n";
        echo "Nume parte relevantă: " . cleanForTxt($relevantParty ? $relevantParty['nume'] : '') . "\n";
        echo "Calitate parte: " . cleanForTxt($relevantParty ? $relevantParty['calitate'] : '') . "\n";
        echo "Link detalii: " . buildCleanDetailUrl($dosar->numar, $dosar->institutie) . "\n";
        echo "\n";

        $counter++;
    }

    echo "===================================\n";
    echo "Sfârșit export - Total: " . ($counter - 1) . " dosare\n";
}

/**
 * Curăță textul pentru format TXT - versiune simplă și curată
 */
function cleanForTxt($text) {
    if (empty($text)) return '-';

    $text = (string) $text;

    // Enhanced encoding detection and handling for Romanian characters
    if (!mb_check_encoding($text, 'UTF-8')) {
        // Define encoding list prioritizing common Romanian encodings
        $encodingList = [
            'UTF-8',
            'ISO-8859-2',    // Latin-2 (Central European) - common for Romanian
            'Windows-1250',  // Windows Central European - common for Romanian
            'ISO-8859-1',    // Latin-1 (Western European)
            'Windows-1252',  // Windows Western European
            'ASCII'
        ];

        // Try to detect encoding with specific list
        $detectedEncoding = mb_detect_encoding($text, $encodingList, true);

        if ($detectedEncoding !== false) {
            // Successfully detected encoding, convert to UTF-8
            $text = mb_convert_encoding($text, 'UTF-8', $detectedEncoding);
        } else {
            // Fallback: assume Windows-1250 and suppress warnings
            $originalErrorReporting = error_reporting();
            error_reporting($originalErrorReporting & ~E_WARNING);

            try {
                $text = mb_convert_encoding($text, 'UTF-8', 'Windows-1250');
            } catch (Exception $e) {
                error_log("Romanian Portal TXT: Character encoding conversion failed for text: " . substr($text, 0, 50));
            }

            error_reporting($originalErrorReporting);
        }
    }

    // Curățăm caracterele problematice pentru TXT
    $text = str_replace(["\r\n", "\r", "\n", "\t"], ' ', $text);
    $text = strip_tags($text); // Eliminăm orice HTML
    $text = preg_replace('/\s+/u', ' ', $text);
    $text = trim($text);

    // Limităm lungimea pentru lizibilitate
    if (mb_strlen($text, 'UTF-8') > 500) {
        $text = mb_substr($text, 0, 497, 'UTF-8') . '...';
    }

    return $text ?: '-';
}

/**
 * Formatează datele pentru format TXT
 */
function formatDateForTxt($date) {
    if (empty($date)) return '-';

    $date = trim((string) $date);
    if (empty($date)) return '-';

    try {
        $formats = ['Y-m-d', 'Y-m-d H:i:s', 'd.m.Y', 'd/m/Y', 'd-m-Y'];

        foreach ($formats as $format) {
            $dateObj = DateTime::createFromFormat($format, $date);
            if ($dateObj !== false) {
                return $dateObj->format('d.m.Y');
            }
        }

        $dateObj = new DateTime($date);
        return $dateObj->format('d.m.Y');

    } catch (Exception $e) {
        return cleanForTxt($date);
    }
}

/**
 * Construiește URL curat pentru detalii
 */
function buildCleanDetailUrl($numar, $institutie) {
    if (empty($numar) || empty($institutie)) return '';

    $numar = trim((string) $numar);
    $institutie = trim((string) $institutie);

    if (empty($numar) || empty($institutie)) return '';

    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $path = dirname($_SERVER['REQUEST_URI'] ?? '');

    return $protocol . '://' . $host . $path . '/detalii_dosar.php?numar=' . urlencode($numar) . '&institutie=' . urlencode($institutie);
}

/**
 * Generează fișier Excel (.xlsx) cu rezultatele căutării
 */
function generateExcelFile($results, $filename, $numeParte = '') {
    // Încărcăm PhpSpreadsheet
    require_once __DIR__ . '/vendor/autoload.php';

    // Validare input
    if (!is_array($results) || empty($results)) {
        throw new Exception('Nu există rezultate valide pentru export Excel');
    }

    // Creăm un nou spreadsheet
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Rezultate Căutare');

    // Definim header-ele coloanelor (exact ca în tabelul de pe pagină)
    $headers = [
        'A' => 'Număr dosar',
        'B' => 'Instanță',
        'C' => 'Obiect',
        'D' => 'Stadiu procesual',
        'E' => 'Data',
        'F' => 'Categorie caz',
        'G' => 'Data ultimei modificări',
        'H' => 'Nume',
        'I' => 'Calitate'
    ];

    // Setăm header-ele în prima linie
    foreach ($headers as $column => $header) {
        $sheet->setCellValue($column . '1', $header);
    }

    // Aplicăm formatare pentru header
    $headerRange = 'A1:I1';
    $sheet->getStyle($headerRange)->getFont()->setBold(true);
    $sheet->getStyle($headerRange)->getFill()
        ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
        ->getStartColor()->setRGB('007bff');
    $sheet->getStyle($headerRange)->getFont()->getColor()->setRGB('FFFFFF');
    $sheet->getStyle($headerRange)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

    // Pre-calculăm părțile relevante
    $relevantParties = preCalculateRelevantParties($results, $numeParte);

    // Obținem lista instanțelor pentru mapare
    $institutii = getInstanteList();

    // Populăm datele
    $row = 2; // Începem de la linia 2 (după header)
    foreach ($results as $dosar) {
        if (!is_object($dosar) || empty($dosar->numar)) {
            continue; // Sărim dosarele invalide
        }

        $dosarId = $dosar->numar . '_' . $dosar->institutie;
        $relevantParty = $relevantParties[$dosarId] ?? null;

        // Populăm coloanele
        $sheet->setCellValue('A' . $row, cleanForExcel($dosar->numar ?? ''));
        $sheet->setCellValue('B' . $row, cleanForExcel($institutii[$dosar->institutie] ?? $dosar->institutie));
        $sheet->setCellValue('C' . $row, cleanForExcel($dosar->obiect ?? ''));
        $sheet->setCellValue('D' . $row, cleanForExcel($dosar->stadiuProcesualNume ?? ''));
        $sheet->setCellValue('E' . $row, formatDateForExcel($dosar->data ?? ''));
        $sheet->setCellValue('F' . $row, cleanForExcel($dosar->categorieCazNume ?? ''));
        $sheet->setCellValue('G' . $row, formatDateForExcel($dosar->dataModificare ?? ''));
        $sheet->setCellValue('H' . $row, cleanForExcel($relevantParty ? $relevantParty['nume'] : ''));
        $sheet->setCellValue('I' . $row, cleanForExcel($relevantParty ? $relevantParty['calitate'] : ''));

        $row++;
    }

    // Auto-dimensionăm coloanele
    foreach (range('A', 'I') as $column) {
        $sheet->getColumnDimension($column)->setAutoSize(true);
    }

    // Aplicăm borduri pentru toate celulele cu date
    $dataRange = 'A1:I' . ($row - 1);
    $sheet->getStyle($dataRange)->getBorders()->getAllBorders()
        ->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);

    // Setăm header-ele pentru descărcare
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Cache-Control: max-age=1');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Cache-Control: cache, must-revalidate');
    header('Pragma: public');

    // Generăm și trimitem fișierul
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save('php://output');
}
?>

<div class="container ultra-streamlined-content" id="searchMainContent">
    <!-- Breadcrumbs -->
    <?php echo BreadcrumbHelper::renderBreadcrumbs('search', ['search_term' => $searchTerm]); ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card streamlined-card">
                <?php if (!empty($results) && hasMeaningfulSearchCriteria($searchParams)): ?>
                    <!-- Search Criteria Summary -->
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            <?php echo getPrimarySearchTerms($searchParams); ?>
                        </h5>
                    </div>
                    <?php
                    $searchSummary = generateSearchSummary($searchParams);
                    if ($searchSummary !== null):
                    ?>
                    <div class="card-body bg-light border-bottom">
                        <?php echo $searchSummary; ?>
                    </div>
                    <?php endif; ?>
                <?php endif; ?>

                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!$hasSearchCriteria && !$error): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Vă rugăm să completați cel puțin un criteriu de căutare: număr dosar, nume parte, obiect dosar, instanță judecătorească sau interval de date.
                        </div>
                    <?php elseif (empty($results) && !$error): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Nu au fost găsite rezultate pentru criteriile de căutare specificate.
                            <?php if (!empty($numeParte) && preg_match('/[ăâîșțĂÂÎȘȚ]/', $numeParte)): ?>
                                <hr>
                                <p><strong>Notă:</strong> Ați folosit caractere diacritice în căutare. Sistemul a încercat să caute atât cu diacritice, cât și fără, dar nu a găsit rezultate. Încercați să:</p>
                                <ul>
                                    <li>Verificați dacă numele este scris corect</li>
                                    <li>Încercați să căutați doar o parte din nume (de exemplu, doar numele de familie)</li>
                                    <li>Încercați să căutați fără diacritice manual (înlocuiți ă/â cu a, î cu i, ș cu s, ț cu t)</li>
                                </ul>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($results)): ?>
                        <div class="mb-3">
                            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-2">
                                <span class="fw-bold text-primary">
                                    <?php echo $totalResults; ?> rezultate găsite
                                    <span class="text-muted fw-normal">(pagina <?php echo $currentPage; ?>/<?php echo $totalPages; ?>)</span>
                                </span>
                                <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-2">
                                    <?php if ($totalResults > 0): ?>
                                        <button type="button"
                                                class="btn btn-success btn-sm excel-export-btn"
                                                onclick="exportToExcel()"
                                                title="Exportă toate rezultatele în format Excel (.xlsx)"
                                                data-total-results="<?php echo $totalResults; ?>">
                                            <i class="fas fa-file-excel me-1"></i>
                                            Export Excel
                                            <span class="export-count">(<?php echo $totalResults; ?> rezultate)</span>
                                        </button>
                                    <?php endif; ?>
                                    <div class="d-flex flex-column">
                                        <small class="text-muted me-sm-3">
                                            <i class="fas fa-sort me-1" style="opacity: 0.6;"></i>
                                            Sortați făcând clic pe antetele coloanelor
                                        </small>
                                        <small class="text-muted d-none d-lg-block">
                                            <i class="fas fa-expand me-1" style="opacity: 0.6;"></i>
                                            Layout expandat pentru vizualizare optimă a conținutului complet
                                        </small>
                                        <small class="text-muted d-lg-none d-sm-block">
                                            <i class="fas fa-arrows-alt-h me-1" style="opacity: 0.6;"></i>
                                            Derulați orizontal pentru a vedea toate coloanele
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Vizualizare tabel pentru desktop și tablete -->
                        <div class="table-container table-responsive">
                            <table class="table table-striped table-hover" id="tabelRezultate">
                                <thead class="thead-dark">
                                    <tr>
                                        <th class="sortable"
                                            data-sort="numar"
                                            <?php echo ($sortBy === 'numar') ? 'data-direction="' . $sortDirection . '"' : ''; ?>
                                            role="columnheader"
                                            aria-sort="<?php echo ($sortBy === 'numar') ? ($sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'; ?>"
                                            tabindex="0">
                                            <a href="<?php echo buildSortUrl($searchParams, 'numar', $sortBy, $sortDirection); ?>"
                                               class="header-link"
                                               aria-label="Sortează după număr dosar <?php echo ($sortBy === 'numar') ? ($sortDirection === 'asc' ? '(descrescător)' : '(crescător)') : '(crescător)'; ?>"
                                               title="Sortează după număr dosar">
                                                <span class="header-text">Număr dosar</span>
                                                <span class="sort-icon" aria-hidden="true">
                                                    <?php if ($sortBy === 'numar'): ?>
                                                        <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-sort"></i>
                                                    <?php endif; ?>
                                                </span>
                                            </a>
                                        </th>
                                        <th class="sortable"
                                            data-sort="instanta"
                                            <?php echo ($sortBy === 'instanta') ? 'data-direction="' . $sortDirection . '"' : ''; ?>
                                            role="columnheader"
                                            aria-sort="<?php echo ($sortBy === 'instanta') ? ($sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'; ?>"
                                            tabindex="0">
                                            <a href="<?php echo buildSortUrl($searchParams, 'instanta', $sortBy, $sortDirection); ?>"
                                               class="header-link"
                                               aria-label="Sortează după instanță <?php echo ($sortBy === 'instanta') ? ($sortDirection === 'asc' ? '(descrescător)' : '(crescător)') : '(crescător)'; ?>"
                                               title="Sortează după instanță">
                                                <span class="header-text">Instanță</span>
                                                <span class="sort-icon" aria-hidden="true">
                                                    <?php if ($sortBy === 'instanta'): ?>
                                                        <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-sort"></i>
                                                    <?php endif; ?>
                                                </span>
                                            </a>
                                        </th>
                                        <th class="sortable"
                                            data-sort="obiect"
                                            <?php echo ($sortBy === 'obiect') ? 'data-direction="' . $sortDirection . '"' : ''; ?>
                                            role="columnheader"
                                            aria-sort="<?php echo ($sortBy === 'obiect') ? ($sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'; ?>"
                                            tabindex="0">
                                            <a href="<?php echo buildSortUrl($searchParams, 'obiect', $sortBy, $sortDirection); ?>"
                                               class="header-link"
                                               aria-label="Sortează după obiect <?php echo ($sortBy === 'obiect') ? ($sortDirection === 'asc' ? '(descrescător)' : '(crescător)') : '(crescător)'; ?>"
                                               title="Sortează după obiect">
                                                <span class="header-text">Obiect</span>
                                                <span class="sort-icon" aria-hidden="true">
                                                    <?php if ($sortBy === 'obiect'): ?>
                                                        <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-sort"></i>
                                                    <?php endif; ?>
                                                </span>
                                            </a>
                                        </th>
                                        <th class="sortable" data-sort="stadiuProcesual" <?php echo ($sortBy === 'stadiuProcesual') ? 'data-direction="' . $sortDirection . '"' : ''; ?>>
                                            <div class="d-flex align-items-center">
                                                <a href="<?php echo buildSortUrl($searchParams, 'stadiuProcesual', $sortBy, $sortDirection); ?>" class="text-decoration-none text-dark">
                                                    <span>Stadiu procesual</span>
                                                    <span class="sort-icon ml-1">
                                                        <?php if ($sortBy === 'stadiuProcesual'): ?>
                                                            <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-sort"></i>
                                                        <?php endif; ?>
                                                    </span>
                                                </a>
                                            </div>
                                        </th>
                                        <th class="sortable" data-sort="data" <?php echo ($sortBy === 'data') ? 'data-direction="' . $sortDirection . '"' : ''; ?>>
                                            <div class="d-flex align-items-center">
                                                <a href="<?php echo buildSortUrl($searchParams, 'data', $sortBy, $sortDirection); ?>" class="text-decoration-none text-dark">
                                                    <span>Data</span>
                                                    <span class="sort-icon ml-1">
                                                        <?php if ($sortBy === 'data'): ?>
                                                            <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-sort"></i>
                                                        <?php endif; ?>
                                                    </span>
                                                </a>
                                            </div>
                                        </th>
                                        <th class="sortable" data-sort="categorieCaz" <?php echo ($sortBy === 'categorieCaz') ? 'data-direction="' . $sortDirection . '"' : ''; ?>>
                                            <div class="d-flex align-items-center">
                                                <a href="<?php echo buildSortUrl($searchParams, 'categorieCaz', $sortBy, $sortDirection); ?>" class="text-decoration-none text-dark">
                                                    <span>Categorie caz</span>
                                                    <span class="sort-icon ml-1">
                                                        <?php if ($sortBy === 'categorieCaz'): ?>
                                                            <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-sort"></i>
                                                        <?php endif; ?>
                                                    </span>
                                                </a>
                                            </div>
                                        </th>
                                        <th class="sortable" data-sort="dataModificare" <?php echo ($sortBy === 'dataModificare') ? 'data-direction="' . $sortDirection . '"' : ''; ?>>
                                            <div class="d-flex align-items-center">
                                                <a href="<?php echo buildSortUrl($searchParams, 'dataModificare', $sortBy, $sortDirection); ?>" class="text-decoration-none text-dark">
                                                    <span>Data ultimei modificări</span>
                                                    <span class="sort-icon ml-1">
                                                        <?php if ($sortBy === 'dataModificare'): ?>
                                                            <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-sort"></i>
                                                        <?php endif; ?>
                                                    </span>
                                                </a>
                                            </div>
                                        </th>
                                        <th class="sortable" data-sort="nume" <?php echo ($sortBy === 'nume') ? 'data-direction="' . $sortDirection . '"' : ''; ?>>
                                            <div class="d-flex align-items-center">
                                                <a href="<?php echo buildSortUrl($searchParams, 'nume', $sortBy, $sortDirection); ?>" class="text-decoration-none text-dark">
                                                    <span>Nume</span>
                                                    <span class="sort-icon ml-1">
                                                        <?php if ($sortBy === 'nume'): ?>
                                                            <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-sort"></i>
                                                        <?php endif; ?>
                                                    </span>
                                                </a>
                                            </div>
                                        </th>
                                        <th class="sortable" data-sort="calitate" <?php echo ($sortBy === 'calitate') ? 'data-direction="' . $sortDirection . '"' : ''; ?>>
                                            <div class="d-flex align-items-center">
                                                <a href="<?php echo buildSortUrl($searchParams, 'calitate', $sortBy, $sortDirection); ?>" class="text-decoration-none text-dark">
                                                    <span>Calitate</span>
                                                    <span class="sort-icon ml-1">
                                                        <?php if ($sortBy === 'calitate'): ?>
                                                            <i class="fas fa-sort-<?php echo $sortDirection === 'asc' ? 'up' : 'down'; ?>"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-sort"></i>
                                                        <?php endif; ?>
                                                    </span>
                                                </a>
                                            </div>
                                        </th>
                                        <th>Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $index => $dosar): ?>
                                    <tr class="rezultat-row"
                                        data-index="<?php echo $index; ?>"
                                        data-numar="<?php echo htmlspecialchars($dosar->numar); ?>"
                                        data-instanta="<?php
                                            // Asigurăm-ne că folosim funcția corectă pentru a obține lista instanțelor
                                            if (!isset($institutii)) {
                                                require_once 'includes/functions.php';
                                                $institutii = getInstanteList();
                                            }
                                            echo htmlspecialchars($institutii[$dosar->institutie] ?? $dosar->institutie);
                                        ?>"
                                        data-obiect="<?php echo htmlspecialchars($dosar->obiect); ?>"
                                        data-stadiu="<?php echo htmlspecialchars($dosar->stadiuProcesualNume); ?>"
                                        data-data="<?php echo htmlspecialchars($dosar->data); ?>"
                                        data-categorieCaz="<?php echo htmlspecialchars($dosar->categorieCazNume ?? ''); ?>"
                                        data-dataModificare="<?php echo htmlspecialchars($dosar->dataModificare ?? ''); ?>"
                                        data-nume="<?php
                                            $dosarId = $dosar->numar . '_' . $dosar->institutie;
                                            $relevantParty = $relevantParties[$dosarId] ?? null;
                                            echo htmlspecialchars($relevantParty ? $relevantParty['nume'] : '');
                                        ?>"
                                        data-calitate="<?php
                                            $dosarId = $dosar->numar . '_' . $dosar->institutie;
                                            $relevantParty = $relevantParties[$dosarId] ?? null;
                                            echo htmlspecialchars($relevantParty ? $relevantParty['calitate'] : '');
                                        ?>">
                                        <td>
                                            <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar); ?>&institutie=<?php echo urlencode($dosar->institutie); ?>"
                                               class="case-number-link"
                                               aria-label="Vezi detaliile dosarului <?php echo htmlspecialchars($dosar->numar); ?>"
                                               title="Vezi detaliile dosarului">
                                                <?php echo htmlspecialchars($dosar->numar); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php
                                            // Obținem lista instanțelor
                                            $institutii = getInstanteList();
                                            echo htmlspecialchars($institutii[$dosar->institutie] ?? $dosar->institutie);
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($dosar->obiect); ?></td>
                                        <td><?php echo htmlspecialchars($dosar->stadiuProcesualNume); ?></td>
                                        <td><?php echo htmlspecialchars($dosar->data); ?></td>
                                        <td><?php echo htmlspecialchars($dosar->categorieCazNume ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($dosar->dataModificare ?? '-'); ?></td>
                                        <td>
                                            <?php
                                            $dosarId = $dosar->numar . '_' . $dosar->institutie;
                                            $relevantParty = $relevantParties[$dosarId] ?? null;
                                            if ($relevantParty) {
                                                echo htmlspecialchars($relevantParty['nume']);
                                                // Afișăm indicatorul pentru părți multiple
                                                if (!empty($dosar->parti) && count($dosar->parti) > 1) {
                                                    echo ' <small class="text-muted">(+' . (count($dosar->parti) - 1) . ' alții)</small>';
                                                }
                                            } else {
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $dosarId = $dosar->numar . '_' . $dosar->institutie;
                                            $relevantParty = $relevantParties[$dosarId] ?? null;
                                            echo htmlspecialchars($relevantParty ? ($relevantParty['calitate'] ?: '-') : '-');
                                            ?>
                                        </td>
                                        <td>
                                            <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar); ?>&institutie=<?php echo urlencode($dosar->institutie); ?>"
                                               class="justice-icon-btn"
                                               data-tooltip="Detalii dosar"
                                               aria-label="Vezi detaliile dosarului <?php echo htmlspecialchars($dosar->numar); ?>"
                                               title="Vezi detaliile dosarului">
                                                <svg class="justice-icon" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                                    <!-- Meticulous Inspection Icon - Document with Magnifying Glass -->
                                                    <defs>
                                                        <linearGradient id="documentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                            <stop offset="0%" style="stop-color:rgba(255,255,255,0.9);stop-opacity:1" />
                                                            <stop offset="100%" style="stop-color:rgba(255,255,255,0.7);stop-opacity:1" />
                                                        </linearGradient>
                                                        <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                            <stop offset="0%" style="stop-color:rgba(255,255,255,0.8);stop-opacity:1" />
                                                            <stop offset="100%" style="stop-color:rgba(255,255,255,0.4);stop-opacity:1" />
                                                        </linearGradient>
                                                    </defs>

                                                    <!-- Document Background -->
                                                    <rect x="80" y="60" width="240" height="320" rx="12" ry="12"
                                                          fill="currentColor" opacity="0.9"/>
                                                    <rect x="85" y="65" width="230" height="310" rx="8" ry="8"
                                                          fill="url(#documentGradient)"/>

                                                    <!-- Document Corner Fold -->
                                                    <path d="M280 60 L320 100 L280 100 Z" fill="currentColor" opacity="0.7"/>
                                                    <path d="M285 65 L310 90 L285 90 Z" fill="rgba(255,255,255,0.5)"/>

                                                    <!-- Document Content Lines (Checklist) -->
                                                    <g fill="currentColor" opacity="0.6">
                                                        <!-- Header line -->
                                                        <rect x="110" y="100" width="160" height="8" rx="4"/>

                                                        <!-- Checklist items -->
                                                        <circle cx="120" cy="140" r="6" fill="currentColor"/>
                                                        <rect x="140" y="136" width="120" height="6" rx="3"/>
                                                        <path d="M115 137 L119 141 L125 135" stroke="rgba(255,255,255,0.9)"
                                                              stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                                                        <circle cx="120" cy="170" r="6" fill="currentColor"/>
                                                        <rect x="140" y="166" width="100" height="6" rx="3"/>
                                                        <path d="M115 167 L119 171 L125 165" stroke="rgba(255,255,255,0.9)"
                                                              stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                                                        <circle cx="120" cy="200" r="6" fill="currentColor"/>
                                                        <rect x="140" y="196" width="140" height="6" rx="3"/>
                                                        <path d="M115 197 L119 201 L125 195" stroke="rgba(255,255,255,0.9)"
                                                              stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                                                        <circle cx="120" cy="230" r="6" fill="currentColor"/>
                                                        <rect x="140" y="226" width="110" height="6" rx="3"/>

                                                        <circle cx="120" cy="260" r="6" fill="currentColor"/>
                                                        <rect x="140" y="256" width="130" height="6" rx="3"/>
                                                    </g>

                                                    <!-- Magnifying Glass -->
                                                    <g transform="translate(280, 280)">
                                                        <!-- Glass rim (outer) -->
                                                        <circle cx="0" cy="0" r="65" fill="currentColor" opacity="0.8"/>
                                                        <!-- Glass lens -->
                                                        <circle cx="0" cy="0" r="55" fill="url(#glassGradient)"/>
                                                        <!-- Glass inner rim -->
                                                        <circle cx="0" cy="0" r="55" fill="none" stroke="currentColor"
                                                                stroke-width="3" opacity="0.6"/>

                                                        <!-- Handle -->
                                                        <line x1="46" y1="46" x2="85" y2="85" stroke="currentColor"
                                                              stroke-width="12" stroke-linecap="round" opacity="0.9"/>
                                                        <line x1="48" y1="48" x2="83" y2="83" stroke="rgba(255,255,255,0.4)"
                                                              stroke-width="6" stroke-linecap="round"/>

                                                        <!-- Lens reflection -->
                                                        <ellipse cx="-15" cy="-15" rx="20" ry="25" fill="rgba(255,255,255,0.3)"
                                                                 transform="rotate(-30)"/>

                                                        <!-- Magnified content hint -->
                                                        <g opacity="0.4" transform="scale(1.2)">
                                                            <circle cx="-10" cy="5" r="3" fill="currentColor"/>
                                                            <rect x="0" y="3" width="15" height="2" rx="1" fill="currentColor"/>
                                                        </g>
                                                    </g>

                                                    <!-- Subtle shadow -->
                                                    <ellipse cx="256" cy="420" rx="120" ry="20" fill="currentColor" opacity="0.1"/>
                                                </svg>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Vizualizare card pentru mobile -->
                        <div class="card-view">
                            <?php foreach ($results as $index => $dosar): ?>
                            <div class="result-card"
                                data-index="<?php echo $index; ?>"
                                data-numar="<?php echo htmlspecialchars($dosar->numar); ?>"
                                data-instanta="<?php
                                    // Asigurăm-ne că folosim funcția corectă pentru a obține lista instanțelor
                                    if (!isset($institutii)) {
                                        require_once 'includes/functions.php';
                                        $institutii = getInstanteList();
                                    }
                                    echo htmlspecialchars($institutii[$dosar->institutie] ?? $dosar->institutie);
                                ?>"
                                data-obiect="<?php echo htmlspecialchars($dosar->obiect); ?>"
                                data-stadiu="<?php echo htmlspecialchars($dosar->stadiuProcesualNume); ?>"
                                data-data="<?php echo htmlspecialchars($dosar->data); ?>"
                                data-categorieCaz="<?php echo htmlspecialchars($dosar->categorieCazNume ?? ''); ?>"
                                data-dataModificare="<?php echo htmlspecialchars($dosar->dataModificare ?? ''); ?>"
                                data-nume="<?php
                                    $dosarId = $dosar->numar . '_' . $dosar->institutie;
                                    $relevantParty = $relevantParties[$dosarId] ?? null;
                                    echo htmlspecialchars($relevantParty ? $relevantParty['nume'] : '');
                                ?>"
                                data-calitate="<?php
                                    $dosarId = $dosar->numar . '_' . $dosar->institutie;
                                    $relevantParty = $relevantParties[$dosarId] ?? null;
                                    echo htmlspecialchars($relevantParty ? $relevantParty['calitate'] : '');
                                ?>">

                                <div class="result-card-header">
                                    <i class="fas fa-folder mr-2"></i>
                                    <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar); ?>&institutie=<?php echo urlencode($dosar->institutie); ?>"
                                       class="case-number-link"
                                       aria-label="Vezi detaliile dosarului <?php echo htmlspecialchars($dosar->numar); ?>"
                                       title="Vezi detaliile dosarului">
                                        <?php echo htmlspecialchars($dosar->numar); ?>
                                    </a>
                                </div>

                                <div class="result-card-body">
                                    <div class="result-card-item">
                                        <div class="result-card-label">Instanță:</div>
                                        <div class="result-card-value">
                                            <?php
                                            // Asigurăm-ne că avem lista instanțelor
                                            if (!isset($institutii)) {
                                                $institutii = getInstanteList();
                                            }
                                            echo htmlspecialchars($institutii[$dosar->institutie] ?? $dosar->institutie);
                                            ?>
                                        </div>
                                    </div>

                                    <div class="result-card-item">
                                        <div class="result-card-label">Obiect:</div>
                                        <div class="result-card-value">
                                            <?php echo htmlspecialchars($dosar->obiect); ?>
                                        </div>
                                    </div>

                                    <div class="result-card-item">
                                        <div class="result-card-label">Stadiu procesual:</div>
                                        <div class="result-card-value">
                                            <?php echo htmlspecialchars($dosar->stadiuProcesualNume); ?>
                                        </div>
                                    </div>

                                    <div class="result-card-item">
                                        <div class="result-card-label">Data:</div>
                                        <div class="result-card-value">
                                            <?php echo htmlspecialchars($dosar->data); ?>
                                        </div>
                                    </div>

                                    <div class="result-card-item">
                                        <div class="result-card-label">Categorie caz:</div>
                                        <div class="result-card-value">
                                            <?php echo htmlspecialchars($dosar->categorieCazNume ?? '-'); ?>
                                        </div>
                                    </div>

                                    <div class="result-card-item">
                                        <div class="result-card-label">Data ultimei modificări:</div>
                                        <div class="result-card-value">
                                            <?php echo htmlspecialchars($dosar->dataModificare ?? '-'); ?>
                                        </div>
                                    </div>

                                    <div class="result-card-item">
                                        <div class="result-card-label">Nume parte:</div>
                                        <div class="result-card-value">
                                            <?php
                                            $dosarId = $dosar->numar . '_' . $dosar->institutie;
                                            $relevantParty = $relevantParties[$dosarId] ?? null;
                                            if ($relevantParty) {
                                                echo htmlspecialchars($relevantParty['nume']);
                                                // Afișăm indicatorul pentru părți multiple
                                                if (!empty($dosar->parti) && count($dosar->parti) > 1) {
                                                    echo ' <small class="text-muted">(+' . (count($dosar->parti) - 1) . ' alții)</small>';
                                                }
                                            } else {
                                                echo '-';
                                            }
                                            ?>
                                        </div>
                                    </div>

                                    <div class="result-card-item">
                                        <div class="result-card-label">Calitate:</div>
                                        <div class="result-card-value">
                                            <?php
                                            $dosarId = $dosar->numar . '_' . $dosar->institutie;
                                            $relevantParty = $relevantParties[$dosarId] ?? null;
                                            echo htmlspecialchars($relevantParty ? ($relevantParty['calitate'] ?: '-') : '-');
                                            ?>
                                        </div>
                                    </div>


                                </div>

                                <div class="result-card-actions">
                                    <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar); ?>&institutie=<?php echo urlencode($dosar->institutie); ?>"
                                       class="justice-icon-btn"
                                       data-tooltip="Detalii dosar"
                                       aria-label="Vezi detaliile dosarului <?php echo htmlspecialchars($dosar->numar); ?>"
                                       title="Vezi detaliile dosarului">
                                        <svg class="justice-icon" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                                            <!-- Meticulous Inspection Icon - Document with Magnifying Glass -->
                                            <defs>
                                                <linearGradient id="documentGradientMobile" x1="0%" y1="0%" x2="100%" y2="100%">
                                                    <stop offset="0%" style="stop-color:rgba(255,255,255,0.9);stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:rgba(255,255,255,0.7);stop-opacity:1" />
                                                </linearGradient>
                                                <linearGradient id="glassGradientMobile" x1="0%" y1="0%" x2="100%" y2="100%">
                                                    <stop offset="0%" style="stop-color:rgba(255,255,255,0.8);stop-opacity:1" />
                                                    <stop offset="100%" style="stop-color:rgba(255,255,255,0.4);stop-opacity:1" />
                                                </linearGradient>
                                            </defs>

                                            <!-- Document Background -->
                                            <rect x="80" y="60" width="240" height="320" rx="12" ry="12"
                                                  fill="currentColor" opacity="0.9"/>
                                            <rect x="85" y="65" width="230" height="310" rx="8" ry="8"
                                                  fill="url(#documentGradientMobile)"/>

                                            <!-- Document Corner Fold -->
                                            <path d="M280 60 L320 100 L280 100 Z" fill="currentColor" opacity="0.7"/>
                                            <path d="M285 65 L310 90 L285 90 Z" fill="rgba(255,255,255,0.5)"/>

                                            <!-- Document Content Lines (Checklist) -->
                                            <g fill="currentColor" opacity="0.6">
                                                <!-- Header line -->
                                                <rect x="110" y="100" width="160" height="8" rx="4"/>

                                                <!-- Checklist items -->
                                                <circle cx="120" cy="140" r="6" fill="currentColor"/>
                                                <rect x="140" y="136" width="120" height="6" rx="3"/>
                                                <path d="M115 137 L119 141 L125 135" stroke="rgba(255,255,255,0.9)"
                                                      stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                                                <circle cx="120" cy="170" r="6" fill="currentColor"/>
                                                <rect x="140" y="166" width="100" height="6" rx="3"/>
                                                <path d="M115 167 L119 171 L125 165" stroke="rgba(255,255,255,0.9)"
                                                      stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                                                <circle cx="120" cy="200" r="6" fill="currentColor"/>
                                                <rect x="140" y="196" width="140" height="6" rx="3"/>
                                                <path d="M115 197 L119 201 L125 195" stroke="rgba(255,255,255,0.9)"
                                                      stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>

                                                <circle cx="120" cy="230" r="6" fill="currentColor"/>
                                                <rect x="140" y="226" width="110" height="6" rx="3"/>

                                                <circle cx="120" cy="260" r="6" fill="currentColor"/>
                                                <rect x="140" y="256" width="130" height="6" rx="3"/>
                                            </g>

                                            <!-- Magnifying Glass -->
                                            <g transform="translate(280, 280)">
                                                <!-- Glass rim (outer) -->
                                                <circle cx="0" cy="0" r="65" fill="currentColor" opacity="0.8"/>
                                                <!-- Glass lens -->
                                                <circle cx="0" cy="0" r="55" fill="url(#glassGradientMobile)"/>
                                                <!-- Glass inner rim -->
                                                <circle cx="0" cy="0" r="55" fill="none" stroke="currentColor"
                                                        stroke-width="3" opacity="0.6"/>

                                                <!-- Handle -->
                                                <line x1="46" y1="46" x2="85" y2="85" stroke="currentColor"
                                                      stroke-width="12" stroke-linecap="round" opacity="0.9"/>
                                                <line x1="48" y1="48" x2="83" y2="83" stroke="rgba(255,255,255,0.4)"
                                                      stroke-width="6" stroke-linecap="round"/>

                                                <!-- Lens reflection -->
                                                <ellipse cx="-15" cy="-15" rx="20" ry="25" fill="rgba(255,255,255,0.3)"
                                                         transform="rotate(-30)"/>

                                                <!-- Magnified content hint -->
                                                <g opacity="0.4" transform="scale(1.2)">
                                                    <circle cx="-10" cy="5" r="3" fill="currentColor"/>
                                                    <rect x="0" y="3" width="15" height="2" rx="1" fill="currentColor"/>
                                                </g>
                                            </g>

                                            <!-- Subtle shadow -->
                                            <ellipse cx="256" cy="420" rx="120" ry="20" fill="currentColor" opacity="0.1"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <?php if ($totalPages > 1): ?>
                            <div class="pagination-container mt-4">
                                <nav aria-label="Navigare pagini rezultate">
                                    <ul class="pagination justify-content-center">
                                        <?php if ($currentPage > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($searchParams, ['page' => $currentPage - 1])); ?>">
                                                    <i class="fas fa-chevron-left"></i> Anterior
                                                </a>
                                            </li>
                                        <?php else: ?>
                                            <li class="page-item disabled">
                                                <span class="page-link"><i class="fas fa-chevron-left"></i> Anterior</span>
                                            </li>
                                        <?php endif; ?>

                                        <?php
                                        // Afișare maxim 9 pagini în jurul paginii curente pentru navigare îmbunătățită
                                        $startPage = max(1, $currentPage - 4);
                                        $endPage = min($totalPages, $currentPage + 4);

                                        // Afișare prima pagină și elipsis dacă este necesar
                                        if ($startPage > 1) {
                                            echo '<li class="page-item"><a class="page-link" href="?' . http_build_query(array_merge($searchParams, ['page' => 1])) . '">1</a></li>';
                                            if ($startPage > 2) {
                                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                            }
                                        }

                                        // Afișare pagini din jurul paginii curente
                                        for ($i = $startPage; $i <= $endPage; $i++) {
                                            if ($i == $currentPage) {
                                                echo '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
                                            } else {
                                                echo '<li class="page-item"><a class="page-link" href="?' . http_build_query(array_merge($searchParams, ['page' => $i])) . '">' . $i . '</a></li>';
                                            }
                                        }

                                        // Afișare ultima pagină și elipsis dacă este necesar
                                        if ($endPage < $totalPages) {
                                            if ($endPage < $totalPages - 1) {
                                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                            }
                                            echo '<li class="page-item"><a class="page-link" href="?' . http_build_query(array_merge($searchParams, ['page' => $totalPages])) . '">' . $totalPages . '</a></li>';
                                        }
                                        ?>

                                        <?php if ($currentPage < $totalPages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($searchParams, ['page' => $currentPage + 1])); ?>">
                                                    Următor <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        <?php else: ?>
                                            <li class="page-item disabled">
                                                <span class="page-link">Următor <i class="fas fa-chevron-right"></i></span>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>

                                <!-- Indicator de pagină pentru mobile -->
                                <div class="text-center mt-2 d-md-none">
                                    <span class="badge badge-secondary p-2">
                                        Pagina <?php echo $currentPage; ?> din <?php echo $totalPages; ?>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (!empty($results)): ?>
                <!-- Compact Results Information -->
                <div class="results-info-compact mt-3 mb-2">
                    <div class="text-muted small">
                        <i class="fas fa-info-circle me-1" style="opacity: 0.6;"></i>
                        <span style="font-size: 0.8rem;">
                            Rezultatele conțin informații de bază despre dosarele judecătorești.
                            Pentru detalii complete, folosiți butonul "Detalii".
                            Datele sunt preluate de la Portalul Instanțelor de Judecată.
                        </span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div> <!-- End search-main-content -->

<!-- Script pentru sortarea tabelului de rezultate -->
<script>
// Funcție pentru logging
function logDebug(message, data) {
    if (window.console && console.debug) {
        if (data) {
            console.debug('[DEBUG] ' + message, data);
        } else {
            console.debug('[DEBUG] ' + message);
        }
    }
}

/**
 * Gestionează loading overlay-ul pentru pagina de căutare
 */
function initSearchLoadingOverlay() {
    const loadingOverlay = document.getElementById('searchLoadingOverlay');
    const mainContent = document.getElementById('searchMainContent');

    if (!loadingOverlay || !mainContent) {
        logDebug('Elementele pentru loading overlay nu au fost găsite!');
        return;
    }

    // Funcție pentru ascunderea loading overlay-ului
    function hideLoadingOverlay() {
        // Adăugăm clasa loaded la conținutul principal
        mainContent.classList.add('loaded');

        // Ascundem overlay-ul cu animație
        loadingOverlay.classList.add('fade-out');

        // Eliminăm complet overlay-ul după animație
        setTimeout(() => {
            if (loadingOverlay.parentNode) {
                loadingOverlay.parentNode.removeChild(loadingOverlay);
            }
        }, 500);

        logDebug('Search loading overlay ascuns cu succes.');
    }

    // Verificăm dacă pagina are rezultate sau mesaje
    const hasResults = document.querySelector('#tabelRezultate');
    const hasMessages = document.querySelector('.alert');
    const hasContent = hasResults || hasMessages;

    // Calculăm timpul minim de afișare (1-2 secunde conform cerințelor)
    const minDisplayTime = 1000; // 1 secundă
    const startTime = Date.now();

    // Funcție pentru ascunderea cu respectarea timpului minim
    function hideWithMinTime() {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        setTimeout(hideLoadingOverlay, remainingTime);
    }

    // Ascundem loading-ul după ce conținutul este gata
    if (hasContent) {
        // Dacă avem conținut, ascundem loading-ul
        hideWithMinTime();
    } else {
        // Fallback: ascundem după 2 secunde maxim
        setTimeout(hideLoadingOverlay, 2000);
    }

    logDebug('Search loading overlay inițializat pentru căutare.');
}

/**
 * Actualizează mesajul din loading overlay pentru căutare
 */
function updateSearchLoadingMessage(message, submessage = '') {
    const messageElement = document.querySelector('.search-loading-message');
    const submessageElement = document.querySelector('.search-loading-submessage');

    if (messageElement) {
        messageElement.textContent = message;
    }

    if (submessageElement) {
        submessageElement.textContent = submessage;
    }
}

/**
 * Inițializează funcționalitatea de navigare mobilă
 */
function initMobileNavigation() {
    logDebug('Inițializare navigație mobilă...');

    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-nav-menu');

    if (!mobileToggle || !mobileMenu) {
        logDebug('Elementele de navigație mobilă nu au fost găsite');
        return;
    }

    // Event listener pentru butonul de toggle
    mobileToggle.addEventListener('click', function() {
        const isExpanded = this.getAttribute('aria-expanded') === 'true';
        const newState = !isExpanded;

        // Actualizăm starea ARIA
        this.setAttribute('aria-expanded', newState);
        mobileMenu.setAttribute('aria-hidden', !newState);

        // Toggle clasa show pentru meniu
        if (newState) {
            mobileMenu.classList.add('show');
        } else {
            mobileMenu.classList.remove('show');
        }

        logDebug(`Meniu mobil ${newState ? 'deschis' : 'închis'}`);
    });

    // Închidere meniu la click pe link-uri
    const mobileLinks = mobileMenu.querySelectorAll('.mobile-nav-link');
    mobileLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Închidere meniu doar pentru link-urile interne (nu externe)
            if (!this.classList.contains('external-link')) {
                mobileToggle.setAttribute('aria-expanded', 'false');
                mobileMenu.setAttribute('aria-hidden', 'true');
                mobileMenu.classList.remove('show');
                logDebug('Meniu mobil închis după click pe link');
            }
        });
    });

    // Închidere meniu la click în afara lui
    document.addEventListener('click', function(event) {
        const isClickInsideNav = mobileToggle.contains(event.target) || mobileMenu.contains(event.target);

        if (!isClickInsideNav && mobileMenu.classList.contains('show')) {
            mobileToggle.setAttribute('aria-expanded', 'false');
            mobileMenu.setAttribute('aria-hidden', 'true');
            mobileMenu.classList.remove('show');
            logDebug('Meniu mobil închis prin click în afara lui');
        }
    });

    // Închidere meniu la apăsarea tastei Escape
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && mobileMenu.classList.contains('show')) {
            mobileToggle.setAttribute('aria-expanded', 'false');
            mobileMenu.setAttribute('aria-hidden', 'true');
            mobileMenu.classList.remove('show');
            mobileToggle.focus(); // Returnăm focus-ul la butonul de toggle
            logDebug('Meniu mobil închis prin tasta Escape');
        }
    });

    // Gestionare redimensionare fereastră
    window.addEventListener('resize', function() {
        // Închidere meniu mobil la redimensionare către desktop
        if (window.innerWidth > 992 && mobileMenu.classList.contains('show')) {
            mobileToggle.setAttribute('aria-expanded', 'false');
            mobileMenu.setAttribute('aria-hidden', 'true');
            mobileMenu.classList.remove('show');
            logDebug('Meniu mobil închis la redimensionare către desktop');
        }
    });

    logDebug('Navigație mobilă inițializată cu succes');
}

document.addEventListener('DOMContentLoaded', function() {
    logDebug('DOM încărcat, inițializare funcționalități...');

    // Inițializăm loading overlay-ul pentru căutare
    initSearchLoadingOverlay();

    // Inițializăm navigația mobilă
    initMobileNavigation();

    // NOTA: Sortarea este acum gestionată server-side prin link-uri în header-ele tabelului
    // Nu mai folosim sortarea client-side JavaScript

    // Dezactivăm complet sortarea client-side pentru a evita conflictele
    disableClientSideSorting();

    // Aplicăm sortarea și la vizualizarea card pentru mobile (doar pentru sincronizare vizuală)
    initCardViewSort();

    // Inițializare funcționalitate de căutare rapidă
    initQuickSearch();

    // Nu mai avem nevoie de tooltips pentru conținut trunchiat
    // Conținutul este afișat complet în tabel

    // Adăugăm funcționalitatea de scroll la începutul paginii după schimbarea paginii
    if (document.referrer.includes('search.php')) {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }
});

/**
 * Dezactivează sortarea client-side pentru a evita conflictele cu sortarea server-side
 */
function disableClientSideSorting() {
    logDebug('Dezactivare sortare client-side...');

    const table = document.getElementById('tabelRezultate');
    if (!table) {
        logDebug('Tabelul de rezultate nu a fost găsit');
        return;
    }

    const headers = table.querySelectorAll('th.sortable');

    headers.forEach(header => {
        // Eliminăm orice event listener de click pentru sortare client-side
        const newHeader = header.cloneNode(true);
        header.parentNode.replaceChild(newHeader, header);

        // Asigurăm-ne că header-ul nu are cursor pointer pentru click
        newHeader.style.cursor = 'default';

        logDebug('Header dezactivat pentru sortare client-side:', newHeader.getAttribute('data-sort'));
    });

    logDebug('Sortarea client-side a fost dezactivată cu succes');
}

/**
 * Inițializează funcționalitatea de sortare pentru tabelul de rezultate
 * NOTA: Această funcție este acum dezactivată în favoarea sortării server-side
 */
function initTableSort() {
    // Funcție dezactivată - sortarea se face server-side
    logDebug('initTableSort() este dezactivată - se folosește sortarea server-side');
    return;

    const table = document.getElementById('tabelRezultate');
    if (!table) return;

    const headers = table.querySelectorAll('th.sortable');

    headers.forEach(header => {
        header.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');
            const currentDirection = this.getAttribute('data-direction') || 'none';

            // Resetează toate antetele
            headers.forEach(h => {
                h.querySelector('.sort-icon').classList.remove('sort-asc', 'sort-desc');
                h.classList.remove('sort-active');
                h.removeAttribute('data-direction');
            });

            // Setează direcția de sortare
            let direction = 'asc';
            if (currentDirection === 'asc') {
                direction = 'desc';
            }

            // Actualizează antetul curent
            this.setAttribute('data-direction', direction);
            this.querySelector('.sort-icon').classList.add('sort-' + direction);
            this.classList.add('sort-active');

            // Sortează tabelul
            sortTable(table, sortBy, direction);
        });
    });
}

/**
 * Sortează tabelul în funcție de coloana și direcția specificate
 * @param {HTMLElement} table - Tabelul care trebuie sortat
 * @param {string} sortBy - Coloana după care se sortează
 * @param {string} direction - Direcția de sortare ('asc' sau 'desc')
 */
function sortTable(table, sortBy, direction) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr.rezultat-row'));

    // Cache pentru valorile sortate pentru a îmbunătăți performanța
    // Stocăm valorile calculate pentru a evita recalcularea lor la fiecare comparație
    const valueCache = new Map();

    // Funcție pentru obținerea valorii din cache sau calcularea și stocarea ei
    const getCachedValue = (row) => {
        if (!valueCache.has(row)) {
            valueCache.set(row, getRowValue(row, sortBy));
        }
        return valueCache.get(row);
    };

    // Sortează rândurile folosind cache-ul de valori
    rows.sort((a, b) => {
        const aValue = getCachedValue(a);
        const bValue = getCachedValue(b);

        return compareValues(aValue, bValue, direction);
    });

    // Reordonează rândurile în tabel
    // Folosim DocumentFragment pentru a îmbunătăți performanța
    // prin reducerea numărului de operații DOM
    const fragment = document.createDocumentFragment();
    rows.forEach(row => {
        fragment.appendChild(row);
    });

    // Adăugăm toate rândurile într-o singură operație
    tbody.appendChild(fragment);
}

/**
 * Obține valoarea unei celule din rând pentru sortare
 * @param {HTMLElement} row - Rândul din care se extrage valoarea
 * @param {string} sortBy - Coloana pentru care se extrage valoarea
 * @returns {string} - Valoarea pentru sortare
 */
function getRowValue(row, sortBy) {
    // Obține valoarea din atributul data corespunzător
    let value = row.getAttribute('data-' + sortBy) || '';

    // Tratare specială pentru date
    if (sortBy === 'data' || sortBy === 'dataModificare') {
        // Convertim data din format DD.MM.YYYY în YYYY-MM-DD pentru sortare corectă
        const parts = value.split('.');
        if (parts.length === 3) {
            value = `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
    }

    return value.toLowerCase();
}

/**
 * Compară două valori pentru sortare
 * @param {string} a - Prima valoare
 * @param {string} b - A doua valoare
 * @param {string} direction - Direcția de sortare ('asc' sau 'desc')
 * @returns {number} - Rezultatul comparației (-1, 0, 1)
 */
function compareValues(a, b, direction) {
    // Tratăm valorile goale special - le punem la sfârșit indiferent de direcția de sortare
    if (a === '' && b !== '') return 1;
    if (a !== '' && b === '') return -1;
    if (a === '' && b === '') return 0;

    // Verificăm dacă valorile sunt date
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    const isDate = dateRegex.test(a) && dateRegex.test(b);

    let result;

    if (isDate) {
        // Comparăm datele
        const dateA = new Date(a);
        const dateB = new Date(b);
        result = dateA - dateB;
    } else {
        // Comparăm textul
        result = a.localeCompare(b, 'ro', { sensitivity: 'base' });
    }

    // Inversăm rezultatul pentru sortare descendentă
    return direction === 'asc' ? result : -result;
}

/**
 * Inițializează funcționalitatea de sortare pentru vizualizarea card pe mobile
 */
function initCardViewSort() {
    // Aplicăm sortarea și la vizualizarea card pentru mobile
    const cardView = document.querySelector('.card-view');
    if (!cardView) return;

    // Funcție pentru sortarea cardurilor
    function sortCards(sortBy, direction) {
        const cards = Array.from(cardView.querySelectorAll('.result-card'));

        // Cache pentru valorile sortate pentru a îmbunătăți performanța
        const valueCache = new Map();

        // Funcție pentru obținerea valorii din cache sau calcularea și stocarea ei
        const getCachedValue = (card) => {
            if (!valueCache.has(card)) {
                valueCache.set(card, card.getAttribute('data-' + sortBy) || '');
            }
            return valueCache.get(card);
        };

        // Sortează cardurile
        cards.sort((a, b) => {
            const aValue = getCachedValue(a);
            const bValue = getCachedValue(b);

            return compareValues(aValue, bValue, direction);
        });

        // Reordonează cardurile în DOM
        const fragment = document.createDocumentFragment();
        cards.forEach(card => {
            fragment.appendChild(card);
        });

        // Adăugăm toate cardurile într-o singură operație
        cardView.appendChild(fragment);
    }

    // Observăm schimbările în atributele antetelor de sortare
    const headers = document.querySelectorAll('th.sortable');
    headers.forEach(header => {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.attributeName === 'data-direction') {
                    const sortBy = header.getAttribute('data-sort');
                    const direction = header.getAttribute('data-direction');
                    if (sortBy && direction) {
                        sortCards(sortBy, direction);
                    }
                }
            });
        });

        observer.observe(header, { attributes: true });
    });
}

/**
 * Funcție eliminată - nu mai este necesară pentru conținut complet afișat
 * Conținutul este acum afișat complet în tabel fără truncare
 */

/**
 * Inițializează funcționalitatea de căutare rapidă pentru numele părților
 */
function initQuickSearch() {
    logDebug('Inițializare funcționalitate de căutare rapidă...');

    // Verificăm dacă suntem pe pagina de rezultate căutare
    const tabelRezultate = document.getElementById('tabelRezultate');
    if (!tabelRezultate) {
        logDebug('Tabelul de rezultate nu a fost găsit, se ignoră inițializarea căutării rapide');
        return;
    }

    // Adăugăm un tooltip pentru a informa utilizatorul despre funcționalitatea de căutare rapidă
    const tableContainer = tabelRezultate.closest('.table-container');
    if (tableContainer) {
        const tooltip = document.createElement('div');
        tooltip.className = 'search-tooltip';
        tooltip.innerHTML = '<i class="fas fa-info-circle mr-1"></i> Puteți face click pe numele unei părți pentru a căuta rapid toate dosarele asociate';

        tableContainer.insertBefore(tooltip, tabelRezultate);

        // Ascundem tooltip-ul după 5 secunde
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                tooltip.remove();
            }, 500);
        }, 5000);
    }

    // Adăugăm funcționalitatea de căutare rapidă pentru fiecare rând din tabel
    const rows = tabelRezultate.querySelectorAll('tbody tr.rezultat-row');
    rows.forEach((row, index) => {
        // Obținem celulele pentru obiect (care poate conține numele părților)
        const obiectCell = row.querySelector('td:nth-child(3)');

        if (obiectCell) {
            // Verificăm dacă celula conține text care ar putea fi un nume de parte
            const text = obiectCell.textContent.trim();
            if (text && text !== '-') {
                // Adăugăm clasa clickable-cell și event listener pentru celula obiect
                obiectCell.classList.add('clickable-cell');
                obiectCell.setAttribute('title', 'Click pentru a căuta dosare cu această parte');
                obiectCell.addEventListener('click', function() {
                    quickSearch(text);
                });
                logDebug(`Rândul ${index}: Adăugat event listener pentru celula obiect`);
            }
        }
    });

    // Adăugăm funcționalitatea de căutare rapidă pentru vizualizarea card pe mobile
    const cardView = document.querySelector('.card-view');
    if (cardView) {
        const cards = cardView.querySelectorAll('.result-card');
        cards.forEach((card, index) => {
            // Obținem elementul care conține obiectul dosarului
            const obiectValue = card.querySelector('.result-card-item:nth-child(3) .result-card-value');

            if (obiectValue) {
                // Verificăm dacă elementul conține text care ar putea fi un nume de parte
                const text = obiectValue.textContent.trim();
                if (text && text !== '-') {
                    // Adăugăm clasa clickable-cell și event listener pentru elementul obiect
                    obiectValue.classList.add('clickable-cell');
                    obiectValue.setAttribute('title', 'Click pentru a căuta dosare cu această parte');
                    obiectValue.addEventListener('click', function() {
                        quickSearch(text);
                    });
                    logDebug(`Card ${index}: Adăugat event listener pentru elementul obiect`);
                }
            }
        });
    }

    logDebug('Funcționalitate de căutare rapidă inițializată cu succes');
}

/**
 * Efectuează o căutare rapidă pentru numele unei părți
 * @param {string} text - Textul care va fi căutat
 */
function quickSearch(text) {
    // Curățăm textul de HTML și spații suplimentare
    const cleanText = cleanSearchText(text);

    if (!cleanText) {
        logDebug('Textul pentru căutare rapidă este gol după curățare');
        return;
    }

    logDebug('Căutare rapidă pentru textul:', cleanText);

    // Redirecționăm către pagina index.php cu parametrul numeParte completat
    window.location.href = 'index.php?numeParte=' + encodeURIComponent(cleanText);
}

/**
 * Curăță textul pentru căutare de HTML și spații suplimentare
 * @param {string} text - Textul care trebuie curățat
 * @returns {string} - Textul curățat
 */
function cleanSearchText(text) {
    if (!text) return '';

    // Creăm un element temporar pentru a curăța HTML-ul
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = text;

    // Obținem textul fără HTML
    let cleanText = tempDiv.textContent || tempDiv.innerText || '';

    // Eliminăm spațiile suplimentare
    cleanText = cleanText.replace(/\s+/g, ' ').trim();

    // Păstrăm caracterele diacritice și alte caractere relevante
    // Eliminăm doar caracterele care ar putea cauza probleme în căutare
    // dar păstrăm diacriticele (ă, â, î, ș, ț, Ă, Â, Î, Ș, Ț)
    cleanText = cleanText.replace(/[^\w\s\-.,ăâîșțĂÂÎȘȚ]/g, '');

    // Logăm textul curățat pentru depanare
    console.debug('[DEBUG] Text curățat pentru căutare:', cleanText);

    return cleanText;
}

// Inițializează loading overlay-ul imediat (înainte de DOMContentLoaded)
// pentru a fi siguri că este vizibil de la început
(function() {
    // Verificăm dacă loading overlay-ul există
    function checkAndInitSearchLoading() {
        const loadingOverlay = document.getElementById('searchLoadingOverlay');
        if (loadingOverlay) {
            // Overlay-ul este deja vizibil prin CSS
            logDebug('Search loading overlay detectat și activ.');
        } else {
            // Reîncercăm după un scurt interval
            setTimeout(checkAndInitSearchLoading, 50);
        }
    }

    // Începem verificarea
    if (document.readyState === 'loading') {
        checkAndInitSearchLoading();
    }
})();

/**
 * Inițializează iconițele de justiție cu fallback
 */
function initJusticeIcons() {
    logDebug('Inițializare iconițe de justiție...');

    const justiceButtons = document.querySelectorAll('.justice-icon-btn');

    justiceButtons.forEach(button => {
        const svg = button.querySelector('.justice-icon');

        if (svg) {
            // Verificăm dacă SVG-ul s-a încărcat corect
            svg.addEventListener('load', function() {
                button.classList.add('svg-loaded');
                logDebug('SVG încărcat cu succes pentru butonul de justiție');
            });

            svg.addEventListener('error', function() {
                logDebug('Eroare la încărcarea SVG, folosim fallback');
                showFallbackIcon(button);
            });

            // Verificăm dacă SVG-ul este deja încărcat
            if (svg.complete || svg.readyState === 'complete') {
                button.classList.add('svg-loaded');
            } else {
                // Timeout pentru fallback în caz că SVG-ul nu se încarcă
                setTimeout(() => {
                    if (!button.classList.contains('svg-loaded')) {
                        logDebug('Timeout pentru încărcarea SVG, folosim fallback');
                        showFallbackIcon(button);
                    }
                }, 2000);
            }
        } else {
            // Dacă nu există SVG, afișăm direct fallback-ul
            showFallbackIcon(button);
        }
    });

    logDebug(`Inițializate ${justiceButtons.length} iconițe de justiție`);
}

/**
 * Afișează iconița de fallback
 */
function showFallbackIcon(button) {
    // Adăugăm iconița de fallback dacă nu există deja
    let fallbackIcon = button.querySelector('.fallback-icon');
    if (!fallbackIcon) {
        fallbackIcon = document.createElement('i');
        fallbackIcon.className = 'fas fa-gavel fallback-icon';
        fallbackIcon.setAttribute('aria-hidden', 'true');
        button.appendChild(fallbackIcon);
    }

    button.classList.remove('svg-loaded');
    logDebug('Fallback icon afișat pentru butonul de justiție');
}

// Inițializăm iconițele când DOM-ul este gata
document.addEventListener('DOMContentLoaded', function() {
    initJusticeIcons();
});

/**
 * Exportă rezultatele căutării în format Excel
 */
function exportToExcel() {
    const exportBtn = document.querySelector('.excel-export-btn');
    const totalResults = exportBtn.getAttribute('data-total-results');

    // Afișăm loading overlay
    showExportLoading(totalResults);

    // Dezactivăm butonul
    exportBtn.disabled = true;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Se exportă...';

    // Construim parametrii pentru export
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set('export', 'xlsx'); // Folosim xlsx pentru Excel
    searchParams.set('all_results', '1'); // Flag pentru toate rezultatele

    // Creăm un link pentru download forțat
    const downloadUrl = 'search.php?' + searchParams.toString();

    // Metodă îmbunătățită pentru download forțat
    try {
        // Încercăm mai întâi cu un link direct
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = 'Rezultate_Cautare_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.xlsx';
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);

        // Simulăm click-ul pentru download
        downloadLink.click();

        // Eliminăm link-ul după un scurt delay
        setTimeout(() => {
            if (downloadLink.parentNode) {
                downloadLink.parentNode.removeChild(downloadLink);
            }
        }, 1000);

        // Fallback cu iframe pentru browsere mai vechi
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.style.width = '0';
        iframe.style.height = '0';
        iframe.style.border = 'none';
        iframe.src = downloadUrl;
        document.body.appendChild(iframe);

        // Eliminăm iframe-ul după download
        setTimeout(() => {
            if (iframe.parentNode) {
                iframe.parentNode.removeChild(iframe);
            }
        }, 10000);

    } catch (error) {
        console.error('Eroare la inițierea download-ului:', error);
        // Fallback: deschidere în fereastră nouă
        window.open(downloadUrl, '_blank');
    }

    // Monitorizăm progresul și finalizarea
    let progressInterval = setInterval(() => {
        // Simulăm progresul (în realitate ar trebui să vină de la server)
        const progressElement = document.getElementById('exportProgressCount');
        if (progressElement) {
            const currentCount = parseInt(progressElement.textContent) || 0;
            const targetCount = parseInt(totalResults);
            if (currentCount < targetCount) {
                progressElement.textContent = Math.min(currentCount + Math.ceil(targetCount / 20), targetCount);
            }
        }
    }, 100);

    // Ascundem loading-ul după 3 secunde (redus pentru experiență mai bună)
    setTimeout(() => {
        hideExportLoading();
        clearInterval(progressInterval);

        // Reactivăm butonul
        exportBtn.disabled = false;
        exportBtn.innerHTML = '<i class="fas fa-file-excel me-1"></i>Export Excel <span class="export-count">(' + totalResults + ' rezultate)</span>';

        // Afișăm mesaj de succes
        showNotification('Fișierul Excel a fost descărcat cu succes! Poate fi deschis în Microsoft Excel sau LibreOffice Calc.', 'success');
    }, 3000);
}

/**
 * Afișează loading overlay pentru export
 */
function showExportLoading(totalResults) {
    const overlay = document.getElementById('exportLoadingOverlay');
    const progressCount = document.getElementById('exportProgressCount');

    if (overlay) {
        overlay.style.display = 'flex';
        if (progressCount) {
            progressCount.textContent = '0';
        }
    }
}

/**
 * Ascunde loading overlay pentru export
 */
function hideExportLoading() {
    const overlay = document.getElementById('exportLoadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

/**
 * Verifică dacă download-ul a fost inițiat cu succes
 */
function verifyDownloadStart(downloadUrl) {
    return new Promise((resolve, reject) => {
        // Creăm o cerere HEAD pentru a verifica dacă fișierul poate fi generat
        fetch(downloadUrl, {
            method: 'HEAD',
            cache: 'no-cache'
        })
        .then(response => {
            if (response.ok) {
                resolve(true);
            } else {
                reject(new Error('Serverul nu poate genera fișierul Excel'));
            }
        })
        .catch(error => {
            reject(error);
        });
    });
}

/**
 * Gestionează erorile de export
 */
function handleExportError(error) {
    console.error('Eroare la exportul Excel:', error);

    hideExportLoading();

    // Reactivăm butonul
    const exportBtn = document.querySelector('.excel-export-btn');
    if (exportBtn) {
        exportBtn.disabled = false;
        const totalResults = exportBtn.getAttribute('data-total-results');
        exportBtn.innerHTML = '<i class="fas fa-file-excel me-1"></i>Export Excel <span class="export-count">(' + totalResults + ' rezultate)</span>';
    }

    // Afișăm mesaj de eroare
    const errorMessage = error.message || 'A apărut o eroare la generarea fișierului Excel';
    showNotification('Eroare: ' + errorMessage, 'danger');
}
</script>

<!-- Loading overlay pentru export Excel -->
<div class="export-loading-overlay" id="exportLoadingOverlay">
    <div class="export-loading-content">
        <div class="export-loading-spinner"></div>
        <h5 class="mb-2">Se exportă datele în format Excel...</h5>
        <p class="text-muted mb-0">Vă rugăm să așteptați, se procesează <span id="exportProgressCount">0</span> rezultate.</p>
    </div>
</div>

<!-- Secțiune cu linkuri utile -->
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="h6 text-primary mb-3">
                                <i class="fas fa-search me-2"></i>Alte Opțiuni de Căutare
                            </h3>
                            <ul class="list-unstyled small">
                                <li class="mb-2">
                                    <i class="fas fa-chevron-right text-primary me-2"></i>
                                    <a href="index.php" class="text-decoration-none">Căutare rapidă</a> - pentru căutări simple în portal judiciar românia
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-chevron-right text-primary me-2"></i>
                                    <a href="avans.php" class="text-decoration-none">Căutare în masă</a> - verificare dosare tribunal multiple
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-chevron-right text-primary me-2"></i>
                                    <a href="avansat.php" class="text-decoration-none">Căutare avansată</a> - cu filtre complexe pentru dosare civile penale
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h3 class="h6 text-primary mb-3">
                                <i class="fas fa-calendar me-2"></i>Informații Suplimentare
                            </h3>
                            <ul class="list-unstyled small">
                                <li class="mb-2">
                                    <i class="fas fa-chevron-right text-primary me-2"></i>
                                    <a href="sedinte.php" class="text-decoration-none">Ședințe judecătorești</a> - program instanțe România
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-chevron-right text-primary me-2"></i>
                                    <a href="http://portal.just.ro" target="_blank" rel="noopener" class="text-decoration-none">Portal Just Oficial</a> - sursa oficială
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-chevron-right text-primary me-2"></i>
                                    <a href="contact.php" class="text-decoration-none">Contact</a> - suport pentru căutare dosare instanțe
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>