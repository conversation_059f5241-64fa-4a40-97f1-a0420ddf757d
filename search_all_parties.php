<?php
/**
 * Comprehensive search through all parties in case 130/98/2022
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test parameters
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<h1>Complete Party List Analysis</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<hr>";

try {
    // Initialize service
    $dosarService = new DosarService();
    
    // Get case details
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar || empty((array)$dosar)) {
        echo "<p style='color: red;'>ERROR: No case data returned</p>";
        exit;
    }
    
    $totalParties = count($dosar->parti);
    echo "<h2>Complete Party List ({$totalParties} parties)</h2>";
    
    // Search for parties containing specific terms
    $searchTerms = ['SARAGEA', 'TUDORIŢA', 'TUDORITA', 'Intervenient', 'intervenient'];
    
    foreach ($searchTerms as $term) {
        echo "<h3>Parties containing '{$term}':</h3>";
        $found = false;
        
        foreach ($dosar->parti as $index => $parte) {
            $partyName = $parte['nume'] ?? '';
            $partyQuality = $parte['calitate'] ?? '';
            
            if (stripos($partyName, $term) !== false || stripos($partyQuality, $term) !== false) {
                echo "<div style='background: #e7f3ff; padding: 5px; margin: 2px 0; border-left: 3px solid #007bff;'>";
                echo "<strong>#" . ($index + 1) . ":</strong> {$partyName}";
                if (!empty($partyQuality)) {
                    echo " - <em>{$partyQuality}</em>";
                }
                echo "</div>";
                $found = true;
            }
        }
        
        if (!$found) {
            echo "<p style='color: #666; font-style: italic;'>No parties found containing '{$term}'</p>";
        }
        echo "<br>";
    }
    
    // Display all parties with index for manual verification
    echo "<h3>Complete Party List (for manual verification):</h3>";
    echo "<div style='max-height: 600px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f8f9fa;'><th>Index</th><th>Party Name</th><th>Quality</th></tr>";
    
    foreach ($dosar->parti as $index => $parte) {
        $partyName = htmlspecialchars($parte['nume'] ?? '');
        $partyQuality = htmlspecialchars($parte['calitate'] ?? '');
        
        // Highlight rows that might be relevant
        $highlight = '';
        if (stripos($partyName, 'SARAGEA') !== false || 
            stripos($partyName, 'TUDORIŢA') !== false || 
            stripos($partyQuality, 'Intervenient') !== false) {
            $highlight = 'background: #fff3cd;';
        }
        
        echo "<tr style='{$highlight}'>";
        echo "<td>" . ($index + 1) . "</td>";
        echo "<td>{$partyName}</td>";
        echo "<td>{$partyQuality}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Search for similar names using fuzzy matching
    echo "<h3>Fuzzy Search for Similar Names:</h3>";
    $targetName = 'SARAGEA TUDORIŢA';
    $similarParties = [];
    
    foreach ($dosar->parti as $index => $parte) {
        $partyName = $parte['nume'] ?? '';
        
        // Calculate similarity
        $similarity = 0;
        similar_text(strtolower($targetName), strtolower($partyName), $similarity);
        
        if ($similarity > 30) { // More than 30% similar
            $similarParties[] = [
                'index' => $index + 1,
                'name' => $partyName,
                'quality' => $parte['calitate'] ?? '',
                'similarity' => round($similarity, 2)
            ];
        }
    }
    
    if (!empty($similarParties)) {
        // Sort by similarity
        usort($similarParties, function($a, $b) {
            return $b['similarity'] <=> $a['similarity'];
        });
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th>Index</th><th>Party Name</th><th>Quality</th><th>Similarity %</th></tr>";
        
        foreach ($similarParties as $party) {
            echo "<tr>";
            echo "<td>{$party['index']}</td>";
            echo "<td>" . htmlspecialchars($party['name']) . "</td>";
            echo "<td>" . htmlspecialchars($party['quality']) . "</td>";
            echo "<td>{$party['similarity']}%</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No similar names found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>EXCEPTION: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><em>Analysis completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
