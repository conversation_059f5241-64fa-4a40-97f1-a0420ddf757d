<?php
/**
 * SOAP API Response Structure Analyzer
 * Investigates potential pagination and limiting in the Romanian judicial portal API
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test parameters
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<h1>SOAP API Response Structure Analysis</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Purpose:</strong> Investigate potential 100-party API limitation</p>";
echo "<hr>";

try {
    // Initialize service with debug mode
    $dosarService = new DosarService();
    
    // Get raw SOAP response with detailed logging
    echo "<h2>1. Raw SOAP Response Analysis</h2>";
    
    // Create SOAP client manually to capture raw response using existing config
    $wsdl = SOAP_WSDL;
    $options = [
        'soap_version' => SOAP_1_2,
        'trace' => true,
        'exceptions' => true,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'connection_timeout' => 10,
        'features' => SOAP_SINGLE_ELEMENT_ARRAYS
    ];

    echo "<p><strong>Using WSDL:</strong> {$wsdl}</p>";

    $client = new SoapClient($wsdl, $options);
    
    // Prepare request parameters (using same structure as DosarService)
    $params = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];

    echo "<h3>Request Parameters:</h3>";
    echo "<pre>" . print_r($params, true) . "</pre>";

    // Make the SOAP call using the correct method
    $startTime = microtime(true);
    $response = $client->CautareDosare2($params);
    $endTime = microtime(true);
    
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    
    echo "<h3>Response Time: {$responseTime}ms</h3>";
    
    // Capture raw XML
    $rawRequest = $client->__getLastRequest();
    $rawResponse = $client->__getLastResponse();
    
    echo "<h3>Raw SOAP Request:</h3>";
    echo "<textarea style='width: 100%; height: 200px;'>" . htmlspecialchars($rawRequest) . "</textarea>";
    
    echo "<h3>Raw SOAP Response (First 5000 chars):</h3>";
    echo "<textarea style='width: 100%; height: 300px;'>" . htmlspecialchars(substr($rawResponse, 0, 5000)) . "</textarea>";
    
    // Analyze response structure
    echo "<h2>2. Response Structure Analysis</h2>";

    if ($response && isset($response->CautareDosare2Result)) {
        $result = $response->CautareDosare2Result;

        echo "<h3>CautareDosare2Result Structure:</h3>";
        echo "<ul>";
        foreach (get_object_vars($result) as $property => $value) {
            $type = gettype($value);
            if (is_array($value)) {
                $count = count($value);
                echo "<li><strong>{$property}</strong>: Array with {$count} elements</li>";
            } elseif (is_object($value)) {
                $props = count(get_object_vars($value));
                echo "<li><strong>{$property}</strong>: Object with {$props} properties</li>";
            } else {
                $valueStr = is_string($value) ? "'" . substr($value, 0, 50) . "'" : $value;
                echo "<li><strong>{$property}</strong>: {$type} = {$valueStr}</li>";
            }
        }
        echo "</ul>";

        // Get the actual dosar data
        $dosarData = null;
        if (isset($result->Dosar)) {
            $dosare = $result->Dosar;
            if (!is_array($dosare)) {
                $dosarData = $dosare;
            } else {
                // Find the specific case
                foreach ($dosare as $dosar) {
                    if ($dosar->numar === $numarDosar && $dosar->institutie === $institutie) {
                        $dosarData = $dosar;
                        break;
                    }
                }
            }
        }

        if (!$dosarData) {
            echo "<p style='color: red;'>ERROR: Could not find case data in response</p>";
            echo "<h3>Available Dosare:</h3>";
            if (isset($result->Dosar)) {
                if (is_array($result->Dosar)) {
                    foreach ($result->Dosar as $i => $dosar) {
                        echo "<p>Dosar {$i}: {$dosar->numar} from {$dosar->institutie}</p>";
                    }
                } else {
                    echo "<p>Single Dosar: {$result->Dosar->numar} from {$result->Dosar->institutie}</p>";
                }
            }
            return;
        }
        
        echo "<h3>Main Response Object Properties:</h3>";
        echo "<ul>";
        foreach (get_object_vars($dosarData) as $property => $value) {
            $type = gettype($value);
            if (is_array($value)) {
                $count = count($value);
                echo "<li><strong>{$property}</strong>: Array with {$count} elements</li>";
            } elseif (is_object($value)) {
                $props = count(get_object_vars($value));
                echo "<li><strong>{$property}</strong>: Object with {$props} properties</li>";
            } else {
                $valueStr = is_string($value) ? "'" . substr($value, 0, 50) . "'" : $value;
                echo "<li><strong>{$property}</strong>: {$type} = {$valueStr}</li>";
            }
        }
        echo "</ul>";
        
        // Analyze parties array specifically
        if (isset($dosarData->parti) && is_array($dosarData->parti)) {
            $partiCount = count($dosarData->parti);
            echo "<h3>Parties Analysis:</h3>";
            echo "<p><strong>Total parties found:</strong> {$partiCount}</p>";
            
            // Check for pagination indicators
            echo "<h4>Checking for Pagination Indicators:</h4>";
            $paginationFields = ['totalCount', 'totalResults', 'hasMore', 'nextPage', 'pageSize', 'maxResults', 'limit', 'offset', 'page'];
            $foundPagination = false;
            
            foreach ($paginationFields as $field) {
                if (isset($dosarData->$field)) {
                    echo "<p style='color: green;'>✓ Found pagination field: <strong>{$field}</strong> = " . $dosarData->$field . "</p>";
                    $foundPagination = true;
                }
            }
            
            if (!$foundPagination) {
                echo "<p style='color: orange;'>⚠️ No standard pagination fields found in response</p>";
            }
            
            // Analyze first and last parties
            echo "<h4>First Party:</h4>";
            echo "<pre>" . print_r($dosarData->parti[0], true) . "</pre>";
            
            echo "<h4>Last Party:</h4>";
            echo "<pre>" . print_r($dosarData->parti[$partiCount - 1], true) . "</pre>";
            
            // Check if exactly 100 parties
            if ($partiCount === 100) {
                echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
                echo "<h4 style='color: #856404;'>⚠️ POTENTIAL API LIMITATION DETECTED</h4>";
                echo "<p>Exactly 100 parties found - this could indicate an API limit rather than the actual number of parties.</p>";
                echo "</div>";
            }
        }
        
        // Check response headers for additional metadata
        echo "<h3>Response Headers Analysis:</h3>";
        $headers = $client->__getLastResponseHeaders();
        if ($headers) {
            echo "<pre>" . htmlspecialchars($headers) . "</pre>";
        } else {
            echo "<p>No response headers captured</p>";
        }
        
    } else {
        echo "<p style='color: red;'>ERROR: No valid response received</p>";
    }
    
} catch (SoapFault $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24;'>SOAP Fault:</h4>";
    echo "<p><strong>Code:</strong> " . $e->getCode() . "</p>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Detail:</strong> " . htmlspecialchars($e->detail ?? 'N/A') . "</p>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24;'>Exception:</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Analysis completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
