<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Raport zilnic - Dosarele monitorizate</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #17a2b8;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .summary-box {
            background-color: #e1f5fe;
            border: 1px solid #b3e5fc;
            border-left: 4px solid #17a2b8;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
            text-align: center;
        }
        .summary-box h3 {
            margin: 0 0 10px 0;
            color: #0277bd;
            font-size: 18px;
        }
        .summary-stats {
            font-size: 24px;
            font-weight: 600;
            color: #17a2b8;
        }
        .case-list {
            margin: 20px 0;
        }
        .case-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .case-item:last-child {
            margin-bottom: 0;
        }
        .case-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        .case-number {
            font-weight: 600;
            color: #007bff;
            font-size: 16px;
        }
        .changes-count {
            background-color: #17a2b8;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        .case-institution {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .case-changes {
            color: #495057;
            font-size: 14px;
            line-height: 1.4;
        }
        .action-button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
        .action-button:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .footer p {
            margin: 5px 0;
            font-size: 14px;
            color: #6c757d;
        }
        .footer .portal-name {
            font-weight: 600;
            color: #007bff;
        }
        .no-changes {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        .no-changes .icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .header {
                padding: 20px 15px;
            }
            .content {
                padding: 20px 15px;
            }
            .case-header {
                flex-direction: column;
                align-items: flex-start;
            }
            .changes-count {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>{{ portal_name }}</h1>
            <p>Raport zilnic - {{ date }}</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                Bună ziua <strong>{{ user_name }}</strong>,
            </div>
            
            {% if total_cases > 0 %}
                <div class="summary-box">
                    <h3>📊 Rezumat zilnic</h3>
                    <div class="summary-stats">{{ total_cases }}</div>
                    <p>dosare cu modificări în ultimele 24 de ore</p>
                </div>
                
                <div class="case-list">
                    {% for case in cases %}
                    <div class="case-item">
                        <div class="case-header">
                            <span class="case-number">📋 {{ case.case_number }}</span>
                            <span class="changes-count">{{ case.changes_count }} modificări</span>
                        </div>
                        <div class="case-institution">🏛️ {{ case.institution_name }}</div>
                        {% if case.case_object %}
                        <div class="case-institution">📝 {{ case.case_object }}</div>
                        {% endif %}
                        <div class="case-changes">
                            <strong>Modificări:</strong> {{ case.recent_changes }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div style="text-align: center;">
                    <a href="{{ base_url }}/monitor.php" class="action-button">
                        Vezi toate dosarele monitorizate
                    </a>
                </div>
                
                <p style="margin-top: 30px; font-size: 14px; color: #6c757d;">
                    💡 <strong>Sfat:</strong> Pentru a vedea detaliile complete ale unui dosar, faceți clic pe numărul dosarului sau accesați secțiunea de căutare.
                </p>
            {% else %}
                <div class="no-changes">
                    <div class="icon">😴</div>
                    <h3>Nicio modificare detectată</h3>
                    <p>Nu s-au detectat modificări în dosarele monitorizate în ultimele 24 de ore.</p>
                    <p>Veți fi notificat imediat ce se produc modificări.</p>
                </div>
                
                <div style="text-align: center;">
                    <a href="{{ base_url }}/monitor.php" class="action-button">
                        Gestionează dosarele monitorizate
                    </a>
                </div>
            {% endif %}
            
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <strong>🔔 Frecvența notificărilor:</strong> Primiți acest raport zilnic la ora 08:00. 
                Pentru a modifica frecvența notificărilor, accesați 
                <a href="{{ base_url }}/monitor.php" style="color: #007bff;">panoul de monitorizare</a>.
            </div>
        </div>
        
        <div class="footer">
            <p class="portal-name">{{ portal_name }}</p>
            <p>&copy; {{ "now"|date("Y") }} Portal Judiciar România. Toate drepturile rezervate.</p>
            <p>
                <a href="{{ base_url }}" style="color: #007bff; text-decoration: none;">{{ base_url }}</a>
            </p>
        </div>
    </div>
</body>
</html>
