<?php
/**
 * Test specific institution code validation
 */

require_once 'services/SedinteService.php';
require_once 'includes/functions.php';

// Test the specific problematic institution code
$testCode = 'CurteadeApelALBAIULIA';
$testDate = '24.06.2025';

echo "<!DOCTYPE html>\n";
echo "<html lang='ro'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "    <title>Test Instituție Specifică</title>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>\n";
echo "</head>\n";
echo "<body>\n";
echo "<div class='container mt-4'>\n";
echo "    <h1 class='text-primary mb-4'><i class='fas fa-building me-2'></i>Test Validare Instituție Specifică</h1>\n";

echo "    <div class='card'>\n";
echo "        <div class='card-header'>\n";
echo "            <h5 class='mb-0'>Test pentru: {$testCode}</h5>\n";
echo "        </div>\n";
echo "        <div class='card-body'>\n";

try {
    $sedinteService = new SedinteService();
    
    echo "            <div class='alert alert-info'>\n";
    echo "                <strong>Încercare căutare ședințe...</strong><br>\n";
    echo "                Cod instituție: <code>{$testCode}</code><br>\n";
    echo "                Data: <code>{$testDate}</code>\n";
    echo "            </div>\n";
    
    $results = $sedinteService->cautareSedinte($testDate, $testCode);
    
    echo "            <div class='alert alert-success'>\n";
    echo "                <strong>✅ Succes!</strong><br>\n";
    echo "                Codul instituției este valid. Găsite " . count($results) . " ședințe.\n";
    echo "            </div>\n";
    
} catch (Exception $e) {
    echo "            <div class='alert alert-danger'>\n";
    echo "                <strong>❌ Eroare SOAP:</strong><br>\n";
    echo "                <code>" . htmlspecialchars($e->getMessage()) . "</code>\n";
    echo "            </div>\n";
    
    // Let's try some variations
    $variations = [
        'CurteadeApelAlbaIulia',
        'CurteadeApelALBA_IULIA',
        'CurteadeApelAlba_Iulia',
        'CurteadeApelALBA-IULIA',
        'CurteadeApelAlba-Iulia',
        'CurteadeApelALBAIULIA',
        'CurteadeApelAlbaIulia',
        'CurteadeApelALBA',
        'CurteadeApelAlba'
    ];
    
    echo "            <div class='mt-4'>\n";
    echo "                <h6 class='text-warning'>Testare variații posibile:</h6>\n";
    echo "                <div class='list-group'>\n";
    
    foreach ($variations as $variation) {
        echo "                    <div class='list-group-item'>\n";
        echo "                        <div class='d-flex justify-content-between align-items-center'>\n";
        echo "                            <code>{$variation}</code>\n";
        
        try {
            $testResults = $sedinteService->cautareSedinte($testDate, $variation);
            echo "                            <span class='badge badge-success'>✅ Valid (" . count($testResults) . " ședințe)</span>\n";
        } catch (Exception $varError) {
            $errorMsg = $varError->getMessage();
            if (strpos($errorMsg, 'not a valid value for Institutie') !== false) {
                echo "                            <span class='badge badge-danger'>❌ Invalid</span>\n";
            } else {
                echo "                            <span class='badge badge-warning'>⚠️ Altă eroare</span>\n";
            }
        }
        
        echo "                        </div>\n";
        echo "                    </div>\n";
    }
    
    echo "                </div>\n";
    echo "            </div>\n";
}

// Also check what's in our local institution list
$institutii = getInstanteList();
echo "            <div class='mt-4'>\n";
echo "                <h6 class='text-info'>Informații din lista locală:</h6>\n";
if (isset($institutii[$testCode])) {
    echo "                <div class='alert alert-info'>\n";
    echo "                    <strong>Găsit în lista locală:</strong><br>\n";
    echo "                    Cod: <code>{$testCode}</code><br>\n";
    echo "                    Nume: <code>" . htmlspecialchars($institutii[$testCode]) . "</code>\n";
    echo "                </div>\n";
} else {
    echo "                <div class='alert alert-warning'>\n";
    echo "                    <strong>Nu este găsit în lista locală!</strong>\n";
    echo "                </div>\n";
}

// Search for similar codes in local list
echo "                <h6 class='text-info mt-3'>Coduri similare în lista locală:</h6>\n";
echo "                <div class='list-group'>\n";
foreach ($institutii as $code => $name) {
    if (stripos($code, 'alba') !== false || stripos($name, 'alba') !== false) {
        echo "                    <div class='list-group-item'>\n";
        echo "                        <strong>Cod:</strong> <code>{$code}</code><br>\n";
        echo "                        <strong>Nume:</strong> {$name}\n";
        echo "                    </div>\n";
    }
}
echo "                </div>\n";
echo "            </div>\n";

echo "        </div>\n";
echo "    </div>\n";

echo "    <div class='text-center mt-4'>\n";
echo "        <a href='sedinte.php' class='btn btn-primary'>\n";
echo "            <i class='fas fa-arrow-left'></i> Înapoi la Căutare Ședințe\n";
echo "        </a>\n";
echo "        <a href='validate_institution_codes.php' class='btn btn-secondary'>\n";
echo "            <i class='fas fa-check-circle'></i> Validare Completă\n";
echo "        </a>\n";
echo "    </div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
