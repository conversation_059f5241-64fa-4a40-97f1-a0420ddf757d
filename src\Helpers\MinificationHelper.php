<?php

namespace App\Helpers;

/**
 * Minification Helper - Gestionează minificarea CSS și JS pentru optimizarea performanței
 */
class MinificationHelper
{
    /**
     * Minifică conținutul CSS
     */
    public static function minifyCSS($css)
    {
        // Elimină comentariile
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Elimină spațiile și tab-urile inutile
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        
        // Elimină spațiile multiple
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Elimină spațiile în jurul caracterelor speciale
        $css = str_replace([' {', '{ ', ' }', '} ', ' :', ': ', ' ;', '; ', ' ,', ', '], 
                          ['{', '{', '}', '}', ':', ':', ';', ';', ',', ','], $css);
        
        // Elimină ultimul semicolon din fiecare bloc
        $css = preg_replace('/;}/', '}', $css);
        
        return trim($css);
    }
    
    /**
     * Minifică conținutul JavaScript
     */
    public static function minifyJS($js)
    {
        // Elimină comentariile single-line
        $js = preg_replace('/\/\/.*$/m', '', $js);
        
        // Elimină comentariile multi-line
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Elimină spațiile și tab-urile inutile
        $js = str_replace(["\r\n", "\r", "\n", "\t"], '', $js);
        
        // Elimină spațiile multiple
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Elimină spațiile în jurul operatorilor și punctuației
        $js = str_replace([' {', '{ ', ' }', '} ', ' (', '( ', ' )', ') ', ' ;', '; ', ' ,', ', ', ' =', '= '], 
                         ['{', '{', '}', '}', '(', '(', ')', ')', ';', ';', ',', ',', '=', '='], $js);
        
        return trim($js);
    }
    
    /**
     * Minifică un fișier CSS și salvează versiunea minificată
     */
    public static function minifyCSSFile($inputPath, $outputPath = null)
    {
        if (!file_exists($inputPath)) {
            return false;
        }
        
        if ($outputPath === null) {
            $outputPath = str_replace('.css', '.min.css', $inputPath);
        }
        
        $css = file_get_contents($inputPath);
        $minifiedCSS = self::minifyCSS($css);
        
        $result = file_put_contents($outputPath, $minifiedCSS);
        
        return $result !== false ? $outputPath : false;
    }
    
    /**
     * Minifică un fișier JavaScript și salvează versiunea minificată
     */
    public static function minifyJSFile($inputPath, $outputPath = null)
    {
        if (!file_exists($inputPath)) {
            return false;
        }
        
        if ($outputPath === null) {
            $outputPath = str_replace('.js', '.min.js', $inputPath);
        }
        
        $js = file_get_contents($inputPath);
        $minifiedJS = self::minifyJS($js);
        
        $result = file_put_contents($outputPath, $minifiedJS);
        
        return $result !== false ? $outputPath : false;
    }
    
    /**
     * Minifică toate fișierele CSS dintr-un director
     */
    public static function minifyAllCSS($directory)
    {
        $results = [];
        
        if (!is_dir($directory)) {
            return $results;
        }
        
        $files = glob($directory . '/*.css');
        
        foreach ($files as $file) {
            // Skip fișierele deja minificate
            if (strpos($file, '.min.css') !== false) {
                continue;
            }
            
            $minifiedPath = self::minifyCSSFile($file);
            if ($minifiedPath) {
                $results[] = [
                    'original' => $file,
                    'minified' => $minifiedPath,
                    'original_size' => filesize($file),
                    'minified_size' => filesize($minifiedPath),
                    'reduction' => round((1 - filesize($minifiedPath) / filesize($file)) * 100, 2)
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Minifică toate fișierele JavaScript dintr-un director
     */
    public static function minifyAllJS($directory)
    {
        $results = [];
        
        if (!is_dir($directory)) {
            return $results;
        }
        
        $files = glob($directory . '/*.js');
        
        foreach ($files as $file) {
            // Skip fișierele deja minificate
            if (strpos($file, '.min.js') !== false) {
                continue;
            }
            
            $minifiedPath = self::minifyJSFile($file);
            if ($minifiedPath) {
                $results[] = [
                    'original' => $file,
                    'minified' => $minifiedPath,
                    'original_size' => filesize($file),
                    'minified_size' => filesize($minifiedPath),
                    'reduction' => round((1 - filesize($minifiedPath) / filesize($file)) * 100, 2)
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Verifică dacă versiunea minificată există și este mai nouă decât originala
     */
    public static function isMinifiedUpToDate($originalPath, $minifiedPath)
    {
        if (!file_exists($originalPath) || !file_exists($minifiedPath)) {
            return false;
        }
        
        return filemtime($minifiedPath) >= filemtime($originalPath);
    }
    
    /**
     * Returnează calea către versiunea minificată a unui fișier
     */
    public static function getMinifiedPath($originalPath)
    {
        if (strpos($originalPath, '.css') !== false) {
            return str_replace('.css', '.min.css', $originalPath);
        } elseif (strpos($originalPath, '.js') !== false) {
            return str_replace('.js', '.min.js', $originalPath);
        }
        
        return $originalPath;
    }
    
    /**
     * Formatează dimensiunea fișierului pentru afișare
     */
    public static function formatFileSize($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
?>
