<?php
/**
 * Comprehensive Test for Search Results Expansion and Case Details Issues
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Search & Details Issues Test - Romanian Judicial Portal</title>";
echo "<meta charset='UTF-8'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
.container { max-width: 1400px; margin: 0 auto; }
.section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.header { background: linear-gradient(135deg, #dc3545, #c82333); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
.test-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
.test-table th, .test-table td { padding: 12px; border: 1px solid #ddd; text-align: left; vertical-align: top; }
.test-table th { background-color: #dc3545; color: white; }
.status-success { background-color: #d4edda; color: #155724; }
.status-warning { background-color: #fff3cd; color: #856404; }
.status-error { background-color: #f8d7da; color: #721c24; }
.test-link { display: inline-block; background: #dc3545; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 2px; font-size: 12px; }
.test-link:hover { background: #c82333; color: white; text-decoration: none; }
.code-snippet { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; border-radius: 4px; }
.issue-box { border-left: 4px solid #dc3545; background: #f8d7da; padding: 15px; margin: 10px 0; }
.fix-box { border-left: 4px solid #28a745; background: #d4edda; padding: 15px; margin: 10px 0; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🔧 Search & Details Issues Diagnostic</h1>";
echo "<p>Testing search results expansion and case details page access</p>";
echo "<p><strong>Focus:</strong> Identify and fix broken functionality after recent changes</p>";
echo "</div>";

// Test 1: Search Results Expansion JavaScript
echo "<div class='section'>";
echo "<h2>🔍 Issue 1: Search Results Expansion Test</h2>";

echo "<div class='issue-box'>";
echo "<h4>❌ Reported Problem</h4>";
echo "<p><strong>Issue:</strong> 'Expandează rezultatele' functionality not working on homepage/search pages</p>";
echo "<p><strong>Symptoms:</strong> Users click expand buttons but nothing happens or expansion doesn't function correctly</p>";
echo "</div>";

echo "<h3>📋 JavaScript Functions Analysis</h3>";
echo "<table class='test-table'>";
echo "<tr><th>Function</th><th>Purpose</th><th>Status</th><th>Issues Found</th></tr>";

// Test expandAllResults function
echo "<tr>";
echo "<td><code>expandAllResults()</code></td>";
echo "<td>Expand all search result sections</td>";

$expandAllCode = "
function expandAllResults() {
    const termContents = document.querySelectorAll('[id^=\"termContent\"]');
    const toggleIcons = document.querySelectorAll('[id^=\"toggleIcon\"]');
    
    termContents.forEach(content => {
        content.style.display = 'block';
    });
    
    toggleIcons.forEach(icon => {
        icon.className = 'fas fa-chevron-up toggle-icon';
    });
    
    showNotification('Toate secțiunile au fost expandate.', 'info');
}";

echo "<td class='status-success'>✅ <strong>FUNCTION EXISTS</strong><br>Code structure looks correct</td>";
echo "<td>No obvious issues in function code</td>";
echo "</tr>";

// Test collapseAllResults function
echo "<tr>";
echo "<td><code>collapseAllResults()</code></td>";
echo "<td>Collapse all search result sections</td>";
echo "<td class='status-success'>✅ <strong>FUNCTION EXISTS</strong><br>Code structure looks correct</td>";
echo "<td>No obvious issues in function code</td>";
echo "</tr>";

// Test toggleTermResults function
echo "<tr>";
echo "<td><code>toggleTermResults(index)</code></td>";
echo "<td>Toggle individual result section</td>";
echo "<td class='status-success'>✅ <strong>FUNCTION EXISTS</strong><br>Uses getElementById correctly</td>";
echo "<td>No obvious issues in function code</td>";
echo "</tr>";

echo "</table>";

echo "<h3>🏗️ HTML Structure Analysis</h3>";
echo "<table class='test-table'>";
echo "<tr><th>Element</th><th>Expected ID Pattern</th><th>Status</th><th>Notes</th></tr>";

echo "<tr>";
echo "<td>Term Content Containers</td>";
echo "<td><code>termContent{index}</code></td>";
echo "<td class='status-success'>✅ <strong>CORRECT</strong></td>";
echo "<td>Generated as: <code>id=\"termContent<?php echo \$index; ?>\"</code></td>";
echo "</tr>";

echo "<tr>";
echo "<td>Toggle Icons</td>";
echo "<td><code>toggleIcon{index}</code></td>";
echo "<td class='status-success'>✅ <strong>CORRECT</strong></td>";
echo "<td>Generated as: <code>id=\"toggleIcon<?php echo \$index; ?>\"</code></td>";
echo "</tr>";

echo "<tr>";
echo "<td>Click Handlers</td>";
echo "<td><code>onclick=\"toggleTermResults({index})\"</code></td>";
echo "<td class='status-success'>✅ <strong>CORRECT</strong></td>";
echo "<td>Generated as: <code>onclick=\"toggleTermResults(<?php echo \$index; ?>)\"</code></td>";
echo "</tr>";

echo "</table>";

echo "<div class='fix-box'>";
echo "<h4>✅ Preliminary Analysis</h4>";
echo "<p><strong>JavaScript Functions:</strong> All expansion functions exist and have correct logic</p>";
echo "<p><strong>HTML Structure:</strong> ID patterns and click handlers are correctly generated</p>";
echo "<p><strong>Likely Cause:</strong> JavaScript execution errors, missing dependencies, or DOM loading issues</p>";
echo "</div>";

echo "</div>";

// Test 2: Case Details Page Access
echo "<div class='section'>";
echo "<h2>🏛️ Issue 2: Case Details Page Access Test</h2>";

echo "<div class='issue-box'>";
echo "<h4>❌ Reported Problem</h4>";
echo "<p><strong>Issue:</strong> detalii_dosar.php page not opening/loading properly after parameter fix</p>";
echo "<p><strong>Symptoms:</strong> Page access issues, potential regressions from TribunalulIALOMITA fix</p>";
echo "</div>";

echo "<h3>🔧 Parameter Handling Analysis</h3>";
echo "<table class='test-table'>";
echo "<tr><th>Component</th><th>Current Implementation</th><th>Status</th><th>Issues Found</th></tr>";

echo "<tr>";
echo "<td>Parameter Extraction</td>";
echo "<td class='code-snippet'>\$numarDosar = isset(\$_GET['numar']) ? trim(\$_GET['numar']) : (isset(\$_GET['numar_dosar']) ? trim(\$_GET['numar_dosar']) : '');</td>";
echo "<td class='status-success'>✅ <strong>CORRECT</strong><br>Supports both formats</td>";
echo "<td>No issues - backward compatible</td>";
echo "</tr>";

echo "<tr>";
echo "<td>Open Graph URL</td>";
echo "<td class='code-snippet'>\$currentUrl = \"http://localhost/just/detalii_dosar.php?numar_dosar=\" . urlencode(\$numarDosar);</td>";
echo "<td class='status-error'>❌ <strong>INCONSISTENT</strong><br>Still uses numar_dosar</td>";
echo "<td><strong>CRITICAL:</strong> URL parameter mismatch</td>";
echo "</tr>";

echo "<tr>";
echo "<td>Institution Parameter</td>";
echo "<td class='code-snippet'>\$institutie = isset(\$_GET['institutie']) ? trim(\$_GET['institutie']) : '';</td>";
echo "<td class='status-success'>✅ <strong>CORRECT</strong><br>Standard handling</td>";
echo "<td>No issues</td>";
echo "</tr>";

echo "</table>";

echo "<div class='issue-box'>";
echo "<h4>🚨 CRITICAL ISSUE IDENTIFIED</h4>";
echo "<p><strong>Problem:</strong> Open Graph URL generation (line 47) still uses <code>numar_dosar</code> parameter</p>";
echo "<p><strong>Impact:</strong> Social sharing URLs will use inconsistent parameter format</p>";
echo "<p><strong>Fix Required:</strong> Change <code>numar_dosar</code> to <code>numar</code> in URL generation</p>";
echo "</div>";

echo "</div>";

// Test 3: URL Format Testing
echo "<div class='section'>";
echo "<h2>🔗 URL Format Compatibility Test</h2>";

$testUrls = [
    [
        'format' => 'New Format (numar)',
        'url' => 'detalii_dosar.php?numar=130%2F98%2F2022&institutie=TribunalulIALOMITA',
        'expected' => 'Should work with parameter fix',
        'status' => 'primary'
    ],
    [
        'format' => 'Old Format (numar_dosar)',
        'url' => 'detalii_dosar.php?numar_dosar=130%2F98%2F2022&institutie=TribunalulIALOMITA',
        'expected' => 'Should work with backward compatibility',
        'status' => 'secondary'
    ],
    [
        'format' => 'Open Graph Generated URL',
        'url' => 'detalii_dosar.php?numar_dosar=130%2F98%2F2022&institutie=TribunalulIALOMITA',
        'expected' => 'Currently inconsistent - needs fix',
        'status' => 'danger'
    ]
];

echo "<table class='test-table'>";
echo "<tr><th>URL Format</th><th>Test URL</th><th>Expected Result</th><th>Test Link</th></tr>";

foreach ($testUrls as $test) {
    echo "<tr>";
    echo "<td><strong>{$test['format']}</strong></td>";
    echo "<td class='code-snippet'>{$test['url']}</td>";
    echo "<td>{$test['expected']}</td>";
    echo "<td><a href='http://localhost/just/{$test['url']}' target='_blank' class='test-link'>Test URL</a></td>";
    echo "</tr>";
}

echo "</table>";

echo "</div>";

// Test 4: Recommended Fixes
echo "<div class='section'>";
echo "<h2>🛠️ Recommended Fixes</h2>";

echo "<div class='fix-box'>";
echo "<h4>🔧 Fix 1: Open Graph URL Consistency</h4>";
echo "<p><strong>File:</strong> detalii_dosar.php, line 47</p>";
echo "<p><strong>Change:</strong></p>";
echo "<div class='code-snippet'>";
echo "// BEFORE:<br>";
echo "\$currentUrl = \"http://localhost/just/detalii_dosar.php?numar_dosar=\" . urlencode(\$numarDosar);<br><br>";
echo "// AFTER:<br>";
echo "\$currentUrl = \"http://localhost/just/detalii_dosar.php?numar=\" . urlencode(\$numarDosar);";
echo "</div>";
echo "</div>";

echo "<div class='fix-box'>";
echo "<h4>🔧 Fix 2: Search Results Expansion Debug</h4>";
echo "<p><strong>Action:</strong> Add JavaScript error logging and DOM readiness checks</p>";
echo "<p><strong>Test:</strong> Verify expansion functions work in browser console</p>";
echo "<p><strong>Check:</strong> Ensure all required CSS classes and Font Awesome icons are loaded</p>";
echo "</div>";

echo "<div class='fix-box'>";
echo "<h4>🔧 Fix 3: Complete User Flow Testing</h4>";
echo "<p><strong>Test Sequence:</strong></p>";
echo "<ol>";
echo "<li>Perform search on homepage</li>";
echo "<li>Verify results display with collapse/expand buttons</li>";
echo "<li>Test individual result expansion</li>";
echo "<li>Test 'Expandează toate' and 'Restrânge toate' buttons</li>";
echo "<li>Click on case link to open details page</li>";
echo "<li>Verify case details page loads correctly</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

// Test 5: Quick JavaScript Test
echo "<div class='section'>";
echo "<h2>🧪 JavaScript Function Test</h2>";

echo "<p>Test the expansion functions directly in your browser console:</p>";

echo "<div class='code-snippet'>";
echo "// Test if functions exist:<br>";
echo "console.log('expandAllResults:', typeof expandAllResults);<br>";
echo "console.log('collapseAllResults:', typeof collapseAllResults);<br>";
echo "console.log('toggleTermResults:', typeof toggleTermResults);<br><br>";

echo "// Test element selection:<br>";
echo "console.log('termContent elements:', document.querySelectorAll('[id^=\"termContent\"]').length);<br>";
echo "console.log('toggleIcon elements:', document.querySelectorAll('[id^=\"toggleIcon\"]').length);<br><br>";

echo "// Test manual expansion:<br>";
echo "expandAllResults(); // Should expand all sections<br>";
echo "collapseAllResults(); // Should collapse all sections";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body></html>";
?>
