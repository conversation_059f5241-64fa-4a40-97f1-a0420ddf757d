<?php
// Debug parties display issue in detalii_dosar.php
require_once 'bootstrap.php';

use App\Services\DosarService;

echo "<h1>🔍 Debug Părți Implicate - Ana<PERSON><PERSON> Completă</h1>";

// Test cu dosarul "14096/3/2024*" - folosim numele corect al instituției
$numarDosar = "14096/3/2024*";
$institutie = "Tribunalul București";

try {
    $dosarService = new DosarService();
    
    echo "<h2>Step 1: Obținere detalii dosar</h2>";
    echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
    echo "<strong>Căutare pentru:</strong><br>";
    echo "Număr dosar: " . htmlspecialchars($numarDosar) . "<br>";
    echo "Instituție: " . htmlspecialchars($institutie) . "<br>";
    echo "</div>";
    
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar) {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
        echo "<strong>❌ Dosarul nu a fost găsit!</strong>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>Step 2: Analiza părților implicate</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
    echo "<strong>Informații generale despre dosar:</strong><br>";
    echo "Număr: " . htmlspecialchars($dosar->numar ?? 'N/A') . "<br>";
    echo "Instituție: " . htmlspecialchars($dosar->institutie ?? 'N/A') . "<br>";
    echo "Obiect: " . htmlspecialchars(substr($dosar->obiect ?? 'N/A', 0, 100)) . "...<br>";
    echo "Data: " . htmlspecialchars($dosar->data ?? 'N/A') . "<br>";
    echo "</div>";
    
    // Analizează părțile
    if (isset($dosar->parti) && is_array($dosar->parti)) {
        $totalParti = count($dosar->parti);
        
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
        echo "<strong>✅ Părți găsite în backend: {$totalParti}</strong><br>";
        echo "</div>";
        
        echo "<h3>Analiza detaliată a părților:</h3>";
        
        // Grupează părțile pe surse
        $partiPeSurse = [
            'soap_api' => [],
            'decision_text' => [],
            'unknown' => []
        ];
        
        foreach ($dosar->parti as $index => $parte) {
            $source = $parte->source ?? 'unknown';
            $partiPeSurse[$source][] = [
                'index' => $index,
                'nume' => $parte->nume ?? '',
                'calitate' => $parte->calitate ?? '',
                'source' => $source
            ];
        }
        
        echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
        echo "<strong>Distribuția pe surse:</strong><br>";
        echo "SOAP API: " . count($partiPeSurse['soap_api']) . " părți<br>";
        echo "Decision Text: " . count($partiPeSurse['decision_text']) . " părți<br>";
        echo "Unknown: " . count($partiPeSurse['unknown']) . " părți<br>";
        echo "</div>";
        
        // Afișează toate părțile
        echo "<h3>Lista completă a părților:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<thead>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th>Index</th><th>Nume</th><th>Calitate</th><th>Sursă</th><th>Lungime Nume</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($dosar->parti as $index => $parte) {
            $nume = $parte->nume ?? '';
            $calitate = $parte->calitate ?? '';
            $source = $parte->source ?? 'unknown';
            $lungimeNume = strlen($nume);
            
            $rowStyle = '';
            if ($source === 'soap_api') {
                $rowStyle = 'background: #e7f3ff;';
            } elseif ($source === 'decision_text') {
                $rowStyle = 'background: #f8f9fa;';
            } else {
                $rowStyle = 'background: #fff3cd;';
            }
            
            echo "<tr style='$rowStyle'>";
            echo "<td>" . ($index + 1) . "</td>";
            echo "<td>" . htmlspecialchars($nume) . "</td>";
            echo "<td>" . htmlspecialchars($calitate) . "</td>";
            echo "<td>" . htmlspecialchars($source) . "</td>";
            echo "<td>{$lungimeNume}</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        
        // Verifică pentru probleme potențiale
        echo "<h3>Verificări pentru probleme potențiale:</h3>";
        
        $probleme = [];
        
        // Verifică pentru nume goale
        $numeGoale = 0;
        foreach ($dosar->parti as $parte) {
            if (empty(trim($parte->nume ?? ''))) {
                $numeGoale++;
            }
        }
        
        if ($numeGoale > 0) {
            $probleme[] = "Găsite {$numeGoale} părți cu nume goale";
        }
        
        // Verifică pentru duplicate
        $numeVazute = [];
        $duplicate = 0;
        foreach ($dosar->parti as $parte) {
            $nume = trim($parte->nume ?? '');
            if (!empty($nume)) {
                $numeNormalizat = strtolower($nume);
                if (isset($numeVazute[$numeNormalizat])) {
                    $duplicate++;
                } else {
                    $numeVazute[$numeNormalizat] = true;
                }
            }
        }
        
        if ($duplicate > 0) {
            $probleme[] = "Găsite {$duplicate} părți duplicate";
        }
        
        // Verifică pentru nume foarte lungi
        $numeLungi = 0;
        foreach ($dosar->parti as $parte) {
            if (strlen($parte->nume ?? '') > 100) {
                $numeLungi++;
            }
        }
        
        if ($numeLungi > 0) {
            $probleme[] = "Găsite {$numeLungi} părți cu nume foarte lungi (>100 caractere)";
        }
        
        if (empty($probleme)) {
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
            echo "<strong>✅ Nu au fost găsite probleme în datele părților</strong>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
            echo "<strong>⚠️ Probleme găsite:</strong><br>";
            foreach ($probleme as $problema) {
                echo "- " . htmlspecialchars($problema) . "<br>";
            }
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
        echo "<strong>❌ Nu au fost găsite părți sau părțile nu sunt în format array</strong><br>";
        echo "Tip părți: " . gettype($dosar->parti ?? null) . "<br>";
        if (isset($dosar->parti)) {
            echo "Conținut părți: " . print_r($dosar->parti, true) . "<br>";
        }
        echo "</div>";
    }
    
    echo "<h2>Step 3: Simulare afișare frontend</h2>";
    
    if (isset($dosar->parti) && is_array($dosar->parti)) {
        echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
        echo "<strong>Simulare bucla foreach din detalii_dosar.php:</strong><br>";
        
        $loop_index = 0;
        $totalPartiCount = count($dosar->parti);
        
        echo "Total părți de procesat: {$totalPartiCount}<br>";
        echo "Începere buclă foreach...<br><br>";
        
        foreach ($dosar->parti as $parteIndex => $parte) {
            $loop_index++;
            
            echo "<div style='background: #e7f3ff; padding: 5px; margin: 2px 0; border: 1px solid #007bff;'>";
            echo "<strong>Iterația {$loop_index}/{$totalPartiCount}:</strong><br>";
            echo "Index: {$parteIndex}<br>";
            echo "Nume: '" . htmlspecialchars($parte->nume ?? '') . "'<br>";
            echo "Calitate: '" . htmlspecialchars($parte->calitate ?? '') . "'<br>";
            echo "Sursă: '" . htmlspecialchars($parte->source ?? '') . "'<br>";
            
            // Verifică dacă ar fi afișată în HTML
            $numeValid = !empty(trim($parte->nume ?? ''));
            echo "Ar fi afișată: " . ($numeValid ? "DA" : "NU") . "<br>";
            
            if (!$numeValid) {
                echo "<strong style='color: red;'>⚠️ Această parte NU ar fi afișată din cauza numelui gol!</strong><br>";
            }
            
            echo "</div>";
        }
        
        echo "<br><strong>Bucla finalizată. Părți procesate: {$loop_index}</strong><br>";
        echo "</div>";
    }
    
    echo "<h2>Step 4: Testare cu alte dosare</h2>";
    
    // Testează cu alte dosare pentru comparație
    $alteDosare = [
        ["14096/3/2024", "Tribunalul București"],
        ["14096/3/2024", "Curtea de Apel București"]
    ];
    
    foreach ($alteDosare as $dosarTest) {
        echo "<h3>Test cu dosarul: {$dosarTest[0]} - {$dosarTest[1]}</h3>";
        
        try {
            $dosarTest = $dosarService->getDetaliiDosar($dosarTest[0], $dosarTest[1]);
            
            if ($dosarTest && isset($dosarTest->parti)) {
                $partiCount = is_array($dosarTest->parti) ? count($dosarTest->parti) : 0;
                echo "<div style='background: #d4edda; padding: 8px; margin: 3px 0; border: 1px solid #c3e6cb;'>";
                echo "Părți găsite: {$partiCount}";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 8px; margin: 3px 0; border: 1px solid #f5c6cb;'>";
                echo "Nu au fost găsite părți";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 8px; margin: 3px 0; border: 1px solid #f5c6cb;'>";
            echo "Eroare: " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Eroare:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🎯 Concluzie</h2>";
echo "<p>Acest test verifică:</p>";
echo "<ol>";
echo "<li>Dacă backend-ul returnează toate părțile corect</li>";
echo "<li>Dacă există probleme în datele părților (nume goale, duplicate, etc.)</li>";
echo "<li>Dacă bucla foreach din frontend ar procesa toate părțile</li>";
echo "<li>Comparația cu alte dosare similare</li>";
echo "</ol>";

echo "<p><strong>Pentru a testa interfața web:</strong></p>";
echo "<ol>";
echo "<li>Deschide <a href='detalii_dosar.php?numar=" . urlencode($numarDosar) . "&institutie=" . urlencode($institutie) . "&debug=1' target='_blank'>detalii_dosar.php cu debug</a></li>";
echo "<li>Verifică secțiunea 'Părți implicate'</li>";
echo "<li>Compară numărul afișat cu cel din acest test</li>";
echo "<li>Verifică consola browser pentru mesaje de debug</li>";
echo "</ol>";
?>
