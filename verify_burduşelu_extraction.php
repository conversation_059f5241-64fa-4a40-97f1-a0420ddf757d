<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 VERIFYING EXTRACTION OF: Burduşelu Tudoriţa\n";
echo "===============================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Total parties: " . count($dosar->parti) . "\n\n";
    
    // Search for "Burduşelu Tudoriţa" in the party list
    $found = false;
    $foundParties = [];
    
    foreach ($dosar->parti as $party) {
        $partyName = is_array($party) ? $party['nume'] : $party->nume;
        $partyQuality = is_array($party) ? ($party['calitate'] ?? 'Unknown') : ($party->calitate ?? 'Unknown');
        $partySource = is_array($party) ? ($party['source'] ?? 'soap_api') : ($party->source ?? 'soap_api');
        
        // Check for exact match
        if ($partyName === 'Burduşelu Tudoriţa') {
            $found = true;
            $foundParties[] = [
                'nume' => $partyName,
                'calitate' => $partyQuality,
                'source' => $partySource,
                'match_type' => 'EXACT'
            ];
        }
        // Check for partial matches
        elseif (stripos($partyName, 'Burduşelu') !== false || stripos($partyName, 'Tudoriţa') !== false) {
            $foundParties[] = [
                'nume' => $partyName,
                'calitate' => $partyQuality,
                'source' => $partySource,
                'match_type' => 'PARTIAL'
            ];
        }
    }
    
    if ($found) {
        echo "✅ SUCCESS! 'Burduşelu Tudoriţa' found in party list!\n\n";
    } else {
        echo "❌ 'Burduşelu Tudoriţa' NOT found in party list\n\n";
    }
    
    if (!empty($foundParties)) {
        echo "🔍 RELATED PARTIES FOUND:\n";
        echo "=========================\n";
        foreach ($foundParties as $party) {
            echo "Name: '{$party['nume']}'\n";
            echo "Quality: {$party['calitate']}\n";
            echo "Source: {$party['source']}\n";
            echo "Match: {$party['match_type']}\n";
            echo "---\n";
        }
    } else {
        echo "❌ No related parties found\n";
    }
    
    // Also check for other names with similar patterns
    echo "\n🔍 CHECKING FOR OTHER NAMES WITH 'ş' OR 'ţ':\n";
    echo "=============================================\n";
    
    $romanianCharCount = 0;
    foreach ($dosar->parti as $party) {
        $partyName = is_array($party) ? $party['nume'] : $party->nume;
        $partySource = is_array($party) ? ($party['source'] ?? 'soap_api') : ($party->source ?? 'soap_api');
        
        if (strpos($partyName, 'ş') !== false || strpos($partyName, 'ţ') !== false || 
            strpos($partyName, 'Ş') !== false || strpos($partyName, 'Ţ') !== false) {
            $romanianCharCount++;
            if ($romanianCharCount <= 10) { // Show first 10 examples
                echo "'{$partyName}' (Source: {$partySource})\n";
            }
        }
    }
    
    echo "\nTotal parties with old Romanian characters (ş, ţ): {$romanianCharCount}\n";
    
    echo "\n✅ Verification complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
