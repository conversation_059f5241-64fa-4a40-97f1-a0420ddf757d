<?php

namespace App\Services;

use App\Config\Database;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use Exception;

/**
 * Administrative Authentication Service
 * 
 * Handles admin user authentication, role management, and access control
 * for the Portal Judiciar România administrative interface.
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */
class AdminAuthService
{
    // Admin roles
    public const ROLE_SUPER_ADMIN = 'super_admin';
    public const ROLE_ADMIN = 'admin';
    public const ROLE_MODERATOR = 'moderator';
    public const ROLE_VIEWER = 'viewer';
    
    // Admin permissions
    public const PERMISSIONS = [
        self::ROLE_SUPER_ADMIN => [
            'user_management', 'system_settings', 'security_logs', 
            'gdpr_management', 'database_access', 'backup_restore'
        ],
        self::ROLE_ADMIN => [
            'user_management', 'security_logs', 'gdpr_management', 'system_monitoring'
        ],
        self::ROLE_MODERATOR => [
            'user_monitoring', 'case_management', 'notification_management'
        ],
        self::ROLE_VIEWER => [
            'view_statistics', 'view_logs'
        ]
    ];
    
    /**
     * Check if user is admin
     */
    public static function isAdmin($userId): bool
    {
        try {
            $user = Database::fetchOne(
                "SELECT admin_role FROM users WHERE id = ? AND admin_role IS NOT NULL",
                [$userId]
            );
            
            return !empty($user);
        } catch (Exception $e) {
            error_log("Admin check failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get admin role for user
     */
    public static function getAdminRole($userId): ?string
    {
        try {
            $user = Database::fetchOne(
                "SELECT admin_role FROM users WHERE id = ?",
                [$userId]
            );
            
            return $user['admin_role'] ?? null;
        } catch (Exception $e) {
            error_log("Get admin role failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Check if user has specific permission
     */
    public static function hasPermission($userId, string $permission): bool
    {
        $role = self::getAdminRole($userId);
        
        if (!$role) {
            return false;
        }
        
        $permissions = self::PERMISSIONS[$role] ?? [];
        return in_array($permission, $permissions);
    }
    
    /**
     * Require admin access (redirect if not admin)
     */
    public static function requireAdmin($requiredPermission = null): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $userId = $_SESSION['user_id'] ?? null;
        
        if (!$userId || !self::isAdmin($userId)) {
            // Get current script directory to determine correct path
            $currentDir = dirname($_SERVER['SCRIPT_NAME']);
            if (strpos($currentDir, '/admin') !== false) {
                header('Location: login.php');
            } else {
                header('Location: admin/login.php');
            }
            exit;
        }
        
        if ($requiredPermission && !self::hasPermission($userId, $requiredPermission)) {
            http_response_code(403);
            header('Location: /admin/?error=insufficient_permissions');
            exit;
        }
    }
    
    /**
     * Get admin dashboard statistics
     */
    public static function getDashboardStats(): array
    {
        try {
            // User statistics
            $userStats = Database::fetchOne("
                SELECT 
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN email_verified = 1 THEN 1 END) as verified_users,
                    COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users,
                    COUNT(CASE WHEN admin_role IS NOT NULL THEN 1 END) as admin_users
                FROM users 
                WHERE deleted_at IS NULL
            ");
            
            // Case monitoring statistics
            $caseStats = Database::fetchOne("
                SELECT 
                    COUNT(*) as total_monitored_cases,
                    COUNT(DISTINCT user_id) as users_with_cases,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_cases,
                    AVG(TIMESTAMPDIFF(DAY, created_at, NOW())) as avg_monitoring_days
                FROM monitored_cases
            ");
            
            // Notification statistics
            $notificationStats = Database::fetchOne("
                SELECT 
                    COUNT(*) as total_notifications,
                    COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_notifications,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_notifications,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as notifications_24h
                FROM notification_queue
            ");
            
            // Security statistics
            $securityStats = Database::fetchOne("
                SELECT 
                    COUNT(*) as total_security_incidents,
                    COUNT(CASE WHEN severity = 'high' OR severity = 'critical' THEN 1 END) as high_severity_incidents,
                    COUNT(CASE WHEN resolved = 0 THEN 1 END) as unresolved_incidents,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as incidents_24h
                FROM security_incidents
            ");
            
            // GDPR compliance statistics
            $gdprStats = Database::fetchOne("
                SELECT 
                    COUNT(DISTINCT user_id) as users_with_consent,
                    COUNT(CASE WHEN consent_type = 'data_processing' AND granted = 1 THEN 1 END) as data_processing_consents,
                    COUNT(CASE WHEN consent_type = 'monitoring' AND granted = 1 THEN 1 END) as monitoring_consents
                FROM user_consents
            ");
            
            return [
                'users' => $userStats ?: [],
                'cases' => $caseStats ?: [],
                'notifications' => $notificationStats ?: [],
                'security' => $securityStats ?: [],
                'gdpr' => $gdprStats ?: []
            ];
            
        } catch (Exception $e) {
            error_log("Failed to get dashboard stats: " . $e->getMessage());
            return [
                'users' => [],
                'cases' => [],
                'notifications' => [],
                'security' => [],
                'gdpr' => []
            ];
        }
    }
    
    /**
     * Get recent admin activity
     */
    public static function getRecentActivity($limit = 20): array
    {
        try {
            return Database::fetchAll("
                SELECT 
                    dpl.action,
                    dpl.context,
                    dpl.created_at,
                    dpl.ip_address,
                    u.first_name,
                    u.last_name,
                    u.email
                FROM data_processing_logs dpl
                LEFT JOIN users u ON u.id = dpl.user_id
                WHERE dpl.action IN ('admin_login', 'user_created', 'user_deleted', 'security_incident', 'gdpr_request')
                ORDER BY dpl.created_at DESC
                LIMIT ?
            ", [$limit]);
            
        } catch (Exception $e) {
            error_log("Failed to get recent activity: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Log admin action
     */
    public static function logAdminAction($userId, string $action, array $context = []): void
    {
        try {
            Database::insert('data_processing_logs', [
                'user_id' => $userId,
                'action' => $action,
                'context' => json_encode($context, JSON_UNESCAPED_UNICODE),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Failed to log admin action: " . $e->getMessage());
        }
    }
    
    /**
     * Get user details for admin interface
     */
    public static function getUserDetails($userId): array
    {
        try {
            $user = Database::fetchOne("
                SELECT
                    u.*,
                    COUNT(mc.id) as monitored_cases_count,
                    COUNT(nq.id) as notifications_sent,
                    MAX(mc.last_checked) as last_case_check
                FROM users u
                LEFT JOIN monitored_cases mc ON mc.user_id = u.id
                LEFT JOIN notification_queue nq ON nq.user_id = u.id AND nq.status = 'sent'
                WHERE u.id = ?
                GROUP BY u.id
            ", [$userId]);

            if (!$user) {
                throw new Exception("User not found");
            }

            // Get user consents
            $consents = Database::fetchAll("
                SELECT consent_type, granted, created_at
                FROM user_consents
                WHERE user_id = ?
                ORDER BY created_at DESC
            ", [$userId]);

            $user['consents'] = $consents;
            return $user;

        } catch (Exception $e) {
            error_log("Failed to get user details: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update user status
     */
    public static function updateUserStatus($userId, string $status): bool
    {
        try {
            $validStatuses = ['active', 'inactive', 'suspended'];
            if (!in_array($status, $validStatuses)) {
                throw new Exception("Invalid status: $status");
            }

            $updateData = [];

            if ($status === 'suspended') {
                $updateData['locked_until'] = date('Y-m-d H:i:s', strtotime('+30 days'));
            } else {
                $updateData['locked_until'] = null;
            }

            if ($status === 'inactive') {
                $updateData['deleted_at'] = date('Y-m-d H:i:s');
            } else {
                $updateData['deleted_at'] = null;
            }

            $result = Database::update('users', $updateData, ['id' => $userId]);
            return $result > 0;

        } catch (Exception $e) {
            error_log("Failed to update user status: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Resolve security incident
     */
    public static function resolveSecurityIncident($incidentId, $resolvedBy): bool
    {
        try {
            $result = Database::update('security_incidents', [
                'resolved' => 1,
                'resolved_at' => date('Y-m-d H:i:s'),
                'resolved_by' => $resolvedBy
            ], ['id' => $incidentId]);

            if ($result > 0) {
                self::logAdminAction($resolvedBy, 'security_incident_resolved', [
                    'incident_id' => $incidentId
                ]);
                return true;
            }

            return false;

        } catch (Exception $e) {
            error_log("Failed to resolve security incident: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create admin user
     */
    public static function createAdminUser(string $email, string $password, string $role, array $userData = []): bool
    {
        try {
            // Check if role is valid
            if (!array_key_exists($role, self::PERMISSIONS)) {
                throw new Exception("Invalid admin role: $role");
            }

            // Hash password
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);

            // Insert user
            $userId = Database::insert('users', [
                'email' => $email,
                'password_hash' => $passwordHash,
                'first_name' => $userData['first_name'] ?? 'Admin',
                'last_name' => $userData['last_name'] ?? 'User',
                'email_verified' => 1,
                'admin_role' => $role,
                'data_processing_consent' => 1,
                'gdpr_consent_date' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            if ($userId) {
                self::logAdminAction($_SESSION['user_id'] ?? null, 'admin_user_created', [
                    'new_admin_id' => $userId,
                    'role' => $role,
                    'email' => $email
                ]);
                return true;
            }

            return false;

        } catch (Exception $e) {
            error_log("Failed to create admin user: " . $e->getMessage());
            return false;
        }
    }
}
