<?php
/**
 * Test Parameter Fix
 * Tests if the parameter fix resolves the loading issue
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Test Parameter Fix - Romanian Judicial Portal</title>";
echo "<meta charset='UTF-8'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
.container { max-width: 1200px; margin: 0 auto; }
.section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.header { background: linear-gradient(135deg, #28a745, #20c997); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
.metric-card { background: #f8f9fa; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0; }
.timing-info { background: #d4edda; border: 1px solid #28a745; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap; border-radius: 4px; }
.success { background: #d4edda; border-left: 4px solid #28a745; }
.warning { background: #fff3cd; border-left: 4px solid #ffc107; }
.critical { background: #f8d7da; border-left: 4px solid #dc3545; }
.test-link { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
.test-link:hover { background: #0056b3; color: white; text-decoration: none; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🔧 Test Parameter Fix</h1>";
echo "<p>Testing if the parameter fix resolves the TribunalulIALOMITA loading issue</p>";
echo "</div>";

// Simulate the parameter handling from detalii_dosar.php
echo "<div class='section'>";
echo "<h2>📋 Parameter Handling Test</h2>";

// Test both URL formats
$testUrls = [
    'http://localhost/just/detalii_dosar.php?numar=130%2F98%2F2022&institutie=TribunalulIALOMITA',
    'http://localhost/just/detalii_dosar.php?numar_dosar=130%2F98%2F2022&institutie=TribunalulIALOMITA'
];

foreach ($testUrls as $index => $testUrl) {
    echo "<div class='metric-card'>";
    echo "<h4>🔗 Test URL " . ($index + 1) . "</h4>";
    echo "<p><strong>URL:</strong> <code>$testUrl</code></p>";
    
    // Parse URL to simulate $_GET parameters
    $urlParts = parse_url($testUrl);
    parse_str($urlParts['query'], $queryParams);
    
    // Simulate the fixed parameter handling logic
    $numarDosar = isset($queryParams['numar']) ? trim($queryParams['numar']) : (isset($queryParams['numar_dosar']) ? trim($queryParams['numar_dosar']) : '');
    $institutie = isset($queryParams['institutie']) ? trim($queryParams['institutie']) : '';
    
    echo "<div class='timing-info'>";
    echo "Simulated \$_GET parameters:\n";
    foreach ($queryParams as $key => $value) {
        echo "$key: " . urldecode($value) . "\n";
    }
    echo "\nParameter extraction result:\n";
    echo "\$numarDosar: '$numarDosar'\n";
    echo "\$institutie: '$institutie'\n";
    echo "Parameters valid: " . (!empty($numarDosar) && !empty($institutie) ? "✅ YES" : "❌ NO") . "\n";
    echo "</div>";
    
    // Test actual data loading
    if (!empty($numarDosar) && !empty($institutie)) {
        echo "<div class='timing-info'>";
        echo "Testing data loading...\n";
        
        try {
            $startTime = microtime(true);
            $dosarService = new DosarService();
            $dosare = $dosarService->cautareDupaNumarDosar($numarDosar, $institutie, '', '', '');
            $endTime = microtime(true);
            
            $loadTime = ($endTime - $startTime) * 1000;
            
            if (!empty($dosare)) {
                $dosar = $dosare[0];
                echo "Data loading: ✅ SUCCESS\n";
                echo "Load time: " . round($loadTime, 2) . " ms\n";
                echo "Case number: " . ($dosar->numarDosar ?? 'N/A') . "\n";
                echo "Parties count: " . count($dosar->parti ?? []) . "\n";
                echo "Will show .dosar-header: ✅ YES\n";
                echo "Loading overlay will hide: ✅ YES\n";
            } else {
                echo "Data loading: ❌ NO DATA\n";
                echo "Load time: " . round($loadTime, 2) . " ms\n";
                echo "Will show .dosar-header: ❌ NO\n";
                echo "Loading overlay will hide: ❌ NO (stuck)\n";
            }
        } catch (Exception $e) {
            echo "Data loading: ❌ ERROR\n";
            echo "Error: " . $e->getMessage() . "\n";
            echo "Will show .dosar-header: ❌ NO\n";
            echo "Loading overlay will hide: ❌ NO (stuck)\n";
        }
        echo "</div>";
    }
    
    echo "<p><a href='$testUrl' target='_blank' class='test-link'>Test This URL</a></p>";
    echo "</div>";
}

echo "</div>";

// Test the actual page loading logic
echo "<div class='section'>";
echo "<h2>🎯 Actual Page Loading Simulation</h2>";

echo "<div class='metric-card success'>";
echo "<h4>📄 Simulating detalii_dosar.php Logic</h4>";

// Simulate the exact logic from detalii_dosar.php
$_GET['numar'] = '130/98/2022';
$_GET['institutie'] = 'TribunalulIALOMITA';

// Use the fixed parameter handling
$numarDosar = isset($_GET['numar']) ? trim($_GET['numar']) : (isset($_GET['numar_dosar']) ? trim($_GET['numar_dosar']) : '');
$institutie = isset($_GET['institutie']) ? trim($_GET['institutie']) : '';

echo "<div class='timing-info'>";
echo "Simulated \$_GET array:\n";
echo "numar: '{$_GET['numar']}'\n";
echo "institutie: '{$_GET['institutie']}'\n";
echo "\nExtracted variables:\n";
echo "\$numarDosar: '$numarDosar'\n";
echo "\$institutie: '$institutie'\n";

$dosar = null;
$error = null;

if (empty($numarDosar) || empty($institutie)) {
    $error = "Parametrii necesari pentru afișarea detaliilor dosarului lipsesc.";
    echo "\nValidation result: ❌ FAILED\n";
    echo "Error: $error\n";
} else {
    echo "\nValidation result: ✅ PASSED\n";
    
    try {
        $dosarService = new DosarService();
        $startTime = microtime(true);
        $dosare = $dosarService->cautareDupaNumarDosar($numarDosar, $institutie, '', '', '');
        $endTime = microtime(true);
        
        $loadTime = ($endTime - $startTime) * 1000;
        
        if (!empty($dosare)) {
            $dosar = $dosare[0];
            echo "SOAP API call: ✅ SUCCESS\n";
            echo "Load time: " . round($loadTime, 2) . " ms\n";
            echo "Case found: ✅ YES\n";
            echo "Parties: " . count($dosar->parti ?? []) . "\n";
            echo "\nPage rendering:\n";
            echo "Will render .dosar-header: ✅ YES\n";
            echo "JavaScript will find hasContent: ✅ YES\n";
            echo "Loading overlay will hide: ✅ YES\n";
            echo "Page will load successfully: ✅ YES\n";
        } else {
            echo "SOAP API call: ✅ SUCCESS (no data)\n";
            echo "Load time: " . round($loadTime, 2) . " ms\n";
            echo "Case found: ❌ NO\n";
            echo "\nPage rendering:\n";
            echo "Will render .dosar-header: ❌ NO\n";
            echo "JavaScript will find hasContent: ❌ NO\n";
            echo "Loading overlay will hide: ❌ NO (timeout after 2s)\n";
            echo "Page will show: 'Nu s-au găsit dosare'\n";
        }
    } catch (Exception $e) {
        $error = "Eroare la obținerea detaliilor dosarului: " . $e->getMessage();
        echo "SOAP API call: ❌ ERROR\n";
        echo "Error: $error\n";
        echo "\nPage rendering:\n";
        echo "Will render .alert-danger: ✅ YES\n";
        echo "JavaScript will find hasError: ✅ YES\n";
        echo "Loading overlay will hide: ✅ YES\n";
        echo "Page will show error message: ✅ YES\n";
    }
}

echo "</div>";
echo "</div>";

echo "</div>";

// Recommendations
echo "<div class='section'>";
echo "<h2>🔧 Fix Recommendations</h2>";

echo "<div class='metric-card success'>";
echo "<h4>✅ Parameter Fix Applied</h4>";
echo "<p>The parameter handling has been fixed to support both 'numar' and 'numar_dosar' parameters.</p>";
echo "<p><strong>Change made:</strong> Line 17 in detalii_dosar.php now uses:</p>";
echo "<code>\$numarDosar = isset(\$_GET['numar']) ? trim(\$_GET['numar']) : (isset(\$_GET['numar_dosar']) ? trim(\$_GET['numar_dosar']) : '');</code>";
echo "</div>";

echo "<div class='metric-card warning'>";
echo "<h4>🔍 Additional Debugging Needed</h4>";
echo "<p>If the issue persists after the parameter fix, check:</p>";
echo "<ul>";
echo "<li>Browser console for JavaScript errors</li>";
echo "<li>Network tab to see if AJAX requests are completing</li>";
echo "<li>PHP error logs for any server-side issues</li>";
echo "<li>Whether the case actually exists in TribunalulIALOMITA</li>";
echo "</ul>";
echo "</div>";

echo "<div class='metric-card'>";
echo "<h4>🧪 Test Links</h4>";
echo "<p>Test the fixed URLs:</p>";
echo "<a href='http://localhost/just/detalii_dosar.php?numar=130%2F98%2F2022&institutie=TribunalulIALOMITA' target='_blank' class='test-link'>Test Problem Case</a>";
echo "<a href='http://localhost/just/detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI' target='_blank' class='test-link'>Test Working Case</a>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body></html>";
?>
