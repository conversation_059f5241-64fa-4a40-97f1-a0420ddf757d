<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Web Interface Search</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border: 1px solid #dee2e6; border-radius: 5px; }
        .result { background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff; border-radius: 3px; }
        .error { background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb; border-radius: 3px; color: #721c24; }
        .success { background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb; border-radius: 3px; color: #155724; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug Web Interface Search for "14096/3/2024*"</h1>
    
    <div class="debug-section">
        <h2>Step 1: Test Form Submission</h2>
        <button onclick="testFormSubmission()">Submit Search Form</button>
        <div id="formResult"></div>
    </div>
    
    <div class="debug-section">
        <h2>Step 2: Check Result Display</h2>
        <button onclick="checkResultDisplay()">Check Current Results</button>
        <div id="displayResult"></div>
    </div>
    
    <div class="debug-section">
        <h2>Step 3: Analyze Table Rows</h2>
        <button onclick="analyzeTableRows()">Analyze Table Rows</button>
        <div id="tableAnalysis"></div>
    </div>
    
    <div class="debug-section">
        <h2>Step 4: Check JavaScript Filtering</h2>
        <button onclick="checkJavaScriptFiltering()">Check JS Filtering</button>
        <div id="jsFilteringResult"></div>
    </div>
    
    <div class="debug-section">
        <h2>Step 5: Manual Search Test</h2>
        <form id="testForm" action="index.php" method="POST" target="_blank">
            <input type="hidden" name="bulkSearchTerms" value="14096/3/2024*">
            <button type="submit">Open Search in New Tab</button>
        </form>
    </div>

    <script>
        function testFormSubmission() {
            const resultDiv = document.getElementById('formResult');
            resultDiv.innerHTML = '<p>Testing form submission...</p>';
            
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'index.php';
            form.style.display = 'none';
            
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'bulkSearchTerms';
            input.value = '14096/3/2024*';
            
            form.appendChild(input);
            document.body.appendChild(form);
            
            // Submit and then check the result
            form.submit();
            
            resultDiv.innerHTML = '<div class="success">Form submitted! Check the main page for results.</div>';
        }
        
        function checkResultDisplay() {
            const resultDiv = document.getElementById('displayResult');
            
            // Check if we're on the results page
            const resultMessages = document.querySelectorAll('[id^="resultMessage"]');
            const tableRows = document.querySelectorAll('table tbody tr');
            const resultCards = document.querySelectorAll('.result-card');
            
            let html = '<h3>Current Page Analysis:</h3>';
            html += `<p>Result messages found: ${resultMessages.length}</p>`;
            html += `<p>Table rows found: ${tableRows.length}</p>`;
            html += `<p>Result cards found: ${resultCards.length}</p>`;
            
            if (resultMessages.length > 0) {
                html += '<h4>Result Messages:</h4>';
                resultMessages.forEach((msg, index) => {
                    const term = msg.getAttribute('data-term');
                    const count = msg.getAttribute('data-original-count');
                    html += `<div class="result">Message ${index + 1}: "${msg.textContent}" (term: ${term}, original count: ${count})</div>`;
                });
            }
            
            if (tableRows.length > 0) {
                html += '<h4>Table Rows with Case Numbers:</h4>';
                let asteriskRows = 0;
                tableRows.forEach((row, index) => {
                    const caseNumber = row.getAttribute('data-numar');
                    if (caseNumber && caseNumber.includes('*')) {
                        asteriskRows++;
                        const isVisible = window.getComputedStyle(row).display !== 'none';
                        html += `<div class="result">Row ${index + 1}: ${caseNumber} (visible: ${isVisible})</div>`;
                    }
                });
                html += `<p><strong>Rows with asterisks: ${asteriskRows}</strong></p>`;
            }
            
            resultDiv.innerHTML = html;
        }
        
        function analyzeTableRows() {
            const resultDiv = document.getElementById('tableAnalysis');
            
            // Look for all table rows
            const allRows = document.querySelectorAll('table tbody tr');
            let analysis = '<h3>Detailed Table Row Analysis:</h3>';
            
            let totalRows = 0;
            let visibleRows = 0;
            let asteriskRows = 0;
            let visibleAsteriskRows = 0;
            
            allRows.forEach((row, index) => {
                totalRows++;
                const isVisible = window.getComputedStyle(row).display !== 'none';
                if (isVisible) visibleRows++;
                
                const caseNumber = row.getAttribute('data-numar');
                if (caseNumber && caseNumber.includes('*')) {
                    asteriskRows++;
                    if (isVisible) visibleAsteriskRows++;
                    
                    analysis += `<div class="result">`;
                    analysis += `<strong>Asterisk Row ${asteriskRows}:</strong><br>`;
                    analysis += `Case Number: ${caseNumber}<br>`;
                    analysis += `Visible: ${isVisible}<br>`;
                    analysis += `Display Style: ${row.style.display}<br>`;
                    analysis += `Classes: ${row.className}<br>`;
                    analysis += `Search Type: ${row.getAttribute('data-search-type')}<br>`;
                    analysis += `</div>`;
                }
            });
            
            analysis += `<div class="success">`;
            analysis += `<strong>Summary:</strong><br>`;
            analysis += `Total rows: ${totalRows}<br>`;
            analysis += `Visible rows: ${visibleRows}<br>`;
            analysis += `Rows with asterisks: ${asteriskRows}<br>`;
            analysis += `Visible asterisk rows: ${visibleAsteriskRows}<br>`;
            analysis += `</div>`;
            
            if (asteriskRows > 0 && visibleAsteriskRows < asteriskRows) {
                analysis += `<div class="error">`;
                analysis += `<strong>ISSUE FOUND:</strong> Some rows with asterisks are hidden!<br>`;
                analysis += `Hidden asterisk rows: ${asteriskRows - visibleAsteriskRows}`;
                analysis += `</div>`;
            }
            
            resultDiv.innerHTML = analysis;
        }
        
        function checkJavaScriptFiltering() {
            const resultDiv = document.getElementById('jsFilteringResult');
            
            // Check for any active filters
            const exactMatchFilter = document.querySelector('input[type="checkbox"][id*="exactMatch"]');
            const isFilterActive = exactMatchFilter && exactMatchFilter.checked;
            
            let html = '<h3>JavaScript Filtering Analysis:</h3>';
            html += `<p>Exact match filter found: ${exactMatchFilter ? 'Yes' : 'No'}</p>`;
            html += `<p>Filter active: ${isFilterActive}</p>`;
            
            // Check for any JavaScript functions that might be filtering
            if (typeof window.applyExactMatchFilter === 'function') {
                html += '<p>applyExactMatchFilter function found</p>';
            }
            
            if (typeof window.updateResultCounters === 'function') {
                html += '<p>updateResultCounters function found</p>';
            }
            
            // Test the filtering manually
            if (isFilterActive) {
                html += '<div class="error">Exact match filter is active - this might be hiding results!</div>';
                html += '<button onclick="toggleExactMatchFilter()">Disable Exact Match Filter</button>';
            }
            
            resultDiv.innerHTML = html;
        }
        
        function toggleExactMatchFilter() {
            const exactMatchFilter = document.querySelector('input[type="checkbox"][id*="exactMatch"]');
            if (exactMatchFilter) {
                exactMatchFilter.checked = false;
                exactMatchFilter.dispatchEvent(new Event('change'));
                
                // Re-analyze after toggling
                setTimeout(() => {
                    checkJavaScriptFiltering();
                    analyzeTableRows();
                }, 100);
            }
        }
        
        // Auto-run analysis when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkResultDisplay();
                analyzeTableRows();
                checkJavaScriptFiltering();
            }, 1000);
        });
    </script>
</body>
</html>
