<?php
/**
 * Debug pattern matching for party extraction
 */

require_once 'config/config.php';

echo "=== DEBUGGING PATTERN MATCHING ===" . PHP_EOL;

try {
    // Read the SOAP response file
    $responseFile = 'soap_response_2025-07-04_17-14-14.xml';
    $xmlContent = file_get_contents($responseFile);
    
    // Extract solutieSumar content
    if (preg_match('/<solutieSumar>(.*?)<\/solutieSumar>/s', $xmlContent, $matches)) {
        $solutieSumar = $matches[1];
        
        echo "Decision text length: " . strlen($solutieSumar) . " characters" . PHP_EOL;
        echo PHP_EOL;
        
        // Test different patterns
        $patterns = [
            'Original' => '/formulate de creditorii ([^;]+(?:;[^;]+)*)/',
            'New Pattern' => '/creditorii\s+([^.]+?)\s+în contradictoriu/s',
            'Alternative 1' => '/creditorii\s+([^.]+)\s+în contradictoriu/s',
            'Alternative 2' => '/creditorii\s+(.*?)\s+în contradictoriu/s',
            'Alternative 3' => '/creditorii\s+(.*?)\s*în contradictoriu/s'
        ];
        
        foreach ($patterns as $name => $pattern) {
            echo "=== TESTING PATTERN: $name ===" . PHP_EOL;
            echo "Pattern: $pattern" . PHP_EOL;
            
            if (preg_match($pattern, $solutieSumar, $matches)) {
                echo "✅ MATCH FOUND" . PHP_EOL;
                echo "Match length: " . strlen($matches[1]) . " characters" . PHP_EOL;
                
                // Count semicolons
                $semicolons = substr_count($matches[1], ';');
                echo "Semicolons: $semicolons" . PHP_EOL;
                
                // Show first 100 and last 100 characters
                $text = $matches[1];
                echo "First 100 chars: " . substr($text, 0, 100) . "..." . PHP_EOL;
                echo "Last 100 chars: ..." . substr($text, -100) . PHP_EOL;
                
                // Check for our target parties
                $targetParties = ['SARAGEA TUDORIŢA', 'ZAMFIR NICOLETA'];
                foreach ($targetParties as $party) {
                    if (strpos($text, $party) !== false) {
                        echo "✅ Contains '$party'" . PHP_EOL;
                    } else {
                        echo "❌ Missing '$party'" . PHP_EOL;
                    }
                }
            } else {
                echo "❌ NO MATCH" . PHP_EOL;
            }
            echo PHP_EOL;
        }
        
        // Check if "în contradictoriu" exists in the text
        echo "=== CHECKING FOR KEY PHRASES ===" . PHP_EOL;
        $keyPhrases = [
            'creditorii',
            'în contradictoriu',
            'formulate de creditorii',
            'SARAGEA TUDORIŢA',
            'ZAMFIR NICOLETA'
        ];
        
        foreach ($keyPhrases as $phrase) {
            $pos = strpos($solutieSumar, $phrase);
            if ($pos !== false) {
                echo "✅ Found '$phrase' at position $pos" . PHP_EOL;
            } else {
                echo "❌ Not found '$phrase'" . PHP_EOL;
            }
        }
        
    } else {
        echo "❌ Could not extract solutieSumar from XML" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== DEBUG COMPLETE ===" . PHP_EOL;
?>
