/**
 * Verification Script for SOAP API Priority Implementation
 * 
 * This script verifies that parties are sourced exclusively from SOAP API
 * and not from decision text extraction for the specific case.
 * 
 * Usage: Run this in the browser console on the case details page
 */

(function() {
    'use strict';
    
    console.group('🎯 SOAP API Priority Verification');
    
    // Get all party rows
    const partyRows = document.querySelectorAll('.parte-row');
    const totalParties = partyRows.length;
    
    console.log(`📊 Total parties found: ${totalParties}`);
    
    if (totalParties === 0) {
        console.error('❌ No parties found! Check if the page loaded correctly.');
        console.groupEnd();
        return;
    }
    
    // Check sources
    const soapApiParties = document.querySelectorAll('.parte-row[data-source="soap_api"]');
    const decisionTextParties = document.querySelectorAll('.parte-row[data-source="decision_text"]');
    const unknownSourceParties = document.querySelectorAll('.parte-row[data-source="unknown"]');
    
    console.log(`🔍 Source Distribution:`);
    console.log(`  ✅ SOAP API: ${soapApiParties.length}`);
    console.log(`  ⚠️  Decision Text: ${decisionTextParties.length}`);
    console.log(`  ❓ Unknown: ${unknownSourceParties.length}`);
    
    // Verification results
    const allFromSoapApi = soapApiParties.length === totalParties;
    const noneFromDecisionText = decisionTextParties.length === 0;
    const noneFromUnknown = unknownSourceParties.length === 0;
    
    console.log(`\n📋 Verification Results:`);
    console.log(`  ${allFromSoapApi ? '✅' : '❌'} All parties from SOAP API: ${allFromSoapApi}`);
    console.log(`  ${noneFromDecisionText ? '✅' : '❌'} No parties from decision text: ${noneFromDecisionText}`);
    console.log(`  ${noneFromUnknown ? '✅' : '❌'} No unknown sources: ${noneFromUnknown}`);
    
    // Check "Informații suplimentare" column content
    const infoColumns = document.querySelectorAll('.informatii-suplimentare');
    let apiOfficialCount = 0;
    let decisionExtractionCount = 0;
    
    infoColumns.forEach((col, index) => {
        const text = col.textContent.trim();
        if (text.includes('API oficial')) {
            apiOfficialCount++;
        }
        if (text.includes('Extras din decizie')) {
            decisionExtractionCount++;
        }
    });
    
    console.log(`\n🔍 "Informații suplimentare" Column Analysis:`);
    console.log(`  ✅ "API oficial" indicators: ${apiOfficialCount}`);
    console.log(`  ⚠️  "Extras din decizie" indicators: ${decisionExtractionCount}`);
    
    // Check for legal text fragments in party names
    const legalTextPatterns = [
        /obligaţia de/i,
        /pune în vedere/i,
        /fixează termen/i,
        /desemnează/i,
        /în temeiul/i,
        /administrator judiciar/i,
        /buletinul procedurilor/i,
        /executorie/i,
        /pronunţată/i
    ];
    
    let partiesWithLegalText = 0;
    const problematicParties = [];
    
    partyRows.forEach((row, index) => {
        const partyName = row.getAttribute('data-nume') || '';
        const hasLegalText = legalTextPatterns.some(pattern => pattern.test(partyName));
        
        if (hasLegalText) {
            partiesWithLegalText++;
            problematicParties.push({
                index: index + 1,
                name: partyName,
                source: row.getAttribute('data-source')
            });
        }
    });
    
    console.log(`\n🔍 Legal Text Fragment Analysis:`);
    console.log(`  ${partiesWithLegalText === 0 ? '✅' : '❌'} Parties with legal text fragments: ${partiesWithLegalText}`);
    
    if (problematicParties.length > 0) {
        console.log(`  ⚠️  Problematic parties found:`);
        problematicParties.forEach(party => {
            console.log(`    - Party #${party.index}: "${party.name}" (source: ${party.source})`);
        });
    }
    
    // Overall assessment
    const isPerfect = allFromSoapApi && noneFromDecisionText && noneFromUnknown && 
                     apiOfficialCount === totalParties && decisionExtractionCount === 0 && 
                     partiesWithLegalText === 0;
    
    console.log(`\n🎯 Overall Assessment:`);
    if (isPerfect) {
        console.log(`✅ PERFECT! All requirements met:`);
        console.log(`   - All ${totalParties} parties sourced from SOAP API`);
        console.log(`   - No decision text extraction used`);
        console.log(`   - Clean party names without legal text`);
        console.log(`   - Proper source attribution in UI`);
    } else {
        console.log(`❌ Issues found! Requirements not fully met:`);
        if (!allFromSoapApi) console.log(`   - Not all parties from SOAP API`);
        if (!noneFromDecisionText) console.log(`   - Some parties from decision text`);
        if (!noneFromUnknown) console.log(`   - Some parties have unknown source`);
        if (apiOfficialCount !== totalParties) console.log(`   - Missing "API oficial" indicators`);
        if (decisionExtractionCount > 0) console.log(`   - Found "Extras din decizie" indicators`);
        if (partiesWithLegalText > 0) console.log(`   - Found legal text fragments in party names`);
    }
    
    // Additional debug information
    if (window.location.search.includes('debug=1')) {
        console.log(`\n🐛 Debug Mode Information:`);
        console.log(`   - Debug mode is enabled`);
        console.log(`   - Check HTML comments for backend processing details`);
        console.log(`   - Check browser network tab for API responses`);
        console.log(`   - Check server error logs for extraction decisions`);
    }
    
    // Export results for further analysis
    window.soapApiVerificationResults = {
        totalParties,
        soapApiParties: soapApiParties.length,
        decisionTextParties: decisionTextParties.length,
        unknownSourceParties: unknownSourceParties.length,
        apiOfficialCount,
        decisionExtractionCount,
        partiesWithLegalText,
        problematicParties,
        isPerfect,
        allFromSoapApi,
        noneFromDecisionText,
        noneFromUnknown
    };
    
    console.log(`\n📊 Results exported to: window.soapApiVerificationResults`);
    console.groupEnd();
    
    // Return summary for immediate use
    return {
        success: isPerfect,
        totalParties,
        soapApiParties: soapApiParties.length,
        issues: !isPerfect ? 'Check console for details' : 'None'
    };
})();
