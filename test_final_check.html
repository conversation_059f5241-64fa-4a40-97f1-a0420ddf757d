<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-check-circle me-2"></i>
            Test Final Funcționalitate
        </h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Accesibilitate Funcții</h3>
                <div id="functionTests"></div>
                
                <h3 class="mt-4">Test Butoane Directe</h3>
                <button type="button" class="btn btn-primary me-2" onclick="expandAllResults()">
                    <i class="fas fa-expand-alt me-1"></i>
                    Expandează toate
                </button>
                <button type="button" class="btn btn-secondary me-2" onclick="collapseAllResults()">
                    <i class="fas fa-compress-alt me-1"></i>
                    Restrânge toate
                </button>
                <button type="button" class="btn btn-info" onclick="toggleTermResults(0)">
                    <i class="fas fa-exchange-alt me-1"></i>
                    Toggle Section 0
                </button>
            </div>
            
            <div class="col-md-6">
                <h3>Elemente Test</h3>
                
                <div class="term-results">
                    <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-light border rounded" onclick="toggleTermResults(0)">
                        <div>
                            <h6 class="mb-1">Test Section 1</h6>
                            <small class="text-muted">Click to toggle</small>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon" id="toggleIcon0"></i>
                    </div>
                    
                    <div id="termContent0" style="display: none;" class="p-3 border rounded mb-3">
                        <p>Test content for section 1</p>
                    </div>
                </div>
                
                <div class="term-results">
                    <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-light border rounded" onclick="toggleTermResults(1)">
                        <div>
                            <h6 class="mb-1">Test Section 2</h6>
                            <small class="text-muted">Click to toggle</small>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon" id="toggleIcon1"></i>
                    </div>
                    
                    <div id="termContent1" style="display: none;" class="p-3 border rounded">
                        <p>Test content for section 2</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info mt-4">
            <h5><i class="fas fa-info-circle me-2"></i>Instrucțiuni</h5>
            <p>Dacă toate funcțiile sunt marcate cu verde și butoanele funcționează, atunci problema a fost rezolvată!</p>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1050; display: none;">
        <div id="notification" class="alert"></div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Notification system (same as in index.php)
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');

            if (!container || !notification) {
                console.error('Notification elements not found');
                return;
            }

            // Icon mapping
            const iconMap = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };

            const icon = iconMap[type] || 'info-circle';

            // Set notification content and style
            notification.className = `alert alert-${type}`;
            notification.innerHTML = `<i class="fas fa-${icon} me-2"></i>${message}`;

            // Show notification
            container.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }
        
        // Toggle function (same as in index.php)
        function toggleTermResults(index) {
            try {
                const content = document.getElementById('termContent' + index);
                const icon = document.getElementById('toggleIcon' + index);

                console.log('Toggling term results for index:', index);
                console.log('Content element:', content);
                console.log('Icon element:', icon);

                if (!content) {
                    console.error('Content element not found for index:', index);
                    showNotification('Eroare: Secțiunea nu a fost găsită.', 'danger');
                    return;
                }

                if (!icon) {
                    console.warn('Icon element not found for index:', index);
                }

                const isVisible = content.style.display !== 'none';

                if (isVisible) {
                    content.style.display = 'none';
                    if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                    console.log('Collapsed section', index);
                } else {
                    content.style.display = 'block';
                    if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                    console.log('Expanded section', index);
                }

            } catch (error) {
                console.error('Error in toggleTermResults:', error);
                showNotification('Eroare la comutarea secțiunii.', 'danger');
            }
        }

        // Expand all function (same as in index.php)
        function expandAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Expanding all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru expandare.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'block';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-up toggle-icon';
                });

                showNotification('Toate secțiunile au fost expandate.', 'info');
                console.log('Successfully expanded', termContents.length, 'sections');

            } catch (error) {
                console.error('Error in expandAllResults:', error);
                showNotification('Eroare la expandarea rezultatelor.', 'danger');
            }
        }

        // Collapse all function (same as in index.php)
        function collapseAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Collapsing all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru restrângere.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'none';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-down toggle-icon';
                });

                showNotification('Toate secțiunile au fost restrânse.', 'info');
                console.log('Successfully collapsed', termContents.length, 'sections');

            } catch (error) {
                console.error('Error in collapseAllResults:', error);
                showNotification('Eroare la restrângerea rezultatelor.', 'danger');
            }
        }
        
        // Test function accessibility
        function testFunctions() {
            const testContainer = document.getElementById('functionTests');
            const functions = [
                'showNotification',
                'toggleTermResults', 
                'expandAllResults',
                'collapseAllResults'
            ];
            
            let results = '';
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results += `<div class="test-result success">✓ ${funcName} este accesibilă</div>`;
                } else {
                    results += `<div class="test-result error">✗ ${funcName} nu este accesibilă</div>`;
                }
            });
            
            testContainer.innerHTML = results;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, testing functions...');
            testFunctions();
            showNotification('Test încărcat cu succes!', 'success');
        });
    </script>
</body>
</html>
