<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dosar adăugat în monitorizare</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .case-details {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .case-details h3 {
            margin: 0 0 15px 0;
            color: #007bff;
            font-size: 18px;
        }
        .detail-row {
            margin-bottom: 10px;
            display: flex;
            flex-wrap: wrap;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
            min-width: 140px;
            margin-right: 10px;
        }
        .detail-value {
            color: #333;
            flex: 1;
        }
        .notification-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .notification-info .icon {
            color: #007bff;
            margin-right: 8px;
        }
        .action-button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
        .action-button:hover {
            background-color: #0056b3;
            color: white;
            text-decoration: none;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .footer p {
            margin: 5px 0;
            font-size: 14px;
            color: #6c757d;
        }
        .footer .portal-name {
            font-weight: 600;
            color: #007bff;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .header {
                padding: 20px 15px;
            }
            .content {
                padding: 20px 15px;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>{{ portal_name }}</h1>
            <p>Dosar adăugat în monitorizare</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                Bună ziua <strong>{{ user_name }}</strong>,
            </div>
            
            <p>Dosarul <strong>{{ case_number }}</strong> de la <strong>{{ institution_name }}</strong> a fost adăugat cu succes în lista de monitorizare.</p>
            
            <div class="case-details">
                <h3>📋 Detalii dosar</h3>
                <div class="detail-row">
                    <span class="detail-label">Numărul dosarului:</span>
                    <span class="detail-value">{{ case_number }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Instanța:</span>
                    <span class="detail-value">{{ institution_name }}</span>
                </div>
                {% if case_object %}
                <div class="detail-row">
                    <span class="detail-label">Obiectul dosarului:</span>
                    <span class="detail-value">{{ case_object }}</span>
                </div>
                {% endif %}
                {% if monitoring_reason %}
                <div class="detail-row">
                    <span class="detail-label">Motivul monitorizării:</span>
                    <span class="detail-value">{{ monitoring_reason }}</span>
                </div>
                {% endif %}
                <div class="detail-row">
                    <span class="detail-label">Frecvența notificărilor:</span>
                    <span class="detail-value">
                        {% if notification_frequency == 'immediate' %}
                            Imediat
                        {% elseif notification_frequency == 'daily' %}
                            Zilnic
                        {% elseif notification_frequency == 'weekly' %}
                            Săptămânal
                        {% else %}
                            {{ notification_frequency }}
                        {% endif %}
                    </span>
                </div>
            </div>
            
            <div class="notification-info">
                <span class="icon">🔔</span>
                <strong>Notificări automate:</strong> Veți primi notificări automate când se produc modificări în acest dosar, conform frecvenței selectate.
            </div>
            
            <div style="text-align: center;">
                <a href="{{ base_url }}/monitor.php" class="action-button">
                    Gestionează dosarele monitorizate
                </a>
            </div>
            
            <p style="margin-top: 30px; font-size: 14px; color: #6c757d;">
                Pentru a vedea detaliile complete ale dosarului, accesați: 
                <a href="{{ base_url }}/detalii_dosar.php?numar={{ case_number }}&institutie={{ institution_name|url_encode }}" style="color: #007bff;">
                    {{ base_url }}/detalii_dosar.php?numar={{ case_number }}
                </a>
            </p>
        </div>
        
        <div class="footer">
            <p class="portal-name">{{ portal_name }}</p>
            <p>&copy; {{ "now"|date("Y") }} Portal Judiciar România. Toate drepturile rezervate.</p>
            <p>
                <a href="{{ base_url }}" style="color: #007bff; text-decoration: none;">{{ base_url }}</a>
            </p>
        </div>
    </div>
</body>
</html>
