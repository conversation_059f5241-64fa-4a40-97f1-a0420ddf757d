<?php
/**
 * Analyze decision text content for the 550+ party case
 * to understand what patterns we need to extract all parties
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🔍 Decision Text Analysis for 550+ Party Case</h1>\n";
echo "<p><strong>Case:</strong> 130/98/2022 from CurteadeApelBUCURESTI</p>\n";

try {
    $dosarService = new DosarService();
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    // Get raw SOAP response to analyze decision text
    $soapClient = new SoapClient('http://portalquery.just.ro/query.asmx?WSDL');
    
    $params = [
        'institutie' => $institutie,
        'numarDosar' => $numarDosar,
        'anDosar' => null,
        'numarSedinta' => null,
        'dataUltimeiModificari' => null
    ];
    
    echo "<h2>📡 SOAP Response Analysis</h2>\n";
    
    $response = $soapClient->CautareDosar($params);
    
    if (isset($response->CautareDosarResult->DosarCautat)) {
        $dosar = $response->CautareDosarResult->DosarCautat;
        
        // Count SOAP parties
        $soapParties = [];
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $partiArray = is_array($dosar->parti->DosarParte) ? $dosar->parti->DosarParte : [$dosar->parti->DosarParte];
            $soapParties = $partiArray;
        }
        
        echo "<p><strong>SOAP API Parties:</strong> " . count($soapParties) . "</p>\n";
        
        // Analyze sessions for decision text
        $allDecisionTexts = [];
        $sessionCount = 0;
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = is_array($dosar->sedinte->DosarSedinta) ? $dosar->sedinte->DosarSedinta : [$dosar->sedinte->DosarSedinta];
            
            foreach ($sedinte as $sedinta) {
                $sessionCount++;
                
                echo "<h3>📅 Session {$sessionCount}</h3>\n";
                echo "<ul>\n";
                echo "<li><strong>Date:</strong> " . ($sedinta->data ?? 'N/A') . "</li>\n";
                echo "<li><strong>Complete:</strong> " . ($sedinta->complet ?? 'N/A') . "</li>\n";
                echo "<li><strong>Object:</strong> " . ($sedinta->obiect ?? 'N/A') . "</li>\n";
                echo "</ul>\n";
                
                // Check for decision text in various fields
                $textSources = [];
                
                if (isset($sedinta->solutie) && !empty($sedinta->solutie)) {
                    $textSources['solutie'] = $sedinta->solutie;
                }
                
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $textSources['solutieSumar'] = $sedinta->solutieSumar;
                }
                
                if (isset($sedinta->obiect) && !empty($sedinta->obiect)) {
                    $textSources['obiect'] = $sedinta->obiect;
                }
                
                echo "<h4>📝 Text Sources Found:</h4>\n";
                
                if (empty($textSources)) {
                    echo "<p style='color: #dc3545;'>❌ No decision text found in this session</p>\n";
                } else {
                    foreach ($textSources as $sourceType => $text) {
                        $textLength = strlen($text);
                        $wordCount = str_word_count($text);
                        
                        echo "<div style='border: 1px solid #dee2e6; margin: 10px 0; padding: 15px; border-radius: 5px;'>\n";
                        echo "<h5 style='margin-top: 0; color: #495057;'>{$sourceType}</h5>\n";
                        echo "<p><strong>Length:</strong> {$textLength} characters</p>\n";
                        echo "<p><strong>Words:</strong> {$wordCount} words</p>\n";
                        
                        // Store for analysis
                        $allDecisionTexts[] = [
                            'session' => $sessionCount,
                            'type' => $sourceType,
                            'text' => $text,
                            'length' => $textLength,
                            'words' => $wordCount
                        ];
                        
                        // Show first 500 characters
                        $preview = substr($text, 0, 500);
                        echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;'>\n";
                        echo "<strong>Preview (first 500 chars):</strong><br>\n";
                        echo htmlspecialchars($preview) . ($textLength > 500 ? "..." : "") . "\n";
                        echo "</div>\n";
                        
                        echo "</div>\n";
                    }
                }
            }
        }
        
        echo "<h2>🔍 Pattern Analysis</h2>\n";
        
        if (empty($allDecisionTexts)) {
            echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
            echo "<p style='color: #721c24; margin: 0;'><strong>❌ CRITICAL ISSUE:</strong> No decision text found in any session. This explains why no additional parties are being extracted.</p>\n";
            echo "</div>\n";
        } else {
            echo "<p><strong>Total decision text sources:</strong> " . count($allDecisionTexts) . "</p>\n";
            
            // Analyze each text for potential party patterns
            foreach ($allDecisionTexts as $index => $textData) {
                echo "<h3>🔍 Pattern Analysis - Session {$textData['session']} ({$textData['type']})</h3>\n";
                
                $text = $textData['text'];
                
                // Test various patterns
                $patterns = [
                    'Creditor patterns' => '/creditor[ii]?\s+([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+)/ui',
                    'Împotriva patterns' => '/împotriva\s+([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+)/ui',
                    'Reclamant patterns' => '/reclamant[iî]?\s+([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+)/ui',
                    'Pârât patterns' => '/pârât[iî]?\s+([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+)/ui',
                    'Numbered lists' => '/\d+\.\s*([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+)/u',
                    'Capitalized names' => '/\b([A-ZĂÂÎȘȚăâîșțţşŞţŢ]{2,}(?:\s+[A-ZĂÂÎȘȚăâîșțţşŞţŢ]{2,})+)\b/u',
                    'Comma separated' => '/,\s*([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+)/u',
                    'Semicolon separated' => '/;\s*([A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+)/u'
                ];
                
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
                echo "<thead style='background: #e9ecef;'>\n";
                echo "<tr><th style='padding: 8px;'>Pattern Type</th><th style='padding: 8px;'>Matches</th><th style='padding: 8px;'>Sample Matches</th></tr>\n";
                echo "</thead>\n";
                echo "<tbody>\n";
                
                foreach ($patterns as $patternName => $pattern) {
                    preg_match_all($pattern, $text, $matches);
                    $matchCount = count($matches[1]);
                    
                    echo "<tr>\n";
                    echo "<td style='padding: 8px;'>{$patternName}</td>\n";
                    echo "<td style='padding: 8px; text-align: center;'>{$matchCount}</td>\n";
                    echo "<td style='padding: 8px;'>\n";
                    
                    if ($matchCount > 0) {
                        $sampleMatches = array_slice($matches[1], 0, 3);
                        foreach ($sampleMatches as $match) {
                            echo htmlspecialchars(trim($match)) . "<br>\n";
                        }
                        if ($matchCount > 3) {
                            echo "... and " . ($matchCount - 3) . " more\n";
                        }
                    } else {
                        echo "<em style='color: #6c757d;'>No matches</em>\n";
                    }
                    
                    echo "</td>\n";
                    echo "</tr>\n";
                }
                
                echo "</tbody>\n";
                echo "</table>\n";
                
                // Look for specific keywords that might indicate large party lists
                $keywords = ['creditor', 'debitor', 'împotriva', 'reclamant', 'pârât', 'lista', 'anexa', 'tabel'];
                $keywordCounts = [];
                
                foreach ($keywords as $keyword) {
                    $count = substr_count(strtolower($text), $keyword);
                    if ($count > 0) {
                        $keywordCounts[$keyword] = $count;
                    }
                }
                
                if (!empty($keywordCounts)) {
                    echo "<h4>🔑 Keyword Analysis</h4>\n";
                    echo "<ul>\n";
                    foreach ($keywordCounts as $keyword => $count) {
                        echo "<li><strong>{$keyword}:</strong> {$count} occurrences</li>\n";
                    }
                    echo "</ul>\n";
                }
            }
        }
        
    } else {
        echo "<p style='color: #dc3545;'><strong>❌ Case not found in SOAP response</strong></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>🎯 Recommendations</h2>\n";
echo "<p>Based on this analysis, the next steps should be:</p>\n";
echo "<ol>\n";
echo "<li>If no decision text found: Verify the case number and institution</li>\n";
echo "<li>If decision text found but few patterns match: Develop case-specific extraction patterns</li>\n";
echo "<li>If many patterns match: Enhance existing patterns to capture more variations</li>\n";
echo "<li>Consider that the case might not actually contain 550+ parties in the decision text</li>\n";
echo "</ol>\n";
?>
