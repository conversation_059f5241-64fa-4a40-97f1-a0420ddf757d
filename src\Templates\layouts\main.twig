<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    {% block seo_meta %}
        <title>{% block title %}{{ app.name }}{% endblock %}</title>
        <meta name="description" content="Portal Judiciar România - Căutare dosare judecătorești din toate instanțele. Acces gratuit și rapid la informații despre dosare civile și penale.">
        <meta name="keywords" content="portal judiciar, căutare dosare, instanțe românia, dosare civile, dosare penale">
        <meta name="robots" content="index, follow">
        <meta name="author" content="Portal Judiciar România">
        <link rel="canonical" href="{{ app.base_url }}/">

        <!-- Open Graph -->
        <meta property="og:title" content="Portal Judiciar România - Căutare Dosare Online">
        <meta property="og:description" content="Căutați dosare judecătorești din România. Portal oficial pentru verificarea dosarelor civile și penale din toate instanțele.">
        <meta property="og:type" content="website">
        <meta property="og:url" content="{{ app.base_url }}/">
        <meta property="og:site_name" content="Portal Judiciar România">
        <meta property="og:locale" content="ro_RO">
        <meta property="og:image" content="{{ app.base_url }}/images/logo.jpg">

        <!-- Twitter Cards -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="Portal Judiciar România - Căutare Dosare Online">
        <meta name="twitter:description" content="Căutați dosare judecătorești din România. Portal oficial pentru verificarea dosarelor civile și penale.">
        <meta name="twitter:image" content="http://localhost/just/images/logo.jpg">
    {% endblock %}

    <!-- Performance Optimizations -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="https://code.jquery.com">

    <!-- CSS Libraries with Performance Headers -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous">

    <!-- Local CSS with Cache Optimization (Minified) -->
    <link rel="stylesheet" href="assets/css/style.min.css?v={{ app.version|default('1.0') }}">
    <link rel="stylesheet" href="assets/css/responsive.min.css?v={{ app.version|default('1.0') }}">
    <link rel="stylesheet" href="assets/css/modern-pdf-export.min.css?v={{ app.version|default('1.0') }}">
    <link rel="stylesheet" href="assets/css/footer.min.css?v={{ app.version|default('1.0') }}">
    <link rel="stylesheet" href="assets/css/buttons.min.css?v={{ app.version|default('1.0') }}"

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    {% block stylesheets %}{% endblock %}
</head>
<body>


    <main class="container py-4">
        {% block breadcrumbs %}{% endblock %}

        {% if flash %}
            {% for message in flash %}
                <div class="alert alert-{{ message.type }} alert-dismissible fade show" role="alert">
                    {{ message.message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- Container pentru notificări -->
        <div id="notificationContainer" class="notification-container" style="display: none;">
            <div id="notification" class="alert" role="alert"></div>
        </div>

        {% block content %}{% endblock %}
    </main>

    <!-- Compact Footer -->
    <footer class="modern-footer" role="contentinfo">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <p class="mb-2">
                        &copy; {{ app.year }} <strong>Portal Judiciar România</strong> - {{ app.name }}. Toate drepturile rezervate.
                    </p>
                    <p class="mb-0 small text-muted">
                        Portal oficial pentru <a href="../search.php" class="footer-link-inline">căutare dosare instanțe</a> și
                        <a href="../sedinte.php" class="footer-link-inline">verificare dosare tribunal</a> din România.
                    </p>
                </div>
                <div class="col-md-4 text-md-right">
                    <div class="footer-links">
                        <a href="../index.php" class="footer-link" title="Portal Judiciar România - Pagina principală">Acasă</a>
                        <a href="../search.php" class="footer-link" title="Căutare dosare instanțe România">Căutare Dosare</a>
                        <a href="../sedinte.php" class="footer-link" title="Ședințe judecătorești România">Ședințe</a>
                        <a href="http://portal.just.ro" target="_blank" rel="noopener" class="footer-link" title="Portal oficial Ministerul Justiției">Portal Just Oficial</a>
                        <a href="../contact.php" class="footer-link" title="Contact Portal Judiciar România">Contact</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Modern Back to Top Button -->
    <button id="backToTopBtn" class="back-to-top" title="Mergi sus" aria-label="Mergi la începutul paginii">
        <i class="fas fa-chevron-up" aria-hidden="true"></i>
    </button>

    <!-- JavaScript Libraries with Performance Optimization -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous" defer></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" crossorigin="anonymous" defer></script>

    <!-- Local JavaScript with Cache Optimization (Minified) -->
    <script src="assets/js/script.min.js?v={{ app.version|default('1.0') }}" defer></script>

    <!-- Enhanced Back to Top Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const backToTopBtn = document.getElementById('backToTopBtn');
        if (!backToTopBtn) return;

        let isScrolling = false;

        // Throttled scroll handler for better performance
        function handleScroll() {
            if (!isScrolling) {
                window.requestAnimationFrame(function() {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                    if (scrollTop > 400) {
                        backToTopBtn.classList.add('visible');
                    } else {
                        backToTopBtn.classList.remove('visible');
                    }

                    isScrolling = false;
                });
                isScrolling = true;
            }
        }

        // Add scroll event listener
        window.addEventListener('scroll', handleScroll, { passive: true });

        // Enhanced click handler with smooth scroll
        backToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Add active state
            this.style.transform = 'translateY(-1px) scale(0.95)';

            // Smooth scroll to top
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });

            // Reset button state
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Focus management for accessibility
            document.body.focus();
        });

        // Keyboard accessibility
        backToTopBtn.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    </script>

    {% block javascripts %}{% endblock %}
</body>
</html>
