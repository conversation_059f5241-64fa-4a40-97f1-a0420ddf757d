{% extends "layouts/main.twig" %}

{# 
    Base template that provides compatibility layer between 
    layouts/main.twig and templates that expect base.twig
#}

{% block seo_meta %}
    <title>{% block title %}{{ app.name }}{% endblock %}</title>
    {% if block('meta_description') is defined %}
        <meta name="description" content="{% block meta_description %}{% endblock %}">
    {% else %}
        <meta name="description" content="Portal Judiciar România - Căutare dosare judecătorești din toate instanțele. Acces gratuit și rapid la informații despre dosare civile și penale.">
    {% endif %}
    <meta name="keywords" content="portal judiciar, căutare dosare, instanțe românia, dosare civile, dosare penale">
    <meta name="robots" content="index, follow">
    <meta name="author" content="Portal Judiciar România">
    <link rel="canonical" href="{{ app.base_url }}/">

    <!-- Open Graph -->
    <meta property="og:title" content="{% block og_title %}{{ block('title') }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ block('meta_description') }}{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ app.base_url }}/">
    <meta property="og:site_name" content="Portal Judiciar România">
    <meta property="og:locale" content="ro_RO">
    <meta property="og:image" content="{{ app.base_url }}/images/logo.jpg">

    <!-- Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}{{ block('title') }}{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}{{ block('meta_description') }}{% endblock %}">
    <meta name="twitter:image" content="{{ app.base_url }}/images/logo.jpg">
{% endblock %}

{% block stylesheets %}
    {% block additional_css %}{% endblock %}
{% endblock %}

{% block javascripts %}
    {% block additional_js %}{% endblock %}
{% endblock %}
