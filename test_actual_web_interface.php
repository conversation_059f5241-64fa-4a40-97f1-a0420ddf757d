<?php
// Test the actual web interface by simulating the exact form submission
$_POST['bulkSearchTerms'] = "14096/3/2024*";
$_SERVER['REQUEST_METHOD'] = 'POST';

// Capture the complete output from index.php
ob_start();
include 'index.php';
$output = ob_get_clean();

echo "<!DOCTYPE html>";
echo "<html lang='ro'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Actual Web Interface Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";

echo "<h1>🔍 ACTUAL WEB INTERFACE TEST</h1>";
echo "<p class='lead'>Testing the real web interface output for '14096/3/2024*'</p>";

// Parse the HTML output to extract the results table
$dom = new DOMDocument();
@$dom->loadHTML($output);
$xpath = new DOMXPath($dom);

// Look for the results table
$tables = $xpath->query('//table[contains(@class, "table")]');
$foundResults = false;
$resultCount = 0;
$foundLiteralCase = false;
$caseDetails = [];

echo "<h3>Raw HTML Analysis</h3>";
echo "<div class='alert alert-info'>";
echo "<p>HTML output length: " . strlen($output) . " characters</p>";
echo "<p>Tables found: " . $tables->length . "</p>";
echo "</div>";

if ($tables->length > 0) {
    echo "<h3>Results Table Found</h3>";
    
    foreach ($tables as $tableIndex => $table) {
        $rows = $xpath->query('.//tr', $table);
        
        echo "<h4>Table " . ($tableIndex + 1) . " - Rows: " . $rows->length . "</h4>";
        
        if ($rows->length > 1) { // More than just header
            $foundResults = true;
            
            // Display the actual table
            echo "<div class='table-responsive'>";
            echo $dom->saveHTML($table);
            echo "</div>";
            
            // Analyze each row
            foreach ($rows as $rowIndex => $row) {
                $cells = $xpath->query('.//td', $row);
                
                if ($cells->length > 0) {
                    $resultCount++;
                    
                    $caseNumber = trim($cells->item(0)->textContent ?? '');
                    $institution = trim($cells->item(1)->textContent ?? '');
                    $date = trim($cells->item(2)->textContent ?? '');
                    $object = trim($cells->item(3)->textContent ?? '');
                    $category = trim($cells->item(4)->textContent ?? '');
                    $stage = trim($cells->item(5)->textContent ?? '');
                    
                    $caseDetails[] = [
                        'number' => $caseNumber,
                        'institution' => $institution,
                        'date' => $date,
                        'object' => $object,
                        'category' => $category,
                        'stage' => $stage
                    ];
                    
                    echo "<div class='card mt-2'>";
                    echo "<div class='card-body'>";
                    echo "<h6>Row " . ($rowIndex) . " Details:</h6>";
                    echo "<ul>";
                    echo "<li><strong>Case Number:</strong> '{$caseNumber}'</li>";
                    echo "<li><strong>Institution:</strong> '{$institution}'</li>";
                    echo "<li><strong>Date:</strong> '{$date}'</li>";
                    echo "<li><strong>Object:</strong> " . substr($object, 0, 100) . "...</li>";
                    echo "<li><strong>Category:</strong> '{$category}'</li>";
                    echo "<li><strong>Stage:</strong> '{$stage}'</li>";
                    echo "</ul>";
                    
                    // Check if this is the literal asterisk case
                    if ($caseNumber === '14096/3/2024*') {
                        $foundLiteralCase = true;
                        echo "<div class='alert alert-success'>";
                        echo "<h5>🎯 FOUND THE LITERAL ASTERISK CASE!</h5>";
                        echo "<p>This is the case the user was looking for with the expected data.</p>";
                        
                        // Verify expected details
                        $expectedDate = '03.10.2024';
                        $expectedObject = 'Rejudecare';
                        $expectedCategory = 'Faliment';
                        $expectedStage = 'Fond';
                        
                        echo "<h6>Verification:</h6>";
                        echo "<ul>";
                        echo "<li>Date match: " . (strpos($date, $expectedDate) !== false ? "✅ YES" : "❌ NO") . "</li>";
                        echo "<li>Object contains 'Rejudecare': " . (strpos($object, $expectedObject) !== false ? "✅ YES" : "❌ NO") . "</li>";
                        echo "<li>Category is 'Faliment': " . ($category === $expectedCategory ? "✅ YES" : "❌ NO") . "</li>";
                        echo "<li>Stage is 'Fond': " . ($stage === $expectedStage ? "✅ YES" : "❌ NO") . "</li>";
                        echo "</ul>";
                        echo "</div>";
                    }
                    
                    echo "</div>";
                    echo "</div>";
                }
            }
        }
    }
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h4>No Results Table Found</h4>";
    echo "<p>The HTML output does not contain a results table. This could indicate:</p>";
    echo "<ul>";
    echo "<li>No search results were found</li>";
    echo "<li>An error occurred during the search</li>";
    echo "<li>The search form was not processed correctly</li>";
    echo "</ul>";
    echo "</div>";
    
    // Look for error messages or notifications
    $alerts = $xpath->query('//div[contains(@class, "alert")]');
    if ($alerts->length > 0) {
        echo "<h4>Alerts/Messages Found:</h4>";
        foreach ($alerts as $alert) {
            echo "<div class='alert alert-info'>";
            echo htmlspecialchars($alert->textContent);
            echo "</div>";
        }
    }
    
    // Show a sample of the raw output for debugging
    echo "<h4>Raw Output Sample (first 2000 characters):</h4>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars(substr($output, 0, 2000));
    echo "</pre>";
}

// Summary
echo "<div class='card mt-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3>🎯 Web Interface Test Summary</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>Search Results</h5>";
echo "<ul>";
echo "<li>Results table found: " . ($foundResults ? "✅ YES" : "❌ NO") . "</li>";
echo "<li>Total cases displayed: {$resultCount}</li>";
echo "<li>Literal asterisk case found: " . ($foundLiteralCase ? "✅ YES" : "❌ NO") . "</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>Expected vs Actual</h5>";
if ($foundLiteralCase) {
    echo "<div class='alert alert-success'>";
    echo "<strong>✅ SUCCESS!</strong><br>";
    echo "The literal asterisk case '14096/3/2024*' is found and displayed in the web interface!";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<strong>❌ ISSUE!</strong><br>";
    echo "The literal asterisk case is not appearing in the web interface results.";
    echo "</div>";
}
echo "</div>";
echo "</div>";

// Detailed case analysis
if (count($caseDetails) > 0) {
    echo "<h5>All Cases Found:</h5>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm'>";
    echo "<thead><tr><th>Case Number</th><th>Institution</th><th>Date</th><th>Category</th><th>Stage</th></tr></thead>";
    echo "<tbody>";
    foreach ($caseDetails as $case) {
        $rowClass = ($case['number'] === '14096/3/2024*') ? 'table-success' : '';
        echo "<tr class='{$rowClass}'>";
        echo "<td>" . htmlspecialchars($case['number']) . "</td>";
        echo "<td>" . htmlspecialchars($case['institution']) . "</td>";
        echo "<td>" . htmlspecialchars($case['date']) . "</td>";
        echo "<td>" . htmlspecialchars($case['category']) . "</td>";
        echo "<td>" . htmlspecialchars($case['stage']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Final conclusion
echo "<div class='card mt-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3>🏁 FINAL CONCLUSION</h3>";
echo "</div>";
echo "<div class='card-body'>";

if ($foundLiteralCase) {
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ WILDCARD SEARCH IS WORKING CORRECTLY!</h4>";
    echo "<p>The search for '14096/3/2024*' successfully finds and displays the literal asterisk case with all the expected details:</p>";
    echo "<ul>";
    echo "<li>✅ Case Number: 14096/3/2024*</li>";
    echo "<li>✅ Date: 03.10.2024</li>";
    echo "<li>✅ Object: procedura insolvenței – societăți pe acțiuni Rejudecare</li>";
    echo "<li>✅ Institution: Tribunalul BUCUREȘTI</li>";
    echo "<li>✅ Category: Faliment</li>";
    echo "<li>✅ Stage: Fond</li>";
    echo "</ul>";
    echo "<p><strong>The user's requirements are fully satisfied!</strong></p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h4>⚠️ INVESTIGATION NEEDED</h4>";
    echo "<p>The backend search is working correctly (as confirmed by previous tests), but the web interface is not displaying the literal asterisk case.</p>";
    echo "<p>This suggests an issue with:</p>";
    echo "<ul>";
    echo "<li>Result processing in the web interface</li>";
    echo "<li>HTML rendering or table generation</li>";
    echo "<li>Form submission handling</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
