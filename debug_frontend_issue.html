<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Frontend Issue</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border: 1px solid #dee2e6; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #dee2e6; padding: 8px; text-align: left; }
        th { background: #e9ecef; }
        .highlight { background: #fff3cd; }
    </style>
</head>
<body>
    <h1>Debug Frontend Issue for "14096/3/2024*"</h1>
    
    <div class="debug-section">
        <h2>Step 1: Perform Search</h2>
        <form action="index.php" method="POST" target="searchFrame" onsubmit="return performSearch()">
            <input type="text" name="bulkSearchTerms" value="14096/3/2024*" style="width: 300px; padding: 8px;">
            <button type="submit">Search</button>
        </form>
        <iframe name="searchFrame" style="width: 100%; height: 400px; border: 1px solid #dee2e6; margin-top: 10px;"></iframe>
    </div>
    
    <div class="debug-section">
        <h2>Step 2: Analyze Results</h2>
        <button onclick="analyzeSearchFrame()">Analyze Search Results</button>
        <div id="analysisResults"></div>
    </div>
    
    <div class="debug-section">
        <h2>Step 3: Test Exact Match Filter</h2>
        <button onclick="testExactMatchFilter()">Test Filter Behavior</button>
        <div id="filterResults"></div>
    </div>
    
    <div class="debug-section">
        <h2>Step 4: Console Logs</h2>
        <button onclick="captureConsoleLogs()">Capture Console Logs</button>
        <div id="consoleLogs"></div>
    </div>

    <script>
        let searchFrame = null;
        
        function performSearch() {
            console.log('Performing search for 14096/3/2024*');
            
            // Clear sessionStorage to ensure clean state
            sessionStorage.removeItem('exactMatchFilter');
            
            setTimeout(() => {
                searchFrame = document.querySelector('iframe[name="searchFrame"]');
                console.log('Search frame:', searchFrame);
                
                // Wait for the frame to load
                setTimeout(() => {
                    analyzeSearchFrame();
                }, 3000);
            }, 1000);
            
            return true; // Allow form submission
        }
        
        function analyzeSearchFrame() {
            const resultDiv = document.getElementById('analysisResults');
            
            if (!searchFrame || !searchFrame.contentDocument) {
                resultDiv.innerHTML = '<div class="error">Cannot access search frame content. Check if search completed.</div>';
                return;
            }
            
            try {
                const frameDoc = searchFrame.contentDocument;
                
                // Look for result messages
                const resultMessages = frameDoc.querySelectorAll('[id^="resultMessage"]');
                const tableRows = frameDoc.querySelectorAll('table tbody tr');
                const resultCards = frameDoc.querySelectorAll('.result-card');
                
                let html = '<h3>Search Frame Analysis:</h3>';
                html += `<p><strong>Result messages found:</strong> ${resultMessages.length}</p>`;
                html += `<p><strong>Table rows found:</strong> ${tableRows.length}</p>`;
                html += `<p><strong>Result cards found:</strong> ${resultCards.length}</p>`;
                
                // Analyze result messages
                if (resultMessages.length > 0) {
                    html += '<h4>Result Messages:</h4>';
                    resultMessages.forEach((msg, index) => {
                        const text = msg.textContent || msg.innerText;
                        const term = msg.getAttribute('data-term');
                        const count = msg.getAttribute('data-original-count');
                        
                        html += `<div class="debug-section ${text.includes('3 rezultate') ? 'success' : 'error'}">`;
                        html += `<strong>Message ${index + 1}:</strong> "${text}"<br>`;
                        html += `<strong>Data term:</strong> ${term}<br>`;
                        html += `<strong>Data count:</strong> ${count}`;
                        html += `</div>`;
                    });
                }
                
                // Analyze table rows
                if (tableRows.length > 0) {
                    html += '<h4>Table Rows Analysis:</h4>';
                    html += '<table><tr><th>Index</th><th>Case Number</th><th>Search Type</th><th>Visible</th><th>Classes</th></tr>';
                    
                    let totalRows = 0;
                    let visibleRows = 0;
                    let asteriskRows = 0;
                    let visibleAsteriskRows = 0;
                    
                    tableRows.forEach((row, index) => {
                        totalRows++;
                        const caseNumber = row.getAttribute('data-numar') || '';
                        const searchType = row.getAttribute('data-search-type') || '';
                        const isVisible = frameDoc.defaultView.getComputedStyle(row).display !== 'none';
                        const classes = row.className;
                        
                        if (isVisible) visibleRows++;
                        
                        const hasAsterisk = caseNumber.includes('*');
                        if (hasAsterisk) {
                            asteriskRows++;
                            if (isVisible) visibleAsteriskRows++;
                        }
                        
                        html += `<tr class="${hasAsterisk ? 'highlight' : ''}">`;
                        html += `<td>${index + 1}</td>`;
                        html += `<td>${caseNumber}</td>`;
                        html += `<td>${searchType}</td>`;
                        html += `<td>${isVisible ? 'YES' : 'NO'}</td>`;
                        html += `<td>${classes}</td>`;
                        html += `</tr>`;
                    });
                    
                    html += '</table>';
                    
                    html += `<div class="debug-section ${visibleAsteriskRows > 0 ? 'success' : 'error'}">`;
                    html += `<strong>Summary:</strong><br>`;
                    html += `Total rows: ${totalRows}<br>`;
                    html += `Visible rows: ${visibleRows}<br>`;
                    html += `Rows with asterisks: ${asteriskRows}<br>`;
                    html += `Visible asterisk rows: ${visibleAsteriskRows}`;
                    html += `</div>`;
                }
                
                // Check exact match filter
                const exactMatchFilter = frameDoc.querySelector('input[type="checkbox"][id*="exactMatch"]');
                if (exactMatchFilter) {
                    html += `<div class="debug-section ${exactMatchFilter.checked ? 'warning' : 'success'}">`;
                    html += `<strong>Exact Match Filter:</strong> ${exactMatchFilter.checked ? 'ENABLED' : 'DISABLED'}<br>`;
                    if (exactMatchFilter.checked) {
                        html += `<button onclick="disableFilterInFrame()">Disable Filter</button>`;
                    }
                    html += `</div>`;
                }
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error analyzing frame: ${error.message}</div>`;
                console.error('Frame analysis error:', error);
            }
        }
        
        function disableFilterInFrame() {
            if (!searchFrame || !searchFrame.contentDocument) return;
            
            try {
                const frameDoc = searchFrame.contentDocument;
                const exactMatchFilter = frameDoc.querySelector('input[type="checkbox"][id*="exactMatch"]');
                
                if (exactMatchFilter) {
                    exactMatchFilter.checked = false;
                    
                    // Trigger change event
                    const event = new frameDoc.defaultView.Event('change');
                    exactMatchFilter.dispatchEvent(event);
                    
                    setTimeout(() => {
                        analyzeSearchFrame();
                    }, 1000);
                }
            } catch (error) {
                console.error('Error disabling filter:', error);
            }
        }
        
        function testExactMatchFilter() {
            const resultDiv = document.getElementById('filterResults');
            
            if (!searchFrame || !searchFrame.contentDocument) {
                resultDiv.innerHTML = '<div class="error">Search frame not available</div>';
                return;
            }
            
            try {
                const frameDoc = searchFrame.contentDocument;
                const frameWindow = frameDoc.defaultView;
                
                // Check if the filter functions exist
                const hasToggleFunction = typeof frameWindow.toggleExactMatchFilter === 'function';
                const hasApplyFunction = typeof frameWindow.applyExactMatchFilter === 'function';
                
                let html = '<h3>Filter Function Test:</h3>';
                html += `<p><strong>toggleExactMatchFilter function:</strong> ${hasToggleFunction ? 'EXISTS' : 'MISSING'}</p>`;
                html += `<p><strong>applyExactMatchFilter function:</strong> ${hasApplyFunction ? 'EXISTS' : 'MISSING'}</p>`;
                
                // Test sessionStorage
                const filterState = frameWindow.sessionStorage.getItem('exactMatchFilter');
                html += `<p><strong>SessionStorage filter state:</strong> ${filterState || 'not set'}</p>`;
                
                // Test manual filter application
                if (hasApplyFunction) {
                    html += '<button onclick="testManualFilter()">Test Manual Filter</button>';
                }
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error testing filter: ${error.message}</div>`;
            }
        }
        
        function testManualFilter() {
            if (!searchFrame || !searchFrame.contentDocument) return;
            
            try {
                const frameDoc = searchFrame.contentDocument;
                const frameWindow = frameDoc.defaultView;
                
                console.log('Testing manual filter application...');
                
                // Call the filter function directly
                if (typeof frameWindow.applyExactMatchFilter === 'function') {
                    const result = frameWindow.applyExactMatchFilter();
                    console.log('Manual filter result:', result);
                    
                    setTimeout(() => {
                        analyzeSearchFrame();
                    }, 1000);
                }
                
            } catch (error) {
                console.error('Manual filter test error:', error);
            }
        }
        
        function captureConsoleLogs() {
            const resultDiv = document.getElementById('consoleLogs');
            
            if (!searchFrame || !searchFrame.contentDocument) {
                resultDiv.innerHTML = '<div class="error">Search frame not available</div>';
                return;
            }
            
            try {
                const frameWindow = searchFrame.contentDocument.defaultView;
                
                // Override console.log in the frame to capture messages
                const originalLog = frameWindow.console.log;
                const originalError = frameWindow.console.error;
                const logs = [];
                
                frameWindow.console.log = function(...args) {
                    logs.push({type: 'log', message: args.join(' '), timestamp: new Date().toISOString()});
                    originalLog.apply(frameWindow.console, args);
                };
                
                frameWindow.console.error = function(...args) {
                    logs.push({type: 'error', message: args.join(' '), timestamp: new Date().toISOString()});
                    originalError.apply(frameWindow.console, args);
                };
                
                // Display captured logs
                setTimeout(() => {
                    let html = '<h3>Captured Console Logs:</h3>';
                    
                    if (logs.length > 0) {
                        html += '<pre>';
                        logs.forEach(log => {
                            html += `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}\n`;
                        });
                        html += '</pre>';
                    } else {
                        html += '<p>No console logs captured yet. Try performing actions in the search frame.</p>';
                    }
                    
                    resultDiv.innerHTML = html;
                }, 2000);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error capturing logs: ${error.message}</div>`;
            }
        }
        
        // Auto-analyze when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded');
        });
    </script>
</body>
</html>
