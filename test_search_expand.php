<?php
/**
 * Test pentru funcționalitatea de căutare și expandare/restrângere
 * Simulează o căutare cu mai mulți termeni pentru a testa butoanele
 */

// Simulăm o căutare cu mai mulți termeni
$_POST['searchTerms'] = "test\nSaragea\nTudorita";
$_POST['searchType'] = 'auto';

// Include funcționalitatea de căutare
include_once 'includes/search_functions.php';
include_once 'includes/soap_client.php';

// Simulăm rezultate pentru test
$searchResults = [
    'test' => [
        [
            'numar_dosar' => '1234/1/2024',
            'instanta' => 'Judecătoria Test',
            'obiect' => 'Test obiect 1',
            'stadiu' => 'În curs',
            'data_ultimei_sedinte' => '15.01.2024'
        ],
        [
            'numar_dosar' => '5678/2/2024',
            'instanta' => 'Tribunalul Test',
            'obiect' => 'Test obiect 2',
            'stadiu' => 'Finalizat',
            'data_ultimei_sedinte' => '20.01.2024'
        ]
    ],
    'Saragea' => [
        [
            'numar_dosar' => '9999/3/2024',
            'instanta' => 'Curtea de Apel Test',
            'obiect' => 'Dosar cu Saragea',
            'stadiu' => 'În curs',
            'data_ultimei_sedinte' => '25.01.2024'
        ]
    ],
    'Tudorita' => [
        [
            'numar_dosar' => '1111/4/2024',
            'instanta' => 'Judecătoria Test 2',
            'obiect' => 'Dosar cu Tudorita',
            'stadiu' => 'Suspendat',
            'data_ultimei_sedinte' => '30.01.2024'
        ]
    ]
];

$totalResults = 4;
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Căutare și Expandare - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .term-header {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .term-header:hover {
            background: #e9ecef;
            border-color: #007bff;
        }
        
        .term-content {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .toggle-icon {
            transition: transform 0.3s ease;
            color: #007bff;
        }
        
        .results-summary {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-search me-2"></i>
            Test Căutare și Funcționalitate Expandare
        </h1>
        
        <!-- Results Summary -->
        <div class="results-summary">
            <h5><i class="fas fa-chart-bar me-2"></i>Sumar Rezultate</h5>
            <p class="mb-2">
                <strong>Termeni căutați:</strong> test, Saragea, Tudorita<br>
                <strong>Total rezultate:</strong> <?php echo $totalResults; ?> dosare găsite
            </p>
        </div>
        
        <!-- Control Buttons -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Rezultate detaliate (<?php echo count($searchResults); ?> secțiuni)
            </h5>
            <div>
                <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="expandAllResults()">
                    <i class="fas fa-expand-alt me-1"></i>
                    Expandează toate
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllResults()">
                    <i class="fas fa-compress-alt me-1"></i>
                    Restrânge toate
                </button>
            </div>
        </div>
        
        <!-- Search Results -->
        <?php $termIndex = 0; ?>
        <?php foreach ($searchResults as $term => $results): ?>
        <div class="term-results">
            <div class="term-header" onclick="toggleTermResults(<?php echo $termIndex; ?>)">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">
                            <i class="fas fa-search me-2"></i>
                            Rezultate pentru: <strong><?php echo htmlspecialchars($term); ?></strong>
                        </h6>
                        <small class="text-muted">
                            <i class="fas fa-file-alt me-1"></i>
                            <?php echo count($results); ?> dosare găsite
                        </small>
                    </div>
                    <div>
                        <i class="fas fa-chevron-down toggle-icon" id="toggleIcon<?php echo $termIndex; ?>"></i>
                    </div>
                </div>
            </div>
            
            <div class="term-content" id="termContent<?php echo $termIndex; ?>" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="fas fa-hashtag me-1"></i>Nr. Dosar</th>
                                <th><i class="fas fa-university me-1"></i>Instanța</th>
                                <th><i class="fas fa-file-text me-1"></i>Obiect</th>
                                <th><i class="fas fa-info-circle me-1"></i>Stadiu</th>
                                <th><i class="fas fa-calendar me-1"></i>Data</th>
                                <th><i class="fas fa-cogs me-1"></i>Acțiuni</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $result): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($result['numar_dosar']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($result['instanta']); ?></td>
                                <td><?php echo htmlspecialchars($result['obiect']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $result['stadiu'] === 'În curs' ? 'primary' : ($result['stadiu'] === 'Finalizat' ? 'success' : 'warning'); ?>">
                                        <?php echo htmlspecialchars($result['stadiu']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($result['data_ultimei_sedinte']); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-info" title="Vezi detalii">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php $termIndex++; ?>
        <?php endforeach; ?>
        
        <!-- Test Instructions -->
        <div class="alert alert-info mt-4">
            <h5><i class="fas fa-info-circle me-2"></i>Instrucțiuni Test</h5>
            <ol>
                <li>Testați butoanele "Expandează toate" și "Restrânge toate" de mai sus</li>
                <li>Testați click-ul pe fiecare secțiune pentru expandare/restrângere individuală</li>
                <li>Verificați consola browser-ului (F12) pentru mesaje de debug</li>
                <li>Verificați dacă iconițele se schimbă corect (sus/jos)</li>
            </ol>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Notification system
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            
            const typeClasses = {
                'success': 'alert-success',
                'danger': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            };
            
            const icons = {
                'success': 'fa-check-circle',
                'danger': 'fa-exclamation-triangle',
                'warning': 'fa-exclamation-circle',
                'info': 'fa-info-circle'
            };
            
            notification.className = `alert ${typeClasses[type]} alert-dismissible fade show`;
            notification.innerHTML = `
                <i class="fas ${icons[type]} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            container.appendChild(notification);
            console.log(`Notification: ${message}`);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Toggle individual term results - GLOBAL FUNCTION
        function toggleTermResults(index) {
            try {
                const content = document.getElementById('termContent' + index);
                const icon = document.getElementById('toggleIcon' + index);

                console.log('Toggling term results for index:', index);
                console.log('Content element:', content);
                console.log('Icon element:', icon);

                if (!content) {
                    console.error('Content element not found for index:', index);
                    showNotification('Eroare: Secțiunea nu a fost găsită.', 'danger');
                    return;
                }

                if (!icon) {
                    console.warn('Icon element not found for index:', index);
                }

                const isVisible = content.style.display !== 'none';

                if (isVisible) {
                    content.style.display = 'none';
                    if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                    console.log('Collapsed section', index);
                } else {
                    content.style.display = 'block';
                    if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                    console.log('Expanded section', index);
                }

            } catch (error) {
                console.error('Error in toggleTermResults:', error);
                showNotification('Eroare la comutarea secțiunii.', 'danger');
            }
        }

        // Expand all results function - GLOBAL FUNCTION
        function expandAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Expanding all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru expandare.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'block';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-up toggle-icon';
                });

                showNotification('Toate secțiunile au fost expandate.', 'info');
                console.log('Successfully expanded', termContents.length, 'sections');

            } catch (error) {
                console.error('Error in expandAllResults:', error);
                showNotification('Eroare la expandarea rezultatelor.', 'danger');
            }
        }

        // Collapse all results function - GLOBAL FUNCTION
        function collapseAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Collapsing all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru restrângere.', 'warning');
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'none';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-down toggle-icon';
                });

                showNotification('Toate secțiunile au fost restrânse.', 'info');
                console.log('Successfully collapsed', termContents.length, 'sections');

            } catch (error) {
                console.error('Error in collapseAllResults:', error);
                showNotification('Eroare la restrângerea rezultatelor.', 'danger');
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, functions ready for testing');
            showNotification('Pagina de test încărcată cu succes! Testați butoanele de expandare/restrângere.', 'success');
            
            // Test function accessibility
            console.log('Function accessibility test:');
            console.log('- expandAllResults:', typeof expandAllResults);
            console.log('- collapseAllResults:', typeof collapseAllResults);
            console.log('- toggleTermResults:', typeof toggleTermResults);
        });
    </script>
</body>
</html>
